package facility_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type ShiftTimeController struct {
	v1.CommonController
}

func NewShiftTimeController() ShiftTimeController {
	return ShiftTimeController{}
}

// @Tags Shift Time
// @Summary 獲取機構的班次時間列表
// @Description 獲取機構的班次時間列表，包含開始時間和兩個結束時間
// @Router /v1/facility/shift-times [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.ShiftTimeListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Success 200 {object} []services.ShiftTimeListResp "Success"
func (con ShiftTimeController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ShiftTimeListReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.ShiftTimeService.List(db, req, &pageSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Shift Time
// @Summary 批量更新機構的班次時間
// @Description 批量更新機構的班次時間，支援開始時間和兩個結束時間，每個結束時間可設定是否為次日
// @Router /v1/facility/shift-times/actions/update [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ShiftTimeUpdateReq true "parameter"
// @Success 200 "Success"
func (con ShiftTimeController) Update(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ShiftTimeUpdateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.ShiftTimeService.CheckIdsExist(db, req.FacilityId, req.Items)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.ShiftTimeService.CheckTimeFormat(req.Items)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.ShiftTimeService.Update(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)

	} else {
		nc.BadRequestResponse(err)
	}
}
