package system_api

import (
	"mime/multipart"

	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type FaqFileController struct {
	v1.CommonController
}

func NewFaqFileController() FaqFileController {
	return FaqFileController{}
}

// @Tags Faq File
// @Summary 上載常見問題圖片
// @Description
// @Router /v1/system/faq-files/actions/upload [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FaqFileUploadReq true "parameter"
// @Param file formData file true "文件"
// @Success 200 {object} services.FaqFileUploadResp "Success"
func (con FaqFileController) Upload(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FaqFileUploadReq
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FaqFileService.CheckFileCodeExist(req.FileCode)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		var formFile *multipart.FileHeader
		formFile, err = c.FormFile("file")
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		req.File = formFile

		tx := db.Begin()
		resp, err := services.FaqFileService.Upload(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Faq File
// @Summary 獲取常見問題文件圖片預覽URL
// @Description 獲取常見問題文件的圖片預覽URL
// @Router /v1/system/faq-files/actions/preview-url [GET]
// @Security ApiKeyAuth
// @Param json query services.FaqFilePreviewUrlReq true "parameter"
// @Success 200 {object} services.FaqFilePreviewUrlResp "Success"
func (con FaqFileController) Preview(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FaqFilePreviewUrlReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FaqFileService.CheckUuidExist(db, &model.FaqFile{}, req.FaqFileUuid)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		resp, err := services.FaqFileService.GenPreviewUrlByUuid(db, req.FaqFileUuid)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
