package services

import (
	"context"
	"errors"
	"fmt"
	"mime/multipart"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xredis"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

type uploadFileByQrcodeService struct{}

var UploadFileByQrcodeService = uploadFileByQrcodeService{}

type GenUploadFileQrcodeReq struct {
	ReqUserId uint64 `json:"-"`                           // 請求用戶ID
	FileCode  string `json:"fileCode" binding:"required"` // 文件編碼
}

type GenUploadFileQrcodeResp struct {
	Uuid       string    `json:"uuid"`       // uuid
	ExpireTime time.Time `json:"expireTime"` // uuid的过期时间
}

type UploadFileByQrcodeCache struct {
	ReqUserId          uint64 `json:"reqUserId"`          // 請求用戶ID
	UserType           string `json:"userType"`           // 用戶類型
	FileCode           string `json:"fileCode"`           // 文件編碼
	ProfessionalId     uint64 `json:"professionalId"`     // 專業人員ID
	FacilityId         uint64 `json:"facilityId"`         // 機構ID
	FacilityFileId     uint64 `json:"facilityFileId"`     // 機構文件ID
	ProfessionalFileId uint64 `json:"professionalFileId"` // 專業人員文件ID
}

const (
	cacheKeyUploadFileByQrcode = "cache:upload_file_qrcode:%s" // 上传文件二维码缓存
)

func (s *uploadFileByQrcodeService) GenQrcodeByFileCode(db *gorm.DB, req GenUploadFileQrcodeReq) (GenUploadFileQrcodeResp, error) {
	var user xmodel.User
	var err error
	var resp GenUploadFileQrcodeResp
	if err = db.Where("id = ?", req.ReqUserId).First(&user).Error; err != nil {
		return resp, err
	}

	var cache UploadFileByQrcodeCache
	qrcodeUuid := uuid.NewV4().String() + uuid.NewV4().String()
	cacheKey := fmt.Sprintf(cacheKeyUploadFileByQrcode, qrcodeUuid)
	if user.UserType == model.UserUserTypeFacilityUser && req.FileCode == model.FacilityFileCodeSignedAgreement {
		var facilityUser model.FacilityUser
		if err = db.Where("user_id = ?", req.ReqUserId).First(&facilityUser).Error; err != nil {
			return resp, err
		}
		cache = UploadFileByQrcodeCache{
			ReqUserId:  req.ReqUserId,
			UserType:   model.UserUserTypeFacilityUser,
			FileCode:   req.FileCode,
			FacilityId: facilityUser.FacilityId,
		}
	} else if user.UserType == model.UserUserTypeProfessional && req.FileCode == model.ProfessionalFileCodeSignedAgreement {
		var professional model.Professional
		if err = db.Where("user_id = ?", req.ReqUserId).Where("data_type = ?", model.ProfessionalDataTypeDraft).First(&professional).Error; err != nil {
			return resp, err
		}
		cache = UploadFileByQrcodeCache{
			ReqUserId:      req.ReqUserId,
			UserType:       model.UserUserTypeProfessional,
			FileCode:       req.FileCode,
			ProfessionalId: professional.Id,
		}
	} else {
		return resp, errors.New("request data incorrect")
	}
	err = xredis.SetStruct(db.Statement.Context, cacheKey, cache, time.Minute*10)
	if err != nil {
		return resp, err
	}
	resp.ExpireTime = time.Now().UTC().Add(time.Minute * 10)
	resp.Uuid = qrcodeUuid
	return resp, nil
}

type ExchangeByQrcodeReq struct {
	Uuid string                `form:"uuid" binding:"required"` // 文件編碼
	File *multipart.FileHeader `form:"-" swaggerignore:"true"`
}

type ExchangeByQrcodeResp struct {
	Status string `json:"status"` // 狀態, SUCCESS=成功, NOT_FOUND=未找到
}

func (s *uploadFileByQrcodeService) ExchangeByQrcode(db *gorm.DB, req ExchangeByQrcodeReq) (ExchangeByQrcodeResp, error) {
	var cache UploadFileByQrcodeCache
	cacheKey := fmt.Sprintf(cacheKeyUploadFileByQrcode, req.Uuid)
	exist, err := xredis.GetStruct(db.Statement.Context, cacheKey, &cache)
	if err != nil {
		return ExchangeByQrcodeResp{}, err
	}
	if exist == false || cache.ProfessionalFileId != 0 || cache.FacilityFileId != 0 {
		return ExchangeByQrcodeResp{Status: "NOT_FOUND"}, nil
	}
	switch cache.UserType {
	case model.UserUserTypeProfessional:
		resp, err := ProfessionalFileService.UploadFile(db, ProfessionalFileUploadFileReq{
			FileCode:       cache.FileCode,
			ProfessionalId: cache.ProfessionalId,
			File:           req.File,
		}, cache.ReqUserId)
		if err != nil {
			return ExchangeByQrcodeResp{}, err
		}
		cache.ProfessionalFileId = resp.ProfessionalFileId
		err = xredis.SetStruct(db.Statement.Context, cacheKey, cache, time.Minute*1) // 重新設置1分鐘來允許兌換
		if err != nil {
			return ExchangeByQrcodeResp{}, err
		}
		return ExchangeByQrcodeResp{Status: "SUCCESS"}, nil
	case model.UserUserTypeFacilityUser:
		resp, err := FacilityFileService.Upload(db, FacilityFileUploadReq{
			FacilityId: cache.FacilityId,
			FileCode:   cache.FileCode,
			File:       req.File,
		})
		if err != nil {
			return ExchangeByQrcodeResp{}, err
		}
		cache.FacilityFileId = resp.FacilityFileId
		err = xredis.SetStruct(db.Statement.Context, cacheKey, cache, time.Minute*1) // 重新設置1分鐘來允許兌換
		if err != nil {
			return ExchangeByQrcodeResp{}, err
		}
		return ExchangeByQrcodeResp{Status: "SUCCESS"}, nil
	}
	return ExchangeByQrcodeResp{}, errors.New("user type not support")
}

type InquireFileIdByQrcodeReq struct {
	ReqUserId uint64 `form:"-"`                       // 請求用戶ID
	Uuid      string `form:"uuid" binding:"required"` // 文件編碼
}

func (s *uploadFileByQrcodeService) InquireByQrcode(ctx context.Context, req InquireFileIdByQrcodeReq) (interface{}, error) {
	var cache UploadFileByQrcodeCache
	cacheKey := fmt.Sprintf(cacheKeyUploadFileByQrcode, req.Uuid)
	exist, err := xredis.GetStruct(ctx, cacheKey, &cache)
	if err != nil {
		return nil, err
	}
	if exist == false || req.ReqUserId != cache.ReqUserId {
		// 不存在 或者不是這個 user 發起的
		return nil, nil
	}
	switch cache.UserType {
	case model.UserUserTypeProfessional:
		return ProfessionalFileUploadFileResp{
			ProfessionalFileId: cache.ProfessionalFileId,
		}, nil
	case model.UserUserTypeFacilityUser:
		return FacilityFileUploadResp{
			FacilityFileId: cache.FacilityFileId,
		}, nil
	}
	return nil, errors.New("user type not support")
}
