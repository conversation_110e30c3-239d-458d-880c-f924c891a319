package services

import (
	"encoding/base64"
	"fmt"
	"os"
	"regexp"
	"strings"
	"time"

	"github.com/Norray/xrocket/xmodel/xtype"
	"github.com/Norray/xrocket/xs3"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xhermes"
	"github.com/Norray/xrocket/xi18n"
	"github.com/Norray/xrocket/xmail"
	"github.com/Norray/xrocket/xtool"
	"github.com/jinzhu/copier"
	"github.com/matcornic/hermes/v2"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

var FacilityAgreementService = new(facilityAgreementService)

type facilityAgreementService struct{}

// region ---------------------------------------------------- Checker ----------------------------------------------------

func (s *facilityAgreementService) CheckIdExist(db *gorm.DB, m *model.FacilityAgreement, id uint64, facility ...bool) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_agreement.id.does_not_exist",
		Other: "No such record, please try after reloading.",
	}
	var err error
	builder := db.First(&m, id)
	if len(facility) > 0 && facility[0] {
		// 機構查詢時，必須已經發送
		builder = builder.Where("status != ?", model.FacilityAgreementStatusUnsent)
	}
	if err = builder.Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

// 檢查協議是否可以修改（僅UNSENT和UNSIGNED狀態可修改）
func (s *facilityAgreementService) CheckCanModify(db *gorm.DB, id uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_agreement.cannot_modify",
		Other: "This agreement cannot be modified in current status.",
	}

	var agreement model.FacilityAgreement
	var err error
	if err = db.First(&agreement, id).Error; err != nil {
		if xgorm.IsNotFoundErr(err) {
			return false, i18n.Message{
				ID:    "checker.facility_agreement.id.does_not_exist",
				Other: "No such record, please try after reloading.",
			}, nil
		}
		return false, msg, err
	}

	// 只有UNSENT和UNSIGNED狀態可以修改
	if agreement.Status == model.FacilityAgreementStatusUnsent ||
		agreement.Status == model.FacilityAgreementStatusUnsigned {
		return true, msg, nil
	}

	return false, msg, nil
}

// 檢查是否可以發送協議電郵
func (s *facilityAgreementService) CheckCanSendAgreementEmail(db *gorm.DB, id uint64) (bool, i18n.Message, error) {
	var err error
	msg := i18n.Message{
		ID:    "checker.facility_agreement.cannot_send_agreement_email",
		Other: "This agreement cannot be sent to the facility.",
	}

	var agreement model.FacilityAgreement
	if err = db.First(&agreement, id).Error; err != nil {
		return false, msg, err
	}

	if agreement.Status != model.FacilityAgreementStatusUnsigned &&
		agreement.Status != model.FacilityAgreementStatusUnsent {
		return false, msg, nil
	}

	return true, msg, nil
}

// 檢查協議時間是否有效（BeginTime必須早於EndTime）, 並且不與其他協議時間重疊
func (s *facilityAgreementService) CheckTimeValid(db *gorm.DB, facilityId uint64, beginTime, endTime string, facilityAgreementId ...uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_agreement.invalid_time_range",
		Other: "Begin time must be earlier than end time.",
	}

	if beginTime > endTime {
		return false, msg, nil
	}

	// 檢查是否存在時間段內的協議
	builder := db.Table("facility_agreement").
		Select("id").
		Where("facility_id = ?", facilityId).
		Where("begin_time <= ?", endTime).
		Where("end_time >= ?", beginTime)
	if len(facilityAgreementId) > 0 {
		builder = builder.Where("id != ?", facilityAgreementId[0])
	}

	var err error
	if err = builder.First(&model.FacilityAgreement{}).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return true, msg, nil
	}

	return false, i18n.Message{
		ID:    "checker.facility_agreement.time_range_overlap",
		Other: "The validity period of the agreement overlaps with that of an existing active agreement.",
	}, nil
}

// 檢查機構是否存在未簽署的協議
func (s *facilityAgreementService) CheckFacilityHasUnsignedAgreement(db *gorm.DB, facilityId uint64, facilityAgreementIds []uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_agreement.has_unsigned_agreement",
		Other: "Please sign all pending agreements before proceeding.",
	}
	var err error
	var unsignedAgreementIds []uint64
	if err = db.Table("facility_agreement").
		Where("facility_id = ?", facilityId).
		Where("status in ?", []string{model.FacilityAgreementStatusReviewing, model.FacilityAgreementStatusUnsigned}).
		Pluck("id", &unsignedAgreementIds).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}

	var facilityAgreementIdsMap = make(map[uint64]bool)
	for _, v := range facilityAgreementIds {
		facilityAgreementIdsMap[v] = true
	}

	// 除了facilityAgreementIds之外，其他未簽署的協議
	for _, v := range unsignedAgreementIds {
		if _, ok := facilityAgreementIdsMap[v]; !ok {
			return false, msg, nil
		}
	}

	return true, msg, nil
}

// 檢查工作時間範圍是否在協議時間範圍內
func (s *facilityAgreementService) CheckJobTimeInRange(db *gorm.DB, facilityId uint64, jobBegin, jobEnd string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_agreement.no_valid_agreement",
		Other: "No valid agreement for the job time period.",
	}

	beginTime, err := time.ParseInLocation(xtool.DateDayA, jobBegin, time.UTC)
	if err != nil {
		return false, msg, err
	}
	endTime, err := time.ParseInLocation(xtool.DateDayA, jobEnd, time.UTC)
	if err != nil {
		return false, msg, err
	}
	beginDate := beginTime.Format(xtool.DateDayA)
	endDate := endTime.Format(xtool.DateDayA)

	var agreement model.FacilityAgreement
	if err = db.Where("facility_id = ?", facilityId).
		Where("begin_time <= ?", endDate).
		Where("end_time >= ?", beginDate).
		Where("status = ?", model.FacilityAgreementStatusSigned).
		First(&agreement).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	}

	return true, msg, nil
}

// 檢查工作計劃時間段是否在協議時間範圍內
func (s *facilityAgreementService) CheckScheduleTimeInRange(agreementBegin, agreementEnd string, scheduleBegin, scheduleEnd time.Time) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_agreement.schedule_time_out_of_range",
		Other: "Schedule time must be within agreement time range.",
	}

	scheduleBeginDate := scheduleBegin.Format(xtool.DateDayA)
	scheduleEndDate := scheduleEnd.Format(xtool.DateDayA)

	if scheduleBeginDate < agreementBegin || scheduleEndDate > agreementEnd {
		return false, msg, nil
	}

	return true, msg, nil
}

// 檢查是否可以簽署協議
func (s *facilityAgreementService) CheckCanSignAgreement(db *gorm.DB, facilityAgreementId uint64) (bool, i18n.Message, error) {
	var err error
	msg := i18n.Message{
		ID:    "checker.facility_agreement.cannot_sign_agreement",
		Other: "This agreement cannot be signed.",
	}

	var facilityAgreement model.FacilityAgreement
	if err = db.First(&facilityAgreement, facilityAgreementId).Error; err != nil {
		return false, msg, err
	}

	if facilityAgreement.Status != model.FacilityAgreementStatusUnsigned && facilityAgreement.Status != model.FacilityAgreementStatusReviewing {
		return false, msg, nil
	}

	return true, msg, nil
}

// endregion ---------------------------------------------------- Checker ----------------------------------------------------

// region ---------------------------------------------------- 機構協議列表 ----------------------------------------------------

type FacilityAgreementListReq struct {
	FacilityId     uint64          `form:"facilityId"`                                                                                 // 機構ID
	FacilityName   string          `form:"facilityName"`                                                                               // 機構名稱
	CommissionRate decimal.Decimal `form:"commissionRate"`                                                                             // 佣金率(預付款佣金率或事後支付佣金率)
	Status         string          `form:"status" binding:"omitempty,oneof=UNSENT UNSIGNED REVIEWING PENDING ACTIVE EXPIRING EXPIRED"` // 狀態 UNSENT UNSIGNED REVIEWING PENDING ACTIVE EXPIRING EXPIRED
}

type FacilityAgreementListResp struct {
	FacilityAgreementId         uint64          `json:"facilityAgreementId"`
	FacilityId                  uint64          `json:"facilityId"`
	FacilityName                string          `json:"facilityName"`
	BeginTime                   xtype.Date      `swaggertype:"string" json:"beginTime"`
	EndTime                     xtype.Date      `swaggertype:"string" json:"endTime"`
	PayUpfrontCommissionId      uint64          `json:"payUpfrontCommissionId"`
	PayUpfrontCommissionRate    decimal.Decimal `json:"payUpfrontCommissionRate"`
	PayUpfrontCommissionLevel   string          `json:"payUpfrontCommissionLevel"`
	PayInArrearsCommissionId    uint64          `json:"payInArrearsCommissionId"`
	PayInArrearsCommissionRate  decimal.Decimal `json:"payInArrearsCommissionRate"`
	PayInArrearsCommissionLevel string          `json:"payInArrearsCommissionLevel"`
	Status                      string          `json:"status"`
}

func (s *facilityAgreementService) List(db *gorm.DB, req FacilityAgreementListReq, pageSet *xresp.PageSet) ([]FacilityAgreementListResp, error) {
	var err error
	var resp []FacilityAgreementListResp
	builder := db.Table("facility_agreement AS fa").
		Joins("JOIN facility AS f ON f.id = fa.facility_id").
		Joins("JOIN facility_profile AS fp ON fp.facility_id = f.id AND fp.data_type = ?", model.FacilityProfileDataTypeDraft).
		Joins("LEFT JOIN commission AS fuc ON fuc.id = fa.pay_upfront_commission_id").
		Joins("LEFT JOIN commission AS fic ON fic.id = fa.pay_in_arrears_commission_id").
		Select([]string{
			"fa.id AS facility_agreement_id",
			"fa.facility_id",
			"fp.name AS facility_name",
			"fa.begin_time",
			"fa.end_time",
			"fa.pay_upfront_commission_id",
			"fuc.commission_rate AS pay_upfront_commission_rate",
			"fuc.level AS pay_upfront_commission_level",
			"fa.pay_in_arrears_commission_id",
			"fic.commission_rate AS pay_in_arrears_commission_rate",
			"fic.level AS pay_in_arrears_commission_level",
			"fa.content",
			"fa.status",
			"fa.update_time",
		})
	if req.FacilityId != 0 {
		builder = builder.Where("fa.facility_id = ?", req.FacilityId)
	}
	if req.FacilityName != "" {
		builder = builder.
			Where("fp.name LIKE ?", "%"+req.FacilityName+"%")
	}
	if !req.CommissionRate.IsZero() {
		builder = builder.Where("fuc.commission_rate = ? OR fic.commission_rate = ?", req.CommissionRate, req.CommissionRate)
	}
	if req.Status != "" {
		nowTime := time.Now().UTC()
		nowStr := nowTime.Format(xtool.DateDayA)
		// 30 天後
		thirtyDaysLater := nowTime.AddDate(0, 0, 30).Format(xtool.DateDayA)
		switch req.Status {
		case model.FacilityAgreementStatusUnsent, model.FacilityAgreementStatusUnsigned, model.FacilityAgreementStatusReviewing: // 未發送、未簽署或審核中
			builder = builder.Where("fa.status = ?", req.Status)
		case model.FacilityAgreementStatusPending: // 待生效
			// 已簽署且生效時間在當前時間之後
			builder = builder.Where("fa.status = ?", model.FacilityAgreementStatusSigned).
				Where("fa.begin_time > ?", nowStr)
		case model.FacilityAgreementStatusActive: // 生效中
			// 已簽署且生效時間在當前時間之前或等於當前時間，結束時間在當前時間之後，且結束時間距離當前時間超過30天
			builder = builder.Where("fa.status = ?", model.FacilityAgreementStatusSigned).
				Where("fa.begin_time <= ?", nowStr).
				Where("fa.end_time > ?", thirtyDaysLater)
		case model.FacilityAgreementStatusExpiring: // 即將到期
			// 已簽署且生效時間在當前時間之前或等於當前時間，結束時間在當前時間之後且在30天內（含30天）
			builder = builder.Where("fa.status = ?", model.FacilityAgreementStatusSigned).
				Where("fa.begin_time <= ?", nowStr).
				Where("fa.end_time <= ?", thirtyDaysLater).
				Where("fa.end_time >= ?", nowStr)
		case model.FacilityAgreementStatusExpired: // 已過期
			// 已簽署且結束時間在當前時間之前
			builder = builder.Where("fa.status = ?", model.FacilityAgreementStatusSigned).
				Where("fa.end_time < ?", nowStr)
		}
	}
	if err = builder.
		Order("fa.begin_time DESC").
		Order("fa.id DESC").
		Scopes(xresp.Paginate(pageSet)).
		Find(&resp).Error; err != nil {
		return resp, err
	}
	for i, v := range resp {
		resp[i].Status = s.GetFacilityAgreementStatus(v.BeginTime.String(), v.EndTime.String(), v.Status)
	}
	return resp, nil
}

func (s *facilityAgreementService) GetFacilityAgreementStatus(beginTime, endTime string, status string) string {

	// 如果協議狀態是 UNSENT 或 UNSIGNED，直接返回當前狀態
	if status == model.FacilityAgreementStatusUnsent ||
		status == model.FacilityAgreementStatusUnsigned ||
		status == model.FacilityAgreementStatusReviewing {
		return status
	}

	// 如果協議已簽署，根據時間判斷狀態
	if status == model.FacilityAgreementStatusSigned {
		nowTime := time.Now().UTC()
		today := nowTime.Format(xtool.DateDayA)

		// 如果當前時間在開始時間之前，則狀態為待生效
		if today < beginTime {
			return model.FacilityAgreementStatusPending
		}

		// 如果當前時間在結束時間之後，則狀態為已過期
		if today > endTime {
			return model.FacilityAgreementStatusExpired
		}

		// 計算30天後的日期
		thirtyDaysLater := nowTime.AddDate(0, 0, 30).Format(xtool.DateDayA)

		// 如果結束時間等於當前時間或在30天內，則狀態為即將到期
		if endTime <= thirtyDaysLater && today <= endTime {
			return model.FacilityAgreementStatusExpiring
		}

		// 其他情況為生效中
		return model.FacilityAgreementStatusActive
	}

	// 返回當前狀態
	return status
}

// endregion ---------------------------------------------------- 機構協議列表 ----------------------------------------------------

// region ---------------------------------------------------- 機構端-協議列表 ----------------------------------------------------

type FacilityAgreementListReqByFacility struct {
	FacilityId uint64 `form:"facilityId" binding:"required"`                                                       // 機構ID
	Status     string `form:"status" binding:"omitempty,oneof=UNSIGNED REVIEWING PENDING ACTIVE EXPIRING EXPIRED"` // 狀態 UNSIGNED REVIEWING PENDING ACTIVE EXPIRING EXPIRED
}

type FacilityAgreementListRespByFacility struct {
	FacilityAgreementId        uint64          `json:"facilityAgreementId"`
	FacilityId                 uint64          `json:"facilityId"`
	FacilityName               string          `json:"-"`
	BeginTime                  xtype.Date      `swaggertype:"string" json:"beginTime"`
	EndTime                    xtype.Date      `swaggertype:"string" json:"endTime"`
	PayUpfrontCommissionRate   decimal.Decimal `json:"payUpfrontCommissionRate"`
	PayInArrearsCommissionRate decimal.Decimal `json:"payInArrearsCommissionRate"`
	Status                     string          `json:"status"`
	Name                       string          `json:"name" gorm:"-"`
}

func (s *facilityAgreementService) GenerateAgreementName(facilityName string, endTimeStr xtype.Date) string {
	endTime, _ := time.ParseInLocation(xtool.DateDayA, endTimeStr.String(), time.UTC)
	return fmt.Sprintf("%s_%s", facilityName, endTime.Format("02012006"))
}

func (s *facilityAgreementService) ListByFacility(db *gorm.DB, req FacilityAgreementListReqByFacility, pageSet *xresp.PageSet) ([]FacilityAgreementListRespByFacility, error) {
	var err error
	var resp []FacilityAgreementListRespByFacility
	builder := db.Table("facility_agreement AS fa").
		Joins("JOIN facility AS f ON f.id = fa.facility_id").
		Joins("JOIN facility_profile AS fp ON fp.facility_id = f.id AND fp.data_type = ?", model.FacilityProfileDataTypeDraft).
		Joins("LEFT JOIN commission AS fuc ON fuc.id = fa.pay_upfront_commission_id").
		Joins("LEFT JOIN commission AS fic ON fic.id = fa.pay_in_arrears_commission_id").
		Select([]string{
			"fa.id AS facility_agreement_id",
			"fa.facility_id",
			"fp.name AS facility_name",
			"fa.begin_time",
			"fa.end_time",
			"fuc.commission_rate AS pay_upfront_commission_rate",
			"fic.commission_rate AS pay_in_arrears_commission_rate",
			"fa.status",
		}).
		Where("fa.status <> ?", model.FacilityAgreementStatusUnsent).
		Where("fa.facility_id = ?", req.FacilityId)

	if req.Status != "" {
		nowTime := time.Now().UTC()
		nowStr := nowTime.Format(xtool.DateDayA)
		// 30 天後
		thirtyDaysLater := nowTime.AddDate(0, 0, 30).Format(xtool.DateDayA)
		switch req.Status {
		case model.FacilityAgreementStatusUnsent, model.FacilityAgreementStatusUnsigned, model.FacilityAgreementStatusReviewing: // 未發送、未簽署或審核中
			builder = builder.Where("fa.status = ?", req.Status)
		case model.FacilityAgreementStatusPending: // 待生效
			// 已簽署且生效時間在當前時間之後
			builder = builder.Where("fa.status = ?", model.FacilityAgreementStatusSigned).
				Where("fa.begin_time > ?", nowStr)
		case model.FacilityAgreementStatusActive: // 生效中
			// 已簽署且生效時間在當前時間之前或等於當前時間，結束時間在當前時間之後，且結束時間距離當前時間超過30天
			builder = builder.Where("fa.status = ?", model.FacilityAgreementStatusSigned).
				Where("fa.begin_time <= ?", nowStr).
				Where("fa.end_time > ?", thirtyDaysLater)
		case model.FacilityAgreementStatusExpiring: // 即將到期
			// 已簽署且生效時間在當前時間之前或等於當前時間，結束時間在當前時間之後且在30天內（含30天）
			builder = builder.Where("fa.status = ?", model.FacilityAgreementStatusSigned).
				Where("fa.begin_time <= ?", nowStr).
				Where("fa.end_time <= ?", thirtyDaysLater).
				Where("fa.end_time >= ?", nowStr)
		case model.FacilityAgreementStatusExpired: // 已過期
			// 已簽署且結束時間在當前時間之前
			builder = builder.Where("fa.status = ?", model.FacilityAgreementStatusSigned).
				Where("fa.end_time < ?", nowStr)
		}
	}
	if err = builder.
		Order("fa.begin_time DESC").
		Order("fa.id DESC").
		Scopes(xresp.Paginate(pageSet)).
		Find(&resp).Error; err != nil {
		return resp, err
	}
	for i, v := range resp {
		resp[i].Status = s.GetFacilityAgreementStatus(v.BeginTime.String(), v.EndTime.String(), v.Status)
		resp[i].Name = s.GenerateAgreementName(v.FacilityName, v.EndTime)
	}
	return resp, nil
}

// endregion ---------------------------------------------------- 機構端-協議列表 ----------------------------------------------------

// region ---------------------------------------------------- 查詢協議 ----------------------------------------------------
type FacilityAgreementInquireReq struct {
	FacilityAgreementId uint64 `form:"facilityAgreementId" binding:"required"`
}

type FacilityAgreementInquireResp struct {
	FacilityAgreementId         uint64          `json:"facilityAgreementId"`         // 機構協議ID
	FacilityId                  uint64          `json:"facilityId"`                  // 機構ID
	FacilityFileId              uint64          `json:"facilityFileId"`              // 機構簽名文件ID
	FacilityName                string          `json:"facilityName"`                // 機構名稱
	BeginTime                   string          `json:"beginTime"`                   // 生效時間
	EndTime                     string          `json:"endTime"`                     // 失效時間
	Content                     string          `json:"content"`                     // 內容
	Template                    string          `json:"template"`                    // 模板名稱
	Status                      string          `json:"status"`                      // 狀態
	PayUpfrontCommissionId      uint64          `json:"payUpfrontCommissionId"`      // 預付款佣金ID
	PayUpfrontCommissionRate    decimal.Decimal `json:"payUpfrontCommissionRate"`    // 預付款佣金率
	PayUpfrontCommissionLevel   string          `json:"payUpfrontCommissionLevel"`   // 預付款佣金等級
	PayInArrearsCommissionId    uint64          `json:"payInArrearsCommissionId"`    // 事後支付佣金ID
	PayInArrearsCommissionRate  decimal.Decimal `json:"payInArrearsCommissionRate"`  // 事後支付佣金率
	PayInArrearsCommissionLevel string          `json:"payInArrearsCommissionLevel"` // 事後支付佣金等級
}

func (s *facilityAgreementService) Inquire(db *gorm.DB, req FacilityAgreementInquireReq) (FacilityAgreementInquireResp, error) {
	var err error
	var resp FacilityAgreementInquireResp
	var m model.FacilityAgreement
	if err = db.First(&m, req.FacilityAgreementId).Error; err != nil {
		return resp, err
	}
	_ = copier.Copy(&resp, m)
	resp.FacilityAgreementId = m.Id

	// 查詢機構資料
	var facilityProfile model.FacilityProfile
	if err = db.Where("facility_id = ?", m.FacilityId).
		Where("data_type = ?", model.FacilityProfileDataTypeDraft).
		First(&facilityProfile).Error; err != nil {
		return resp, err
	}
	resp.FacilityName = facilityProfile.Name

	// 查詢預付款佣金
	var payUpfrontCommission model.Commission
	if err = db.First(&payUpfrontCommission, m.PayUpfrontCommissionId).Error; err != nil {
		return resp, err
	}
	resp.PayUpfrontCommissionRate = payUpfrontCommission.CommissionRate
	resp.PayUpfrontCommissionLevel = payUpfrontCommission.Level

	// 查詢事後支付佣金
	var payInArrearsCommission model.Commission
	if err = db.First(&payInArrearsCommission, m.PayInArrearsCommissionId).Error; err != nil {
		return resp, err
	}
	resp.PayInArrearsCommissionRate = payInArrearsCommission.CommissionRate
	resp.PayInArrearsCommissionLevel = payInArrearsCommission.Level
	// 轉換狀態
	resp.Status = s.GetFacilityAgreementStatus(m.BeginTime.String(), m.EndTime.String(), m.Status)
	return resp, nil
}

// endregion ---------------------------------------------------- 查詢協議 ----------------------------------------------------

// region ---------------------------------------------------- 創建協議 ----------------------------------------------------
type FacilityAgreementCreateReq struct {
	FacilityId               uint64 `json:"facilityId" binding:"required"`               // 機構ID
	BeginTime                string `json:"beginTime" binding:"required"`                // 生效時間
	EndTime                  string `json:"endTime" binding:"required"`                  // 失效時間
	PayUpfrontCommissionId   uint64 `json:"payUpfrontCommissionId" binding:"required"`   // 預付款佣金ID
	PayInArrearsCommissionId uint64 `json:"payInArrearsCommissionId" binding:"required"` // 事後支付佣金ID
	Content                  string `json:"content" binding:"required"`                  // 內容
	Template                 string `json:"template" binding:"omitempty,max=255"`        // 模板名稱
}

type FacilityAgreementCreateResp struct {
	FacilityAgreementId uint64 `json:"facilityAgreementId"`
}

func (s *facilityAgreementService) Create(db *gorm.DB, req FacilityAgreementCreateReq) (FacilityAgreementCreateResp, error) {
	var resp FacilityAgreementCreateResp
	var err error
	var m model.FacilityAgreement
	_ = copier.Copy(&m, req)
	m.Status = model.FacilityAgreementStatusUnsent

	if err = db.Create(&m).Error; err != nil {
		return resp, err
	}
	resp.FacilityAgreementId = m.Id
	return resp, nil
}

// endregion ---------------------------------------------------- 創建協議 ----------------------------------------------------

// region ---------------------------------------------------- 編輯協議 ----------------------------------------------------
type FacilityAgreementEditReq struct {
	FacilityAgreementId      uint64 `json:"facilityAgreementId"  binding:"required"`     // 機構協議ID
	BeginTime                string `json:"beginTime" binding:"required"`                // 生效時間
	EndTime                  string `json:"endTime" binding:"required"`                  // 失效時間
	PayUpfrontCommissionId   uint64 `json:"payUpfrontCommissionId" binding:"required"`   // 預付款佣金ID
	PayInArrearsCommissionId uint64 `json:"payInArrearsCommissionId" binding:"required"` // 事後支付佣金ID
	Content                  string `json:"content" binding:"required"`                  // 內容
	Template                 string `json:"template" binding:"omitempty,max=255"`        // 模板名稱
}

func (s *facilityAgreementService) Edit(db *gorm.DB, req FacilityAgreementEditReq) error {
	var err error
	var m model.FacilityAgreement
	if err = db.First(&m, req.FacilityAgreementId).Error; err != nil {
		return err
	}
	_ = copier.Copy(&m, req)
	nowTime := time.Now().UTC()
	m.UpdateTime = &nowTime

	if err = db.Save(&m).Error; err != nil {
		return err
	}
	return nil
}

// endregion ---------------------------------------------------- 編輯協議 ----------------------------------------------------

// region ---------------------------------------------------- 發送協議 ----------------------------------------------------
type SendAgreementReq struct {
	FacilityAgreementId uint64 `json:"facilityAgreementId" binding:"required"`
}

// 發送協議並將狀態從UNSENT更新為UNSIGNED
func (s *facilityAgreementService) SendAgreement(db *gorm.DB, req SendAgreementReq) error {
	var err error
	var agreement model.FacilityAgreement

	// 查詢協議
	if err = db.First(&agreement, req.FacilityAgreementId).Error; err != nil {
		return err
	}

	// 獲取機構資料草稿版本
	var facilityProfile model.FacilityProfile
	if err = db.Where("facility_id = ?", agreement.FacilityId).Where("data_type = ?", model.FacilityProfileDataTypeDraft).First(&facilityProfile).Error; err != nil {
		return err
	}

	var facilityProfileUpdateMap = make(map[string]interface{})

	// 拒接待審核的資料變更
	if facilityProfile.Status == model.FacilityProfileStatusReviewing {
		facilityProfileUpdateMap["status"] = model.FacilityProfileStatusPending
		facilityProfileUpdateMap["reject_reason"] = "The facility profile is being reviewed."
		facilityProfileUpdateMap["approved_user_id"] = 0
		facilityProfileUpdateMap["approved_time"] = time.Now().UTC()
	}

	// 取消現有協議簽名的關聯
	// 取消現有協議簽名的關聯
	var facilityFileRelationIds []uint64
	if err = db.Table("facility_file_relation AS ffr").
		Joins("join facility_file AS ff on ff.id = ffr.facility_file_id").
		Where("ffr.facility_id = ?", agreement.FacilityId).
		Where("ffr.facility_profile_id = ?", facilityProfile.Id).
		Where("ff.file_code = ?", model.FacilityFileCodeSignedAgreement).
		Pluck("ffr.id", &facilityFileRelationIds).Error; err != nil {
		return err
	}
	for _, v := range facilityFileRelationIds {
		if err = db.Delete(&model.FacilityFileRelation{}, v).Error; err != nil {
			return err
		}
	}

	// 發送電子郵件給機構
	if err = s.SendAgreementEmail(db, req); err != nil {
		return err
	}

	// 更新協議狀態為UNSIGNED
	if err = db.Model(&agreement).Update("status", model.FacilityAgreementStatusUnsigned).Error; err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 發送協議 ----------------------------------------------------

// region ---------------------------------------------------- 發送協議電郵 ----------------------------------------------------
func (s *facilityAgreementService) SendAgreementEmail(db *gorm.DB, req SendAgreementReq) error {
	var err error
	var agreement model.FacilityAgreement

	// 查詢協議
	if err = db.First(&agreement, req.FacilityAgreementId).Error; err != nil {
		return err
	}

	// 查詢機構資料
	var facility model.Facility
	if err = db.First(&facility, agreement.FacilityId).Error; err != nil {
		return err
	}

	// 查詢機構資料
	var facilityProfile model.FacilityProfile
	if err = db.Where("facility_id = ?", facility.Id).
		Where("data_type = ?", model.FacilityProfileDataTypeApproved).
		First(&facilityProfile).Error; xgorm.IsSqlErr(err) {
		return err
	}
	if xgorm.IsNotFoundErr(err) {
		// 如果沒有已批准的機構資料，則查詢草稿（新機構）
		if err = db.Where("facility_id = ?", facility.Id).
			Where("data_type = ?", model.FacilityProfileDataTypeDraft).
			First(&facilityProfile).Error; err != nil {
			return err
		}
	}

	// 構建電郵內容
	emailGreeting := i18n.Message{
		ID:    "facility_agreement.email.greeting",
		Other: "Hello",
	}
	emailSignature := i18n.Message{
		ID:    "facility_agreement.email.signature",
		Other: "Kind regards,",
	}
	emailIntros := i18n.Message{
		ID:    "facility_agreement.email.intros",
		Other: "We have reviewed and verified the information provided by your facility, and a draft agreement has now been prepared accordingly.",
	}
	emailInstructions := i18n.Message{
		ID:    "facility_agreement.email.instructions",
		Other: "To proceed, please log into our system to complete your facility's details and sign the agreement. You may use the same login credentials (email and password) that were used during your initial registration.",
	}
	emailOutros := i18n.Message{
		ID:    "facility_agreement.email.outros",
		Other: "Should you have any questions or require assistance, please do not hesitate to contact us.",
	}
	emailSubject := i18n.Message{
		ID:    "facility_agreement.email.subject",
		Other: "Agreement Ready for Review and Completion on Medic Crew",
	}
	emailButtonText := i18n.Message{
		ID:    "facility_agreement.email.button.text",
		Other: "Review Agreement",
	}

	// 取得語言設定
	lang := "en" // 預設英文

	// HOME_URL
	url, err := CommonSettingService.GetSettingValueByCode(db, model.CommonSettingCodeHomeUrl)
	if err != nil {
		return err
	}

	// 構建電郵內容
	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLang(lang, &emailGreeting),
		Name:      facilityProfile.Name,
		Signature: xi18n.LocalizeWithLang(lang, &emailSignature),
		Intros: []string{
			xi18n.LocalizeWithLang(lang, &emailIntros),
		},
		Actions: []hermes.Action{
			{
				Instructions: xi18n.LocalizeWithLang(lang, &emailInstructions),
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLang(lang, &emailButtonText),
					Link:  url,
				},
			},
		},
		Outros: []string{
			xi18n.LocalizeWithLang(lang, &emailOutros),
		},
	}

	// 構建郵件產品信息
	product := HermesDefaultProduct(medicCrewLogoUrl)

	content, err := xhermes.GenerateHTML(product, body)
	if err != nil {
		return err
	}

	err = xmail.SendMail(facilityProfile.Email, fmt.Sprintf("[%s] %s", MedicCrewName, xi18n.LocalizeWithLang(lang, &emailSubject)), "text/html", content)
	if err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 發送協議電郵 ----------------------------------------------------

// region ---------------------------------------------------- 管理端下載協議PDF ----------------------------------------------------
type FacilityAgreementDownloadReq struct {
	FacilityAgreementId uint64 `json:"facilityAgreementId" binding:"required"`
}

func (s *facilityAgreementService) Download(db *gorm.DB, req FacilityAgreementDownloadReq) (string, []byte, error) {
	var err error
	var agreement model.FacilityAgreement
	var filename string
	var fileBytes []byte

	// 查詢協議
	if err = db.First(&agreement, req.FacilityAgreementId).Error; err != nil {
		return "", nil, err
	}
	content := agreement.Content

	// 查詢機構
	var facility model.Facility
	if err = db.First(&facility, agreement.FacilityId).Error; err != nil {
		return "", nil, err
	}
	// 查詢機構資料
	var facilityProfile model.FacilityProfile
	if err = db.Where("facility_id = ?", facility.Id).
		Where("data_type = ?", model.FacilityProfileDataTypeDraft).
		First(&facilityProfile).Error; err != nil {
		return "", nil, err
	}
	var commissionPayUpfront model.Commission
	if err = db.First(&commissionPayUpfront, agreement.PayUpfrontCommissionId).Error; err != nil {
		return "", nil, err
	}
	commissionPayUpfrontRate := commissionPayUpfront.CommissionRate.Mul(decimal.NewFromInt(100))
	var commissionPayInArrears model.Commission
	if err = db.First(&commissionPayInArrears, agreement.PayInArrearsCommissionId).Error; err != nil {
		return "", nil, err
	}
	commissionPayInArrearsRate := commissionPayInArrears.CommissionRate.Mul(decimal.NewFromInt(100))
	commencementDateTime, _ := time.Parse(xtool.DateDayA, agreement.BeginTime.String())
	terminationDateTime, _ := time.Parse(xtool.DateDayA, agreement.EndTime.String())
	commencementDate := commencementDateTime.Format(xtool.DateDayC)
	terminationDate := terminationDateTime.Format(xtool.DateDayC)
	// 替換佔位符
	content = s.replaceTemplateVars(content, map[string]string{
		"{{Facility}}":               facilityProfile.Name,
		"{{CommissionPayUpfront}}":   commissionPayUpfrontRate.String() + "%",
		"{{CommissionPayinArrears}}": commissionPayInArrearsRate.String() + "%",
		"{{CommencementDate}}":       commencementDate,
		"{{TerminationDate}}":        terminationDate,
	})

	// 生成PDF
	htmlStr := content

	// 創建轉換器實例
	strConv, err := NewHtmlConverter()
	if err != nil {
		return "", nil, err
	}

	// 處理簽名相關信息
	signatureHtml := s.getSignatureInfo(db, agreement, facilityProfile)

	htmlStr += signatureHtml

	if err = strConv.FromString(htmlStr); err != nil {
		return "", nil, err
	}

	facilityName := strings.ReplaceAll(strings.ToLower(facilityProfile.Name), regexp.QuoteMeta(" "), "_")
	// 生成PDF文件名
	filename = fmt.Sprintf("agreement_%s.pdf", facilityName)

	// 生成PDF文件
	fileBytes, err = strConv.GetPDFBytes()
	if err != nil {
		return "", nil, err
	}

	return filename, fileBytes, nil
}

// 獲取簽名相關信息
func (s *facilityAgreementService) getSignatureInfo(db *gorm.DB, agreement model.FacilityAgreement, facilityProfile model.FacilityProfile) string {
	// 常量定義
	const (
		transparentPixelBase64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
		platformSignaturePath  = "resource/pdf/platform_signature.png"
		withSignatureClass     = "with-signature"
		placeholderOnlyClass   = "placeholder-only"
		emptyDatePlaceholder   = "-----"
	)
	// 默認值初始化
	platformSignatureBase64 := transparentPixelBase64
	facilitySignatureBase64 := transparentPixelBase64
	signedDateStr := emptyDatePlaceholder
	platformSignatureClass := placeholderOnlyClass
	facilitySignatureClass := placeholderOnlyClass

	signatureHtml := `<div class="signature">
            <div class="signature-between">between</div>
            <div class="signature-item platform {{PlatformSignatureClass}}">
                <div class="signature-item-left">
                    <div class="signature-item-left-name">{{PlatformCompanyName}}</div>
                    <div class="signature-item-left-item">and each related entity of {{PlatformCompanyName}}</div>
                    <div class="signature-item-left-item">Signature of representative of {{PlatformCompanyName}}</div>
                    <div class="signature-item-left-date">
                        <div class="signature-item-left-date-label">Date: </div>
                        <div class="signature-item-left-date-value highlight">「{{PlatformSignatureDate}}」</div>
                    </div>
                </div>
                <div class="signature-item-right">
                    <div class="signature-item-right-signature-image">
                        <img src="{{PlatformSignature}}" alt="Signature">
                    </div>
                    <div class="signature-placeholder">Signature</div>
                </div>
            </div>
            <div class="signature-and">And</div>
            <div class="signature-item facility {{FacilitySignatureClass}}">
                <div class="signature-item-left">
                   <div class="signature-item-left-name highlight">「{{FacilityName}}」</div>
                   <div class="signature-item-left-item">
                       <div class="signature-item-left-item-label">Name:</div>
                       <div class="signature-item-left-item-value highlight">「{{FacilityContact}}」</div>
                   </div>
                   <div class="signature-item-left-item">
                       <div class="signature-item-left-item-label">Address:</div>
                       <div class="signature-item-left-item-value highlight">「{{FacilityAddress}}」</div>
                   </div>
                   <div class="signature-item-left-item">
                       <div class="signature-item-left-item-label">Signature of representative of</div>
                       <div class="signature-item-left-item-value highlight">「{{FacilityName}}」</div>
                   </div>
                   <div class="signature-item-left-date">
                       <div class="signature-item-left-date-label">Date:</div>
                       <div class="signature-item-left-date-value highlight">「{{FacilitySignatureDate}}」</div>
                   </div>
                </div>
                <div class="signature-item-right">
                    <div class="signature-item-right-signature-image">
                        <img src="{{FacilitySignature}}" alt="Signature">
                    </div>
                    <div class="signature-placeholder">Signature</div>
                </div>
              </div>
        </div>`

	// 平台簽名處理
	platformSignature, err := os.ReadFile(platformSignaturePath)
	if err != nil {
		// 記錄錯誤但不中斷流程
		if os.IsNotExist(err) {
			// 文件不存在的情況
			fmt.Printf("Platform signature file not found: %v\n", err)
		} else {
			// 其他IO錯誤
			fmt.Printf("Failed to read platform signature file: %v\n", err)
		}
	} else {
		platformSignatureBase64 = "data:image/png;base64," + base64.StdEncoding.EncodeToString(platformSignature)
		platformSignatureClass = withSignatureClass
	}

	// 機構簽名處理
	if agreement.Status == model.FacilityAgreementStatusSigned || agreement.Status == model.FacilityAgreementStatusReviewing {
		// 獲取機構簽名日期
		if agreement.RepresentativeSignedDate.Valid {
			signedDate, _ := time.Parse(xtool.DateDayA, agreement.RepresentativeSignedDate.String())
			signedDateStr = signedDate.Format(xtool.DateDayC)
		}

		// 獲取機構簽名
		if agreement.FacilityFileId != 0 {
			var facilityFile model.FacilityFile
			if err := db.First(&facilityFile, agreement.FacilityFileId).Error; err != nil {
				fmt.Printf("Failed to query facility signature file: %v\n", err)
			} else {
				object, err := xs3.GetObject(facilityFile.Bucket, facilityFile.Path)
				if err != nil {
					fmt.Printf("Failed to get facility signature from S3: %v\n", err)
				} else {
					facilitySignatureBase64 = "data:image/png;base64," + base64.StdEncoding.EncodeToString(object)
					facilitySignatureClass = withSignatureClass
				}
			}
		}
	}
	signatureHtml = s.replaceTemplateVars(signatureHtml, map[string]string{
		"{{PlatformCompanyName}}":    "MEDIC CREW PTY LTD",
		"{{PlatformSignatureDate}}":  signedDateStr,
		"{{PlatformSignature}}":      platformSignatureBase64,
		"{{FacilityName}}":           facilityProfile.Name,
		"{{FacilityContact}}":        facilityProfile.ContactFirstName + " " + facilityProfile.ContactLastName,
		"{{FacilityAddress}}":        facilityProfile.Address + " " + facilityProfile.AddressExtra,
		"{{FacilitySignatureDate}}":  signedDateStr,
		"{{FacilitySignature}}":      facilitySignatureBase64,
		"{{PlatformSignatureClass}}": platformSignatureClass,
		"{{FacilitySignatureClass}}": facilitySignatureClass,
	})

	return signatureHtml
}

// 使用map進行模板替換
func (s *facilityAgreementService) replaceTemplateVars(template string, replacements map[string]string) string {
	result := template
	for placeholder, value := range replacements {
		result = strings.ReplaceAll(result, placeholder, value)
	}
	return result
}

// endregion ---------------------------------------------------- 管理端下載協議PDF ----------------------------------------------------

// region ---------------------------------------------------- 機構下載協議PDF ----------------------------------------------------
type FacilityAgreementDownloadByFacilityProfileReq struct {
	FacilityProfileId uint64 `json:"facilityProfileId" binding:"required"`
}

// endregion ---------------------------------------------------- 機構下載協議PDF ----------------------------------------------------

// region ---------------------------------------------------- 機構的列表 ----------------------------------------------------
type FacilityAgreementSearchByFacilityProfileReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"`                                // 機構ID
	Status     string `form:"status" binding:"omitempty,splitin=UNSIGNED REVIEWING SIGNED"` // 狀態 UNSIGNED REVIEWING SIGNED，多個逗號分割
	Limit      int    `form:"limit"`                                                        // 限制數量
}

type FacilityAgreementSearchByFacilityProfileResp struct {
	FacilityAgreementId uint64     `json:"facilityAgreementId"`
	FacilityId          uint64     `json:"facilityId"`
	BeginTime           xtype.Date `swaggertype:"string" json:"beginTime"`
	EndTime             xtype.Date `swaggertype:"string" json:"endTime"`
	Status              string     `json:"status"`
	Name                string     `json:"name" gorm:"-"`
	FacilityName        string     `json:"-"`
}

func (s *facilityAgreementService) SearchByFacilityProfile(db *gorm.DB, req FacilityAgreementSearchByFacilityProfileReq) ([]FacilityAgreementSearchByFacilityProfileResp, error) {
	var err error
	var resp []FacilityAgreementSearchByFacilityProfileResp
	builder := db.Model(&model.FacilityAgreement{}).
		Table("facility_agreement AS fa").
		Joins("JOIN facility AS f ON f.id = fa.facility_id").
		Joins("JOIN facility_profile AS fp ON fp.facility_id = f.id AND fp.data_type = ?", model.FacilityProfileDataTypeDraft).
		Select([]string{
			"fa.id as facility_agreement_id",
			"fa.facility_id",
			"fp.name as facility_name",
			"fa.begin_time",
			"fa.end_time",
			"fa.status",
		}).
		Where("fa.facility_id = ?", req.FacilityId).
		Where("fa.status <> ?", model.FacilityAgreementStatusUnsent)

	if req.Limit > 0 {
		builder = builder.Limit(req.Limit)
	}

	if req.Status != "" {
		builder = builder.Where("fa.status IN (?)", strings.Split(req.Status, ","))
	}

	if err = builder.
		Order("fa.end_time DESC").
		Error; err != nil {
		return resp, err
	}
	if err = builder.Find(&resp).Error; err != nil {
		return resp, err
	}
	for i, v := range resp {
		resp[i].Name = s.GenerateAgreementName(v.FacilityName, v.EndTime)
	}
	return resp, nil
}

// endregion ---------------------------------------------------- 機構的列表 ----------------------------------------------------

// region ---------------------------------------------------- 獲取機構協議佣金 ----------------------------------------------------
type FacilityAgreementCommissionResp struct {
	FacilityAgreementId         uint64          `json:"facilityAgreementId"`         // 機構協議ID
	PayUpfrontCommissionId      uint64          `json:"payUpfrontCommissionId"`      // 預付款佣金ID
	PayUpfrontCommissionRate    decimal.Decimal `json:"payUpfrontCommissionRate"`    // 預付款佣金率
	PayUpfrontCommissionLevel   string          `json:"payUpfrontCommissionLevel"`   // 預付款佣金等級
	PayInArrearsCommissionId    uint64          `json:"payInArrearsCommissionId"`    // 事後支付佣金ID
	PayInArrearsCommissionRate  decimal.Decimal `json:"payInArrearsCommissionRate"`  // 事後支付佣金率
	PayInArrearsCommissionLevel string          `json:"payInArrearsCommissionLevel"` // 事後支付佣金等級
}

func (s *facilityAgreementService) GetCommissionByFacilityIdAndPeriod(db *gorm.DB, facilityId uint64, date string) (FacilityAgreementCommissionResp, error) {
	var err error
	var resp FacilityAgreementCommissionResp

	var m model.FacilityAgreement
	if err = db.
		Where("facility_id = ?", facilityId).
		Where("begin_time <= ?", date).
		Where("end_time >= ?", date).
		First(&m).Error; err != nil {
		return resp, err
	}

	// 查詢預付款佣金
	var payUpfrontCommission model.Commission
	if err = db.First(&payUpfrontCommission, m.PayUpfrontCommissionId).Error; err != nil {
		return resp, err
	}
	resp.PayInArrearsCommissionId = m.PayUpfrontCommissionId
	resp.PayUpfrontCommissionRate = payUpfrontCommission.CommissionRate
	resp.PayUpfrontCommissionLevel = payUpfrontCommission.Level

	// 查詢事後支付佣金
	var payInArrearsCommission model.Commission
	if err = db.First(&payInArrearsCommission, m.PayInArrearsCommissionId).Error; err != nil {
		return resp, err
	}
	resp.PayInArrearsCommissionId = m.PayInArrearsCommissionId
	resp.PayInArrearsCommissionRate = payInArrearsCommission.CommissionRate
	resp.PayInArrearsCommissionLevel = payInArrearsCommission.Level

	return resp, nil
}

// endregion ---------------------------------------------------- 獲取機構協議佣金 ----------------------------------------------------

// region ---------------------------------------------------- 機構簽署協議文件 ----------------------------------------------------
type FacilityAgreementSignReq struct {
	FacilityAgreementId uint64 `json:"facilityAgreementId" binding:"required"`
	FacilityFileId      uint64 `json:"facilityFileId" binding:"required"`
}

func (s *facilityAgreementService) Sign(db *gorm.DB, req FacilityAgreementSignReq) error {
	var err error

	nowTime := time.Now().UTC().Truncate(time.Second)
	updateMap := map[string]interface{}{
		"facility_file_id":           req.FacilityFileId,
		"status":                     model.FacilityAgreementStatusReviewing,
		"representative_signed_date": xtool.NowFormat(xtool.DateDayA),
		"representative_signed":      model.FacilityAgreementRepresentativeSignedY,
		"update_time":                nowTime,
	}
	if err = db.Model(&model.FacilityAgreement{}).
		Where("id = ?", req.FacilityAgreementId).
		Updates(updateMap).Error; err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 機構簽署協議文件 ----------------------------------------------------

// region ---------------------------------------------------- 獲取機構最新協議（已簽署和未簽署） ----------------------------------------------------

func (s *facilityAgreementService) GetLastFacilityAgreement(db *gorm.DB, facilityId uint64, signed ...bool) (model.FacilityAgreement, error) {
	var err error
	var resp model.FacilityAgreement
	builder := db.Where("facility_id = ?", facilityId).Order("begin_time DESC")
	if len(signed) > 0 && signed[0] {
		builder = builder.Where("status = ?", model.FacilityAgreementStatusSigned)
	} else {
		builder = builder.Where("status <> ?", model.FacilityAgreementStatusUnsent)
	}
	if err = builder.First(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

// endregion ---------------------------------------------------- 獲取機構最新協議 ----------------------------------------------------

// region ---------------------------------------------------- 獲取機構指定時間的已簽署協議 ----------------------------------------------------

func (s *facilityAgreementService) GetFacilityAgreementByTime(db *gorm.DB, facilityId uint64, beginDate, endDate string) (model.FacilityAgreement, error) {
	var err error
	var resp model.FacilityAgreement
	if err = db.Where("facility_id = ?", facilityId).
		Where("begin_time <= ?", beginDate).
		Where("end_time >= ?", endDate).
		Where("status = ?", model.FacilityAgreementStatusSigned).
		Order("begin_time DESC").
		First(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

// endregion ---------------------------------------------------- 獲取機構指定時間的已簽署協議 ----------------------------------------------------
