# 【Medic Crew】後端需求整合文檔 - 重新核對版本

## 文檔說明
本文檔基於現有代碼結構重新梳理後端開發需求，特別針對ABN、GST、銀行資料等功能進行準確的需求分析。

## 現有代碼結構分析

### ABN相關功能（已實現）
- **ABN服務** (`services/abn.go`)：已實現ABN查詢API調用
- **ABN驗證** (`services/professional_profile.go`)：已實現ABN有效性檢查和實體名稱匹配
- **數據庫字段**：Professional模型已包含 `AbnNumber`、`AbnValid`、`AbnEntityName`、`AbnEntityType` 字段
- **API接口**：已提供 `/actions/check-abn` 接口

### 銀行資料功能（已實現）
- **銀行帳戶模型** (`model/professional_bank_account.go`)：已定義完整的銀行帳戶結構
- **付款資料服務** (`services/professional_profile.go`)：已實現獲取和更新付款資料的功能
- **數據庫字段**：包含 `BankStateBranch`、`BankAccountNumber`、`BankAccountName`

## 需求重新梳理

### 1. Professional Profile 功能增強

#### 1.1 個人資訊語言功能
**需求描述**：在Personal Information中增加Language多選輸入

**開發任務**：
- 修改 `ProfessionalProfile` 結構體，增加 `Languages` 字段
- 增加語言選項的校驗邏輯
- 更新進度計算邏輯

**預計時間**：2小時

#### 1.2 Areas of Experience功能合併
**需求描述**：將Medical Practitioner的Preferred Grade合併到Areas of Experience

**開發任務**：
- 修改 `ProfessionalExperience` 結構體
- 更新相關校驗邏輯
- 調整進度計算
- 修改Preferred Grade文件上傳校驗

**預計時間**：4小時

#### 1.3 畢業情況輸入
**需求描述**：為Medical Practitioner/Registered Nurse/Enrolled Nurse增加畢業情況輸入

**開發任務**：
- 在 `ProfessionalProfile` 中增加畢業相關字段
- 增加校驗邏輯
- 更新進度計算

**預計時間**：3小時

#### 1.4 Experience Level選擇
**需求描述**：為Enrolled Nurse/Personal Care Worker增加Experience Level選擇

**開發任務**：
- 修改相關數據結構
- 增加校驗邏輯
- 更新選項管理

**預計時間**：2.5小時

#### 1.5 Medication Endorsement
**需求描述**：為Enrolled Nurse增加Medication Endorsement選擇

**開發任務**：
- 在 `ProfessionalProfile` 中增加相關字段
- 增加校驗和進度計算

**預計時間**：2小時

### 2. Facility Job功能增強

#### 2.1 Allowances輸入功能
**需求描述**：在Post Job中增加Allowances輸入

**開發任務**：
- 修改Job相關模型，增加Allowances字段
- 創建Allowances主檔案管理
- 更新Job創建和編輯API

**預計時間**：5小時

#### 2.2 Personal Care Worker資格要求調整
**需求描述**：將Qualifications改為Min. Qualifications

**開發任務**：
- 修改數據庫字段名稱
- 更新校驗邏輯
- 調整工作搜索篩選邏輯

**預計時間**：3小時

#### 2.3 Shift Date & Time結構修改
**需求描述**：修改Shift Date & Time表格內容結構

**開發任務**：
- 重新設計Shift相關數據結構
- 更新相關API
- 修改前端對應邏輯

**預計時間**：6小時

### 3. Master Data管理

#### 3.1 Allowances主檔案
**需求描述**：創建Allowances的主檔案管理系統

**開發任務**：
- 創建Allowances模型
- 實現CRUD操作
- 提供API接口

**預計時間**：4小時

### 4. GST相關功能現狀分析

**重要發現**：基於深入代碼分析，系統中已經有完整的GST/稅金處理機制：

#### 4.1 現有GST實現
1. **數據庫結構**：
   - `document.tax_amount`：稅額字段
   - `document.tax_rate`：GST稅率字段（預設10%）
   - `document_item.count_tax`：是否計算稅額標記

2. **業務邏輯**：
   - 發票和確認通知單都支援GST計算
   - 固定10%的GST稅率
   - 支援項目級別的稅金開關（Y/N）

3. **API實現**：
   - 發票創建/編輯時的GST計算
   - 確認通知單的GST處理
   - PDF生成包含GST顯示

#### 4.2 需求澄清結論
基於現有實現，「註冊GST」需求可能指：
- Professional是否為GST註冊納稅人的狀態記錄
- 根據Professional的GST狀態決定是否在交易中收取GST
- 與ABN驗證結合，確認GST註冊資格

**建議**：現有GST機制已相當完善，主要需要增加Professional端的GST註冊狀態管理。

### 5. Professional GST狀態管理功能模塊（2小時）

**功能描述**：
為Professional增加GST註冊狀態管理，與現有稅金計算邏輯整合。

**開發任務**：

#### 5.1 數據庫修改（0.5小時）
```sql
ALTER TABLE professional ADD COLUMN gst_registered BOOLEAN DEFAULT FALSE COMMENT 'GST註冊狀態';
ALTER TABLE professional ADD COLUMN gst_number VARCHAR(20) NULL COMMENT 'GST註冊號碼';
```

#### 5.2 Professional Profile API增強（1小時）
- 在Professional Profile更新API中增加GST狀態字段
- 增加GST狀態校驗邏輯
- 與ABN驗證整合（GST註冊通常需要有效ABN）

#### 5.3 稅金計算邏輯調整（0.5小時）
- 修改現有發票和確認通知單邏輯
- 根據Professional的GST註冊狀態決定是否收取GST
- 保持現有10%稅率不變

**預計時間**：2小時

## 開發優先級建議

### 第一階段（高優先級）
1. Areas of Experience功能合併（4小時）
2. Allowances輸入功能（5小時）
3. Personal Care Worker資格要求調整（3小時）
4. Shift Date & Time結構修改（6小時）

### 第二階段（中優先級）
1. 個人資訊語言功能（2小時）
2. 畢業情況輸入（3小時）
3. Allowances主檔案（4小時）
4. Professional GST狀態管理（2小時）

### 第三階段（低優先級）
1. Experience Level選擇（2.5小時）
2. Medication Endorsement（2小時）
3. Preferred Grade文件上傳校驗（2小時）

## 總預計開發時間
**總計：35.5小時**

## 技術實現要點

### 數據庫設計原則
- 遵循現有的命名規範
- 使用適當的索引優化查詢性能
- 保持數據一致性和完整性

### API設計原則
- 遵循現有的RESTful設計模式
- 使用統一的錯誤處理機制
- 提供完整的Swagger文檔

### 代碼質量要求
- 遵循項目現有的代碼規範
- 提供充分的單元測試
- 確保向後兼容性

## 風險評估

### 高風險項目
1. **Shift Date & Time結構修改**：可能影響現有功能，需要仔細測試現有Job相關功能

### 中風險項目
1. **Areas of Experience功能合併**：涉及多個專業類型的邏輯調整
2. **Allowances功能**：新增功能，需要完整的測試
3. **GST狀態管理**：需要與現有稅金計算邏輯整合

### 低風險項目
1. **語言功能**：相對獨立的功能增強
2. **畢業情況輸入**：簡單的字段增加

## 後續行動

1. **確認GST狀態管理需求**：與業務方確認Professional GST註冊狀態的具體管理方式
2. **詳細設計**：為每個功能模塊制定詳細的技術設計方案
3. **測試計劃**：制定完整的測試計劃，確保功能質量
4. **部署策略**：制定分階段部署策略，降低上線風險

---

**文檔版本**：v2.0  
**最後更新**：2024年12月  
**更新說明**：基於現有代碼結構重新梳理需求，澄清GST功能疑問