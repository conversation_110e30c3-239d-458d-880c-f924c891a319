package model

import (
	"github.com/Norray/xrocket/xmodel"
)

const (
	ProfessionalFileCodePhoto                         = "PROFESSIONAL_PHOTO"                              // 照片
	ProfessionalFileCodeAhpraCertificate              = "AHPRA_CERT"                                      // AHPRA證書
	ProfessionalFileCodeAbn                           = "PROFESSIONAL_ABN"                                // ABN
	ProfessionalFileCodeIndemnityInsuranceCertificate = "IND_INSURANCE_CERT"                              // 專業人士責任保險證明
	ProfessionalFileCodePersonalCareWorkQualification = "PROFESSIONAL_PERSONAL_CARE_WORKER_QUALIFICATION" // 個人護理工作資格
	ProfessionalFileCodeQualificationCertificate      = "QUALIFICATION_CERTIFICATE"                       // 學歷資格證書
	ProfessionalFileCodeCurriculumVitae               = "PROFESSIONAL_CURRICULUM_VITAE"                   // 專業人士履歷

	// Personal Care Worker Qualifications 個人護理工作者資格證書
	ProfessionalFileCodePersonalCareWorkerQualificationExperienceAgedCareDisability       = "PPCWQ_EXPERIENCE_AGED_CARE_DISABILITY"        // Two or more years of professional aged care/disability experience in the last five years
	ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIAGEDCARE             = "PPCWQ_CERT_III_AGED_CARE"                     // Certificate III Aged Care
	ProfessionalFileCodePersonalCareWorkerQualificationCertIIIDisabilities                = "PPCWQ_CERT_III_DISABILITIES"                  // Certificate III in Disabilities
	ProfessionalFileCodePersonalCareWorkerQualificationCertIIIIndividualSupport           = "PPCWQ_CERT_III_INDIVIDUAL_SUPPORT"            // Certificate III Individual Support
	ProfessionalFileCodePersonalCareWorkerQualificationCertIIIIndividualSupportAgedAged   = "PPCWQ_CERT_III_INDIVIDUAL_SUPPORT_AGED"       // Certificate III Individual Support (Aged Care)
	ProfessionalFileCodePersonalCareWorkerQualificationCertIIIIndividualSupportDisability = "PPCWQ_CERT_III_INDIVIDUAL_SUPPORT_DISABILITY" // Certificate III Individual Support (Disability)
	ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIHomeCommunityCare    = "PPCWQ_CERT_III_HOME_COMMUNITY_CARE"           // Certificate III in Home and Community Care
	ProfessionalFileCodePersonalCareWorkerQualificationCertificateIVAGEDCARE              = "PPCWQ_CERT_IV_AGED_CARE"                      // Certificate IV Aged Care
	ProfessionalFileCodePersonalCareWorkerQualificationCertificateIVDisabilities          = "PPCWQ_CERT_IV_DISABILITIES"                   // Certificate IV in Disabilities
	ProfessionalFileCodePersonalCareWorkerQualificationCertificateIVHomeCommunityCare     = "PPCWQ_CERT_IV_HOME_COMMUNITY_CARE"            // Certificate IV in Home and Community Care
	ProfessionalFileCodePersonalCareWorkerQualificationDegreeAlliedHealth                 = "PPCWQ_DEGREE_ALLIED_HEALTH"                   // Degree in Allied Health
	ProfessionalFileCodePersonalCareWorkerQualificationDegreeNursing                      = "PPCWQ_DEGREE_NURSING"                         // Degree in Nursing
	ProfessionalFileCodePersonalCareWorkerQualificationOtherRelevant                      = "PPCWQ_OTHER_RELEVANT"                         // Other relevant qualification (Enter title of qualification that is relevant for personal care)
	ProfessionalFileCodePersonalCareWorkerQualificationWorkingTowardNursing               = "PPCWQ_WORKING_TOWARD_NURSING"                 // Working Toward Degree Nursing (You must have completed your first year as a nursing student. Upload an official academic transcript that reflects first year of nursing degree has been completed.)

	// Medical Practitioner Preferred Grade相關文件
	ProfessionalFileCodeRegistrarAccreditedEnrolment = "REGISTRAR_ACCREDITED_ENROLMENT" // Registrar (Accredited) 入學證明
	ProfessionalFileCodeFellowshipCertificate        = "FELLOWSHIP_CERTIFICATE"         // Fellowship證書
	ProfessionalFileCodeSpecialistQualification      = "SPECIALIST_QUALIFICATION"       // Specialist資格證明

	ProfessionalFileCodeIdCheck = "PROFESSIONAL_ID_CHECK" // 專業人士身份證明文件
	// Primary
	ProfessionalFileCodeAustralianPassport               = "AUSTRALIAN_PASSPORT"         // 澳洲護照
	ProfessionalFileCodeForeignPassport                  = "FOREIGN_PASSPORT"            // 外國護照
	ProfessionalFileCodeAustralianBirthCertificate       = "AUSTRALIAN_BIRTH_CERT"       // 澳洲出生證明
	ProfessionalFileCodeAustralianCitizenshipCertificate = "AUSTRALIAN_CITIZENSHIP_CERT" // 澳洲公民證
	// Secondary
	ProfessionalFileCodeCurrentAustraliaDriverLicence         = "CURRENT_AUSTRALIA_DRIVER_LICENCE"           // 澳洲駕照
	ProfessionalFileCodeAustralianPublicServiceEmployeeIDCard = "AUSTRALIAN_PUBLIC_SERVICE_EMPLOYEE_ID_CARD" // 澳洲公務員ID卡
	ProfessionalFileCodeOtherAustralianGovernmentIssueIDCard  = "OTHER_AUSTRALIAN_GOVERNMENT_ISSUE_ID_CARD"  // 其他澳洲政府發出的ID卡
	ProfessionalFileCodeTertiaryStudentIDCard                 = "TERTIARY_STUDENT_ID_CARD"                   // 大學生ID卡
	// Others
	ProfessionalFileCodeCreditDebitAtmCard                = "CREDIT_DEBIT_ATM_CARD"                // 信用卡/扣帳卡/ATM卡
	ProfessionalFileCodeMedicareCard                      = "MEDICARE_CARD"                        // 醫療卡
	ProfessionalFileCodeUtilityBillOrRateNotice           = "UTILITY_BILL_OR_RATE_NOTICE"          // 水電費單或收費通知
	ProfessionalFileCodeStatementFromFinancialInstitution = "STATEMENT_FROM_FINANCIAL_INSTITUTION" // 金融機構的結單
	ProfessionalFileCodeCentrelinkOrPensionCard           = "CENTRELINK_OR_PENSION_CARD"           // 澳洲国民福利署或養老金卡

	ProfessionalFileCodeVisa                                  = "VISA"                                       // 簽證
	ProfessionalFileCodeNationalCriminalCheck                 = "NATIONAL_CRIMINAL_CHECK"                    // 國家犯罪檢查
	ProfessionalFileCodeWorkingWithChildrenOrVulnerablePeople = "WORKING_WITH_CHILDREN_OR_VULNERABLE_PEOPLE" // 兒童/脆弱人群工作檢查
	ProfessionalFileCodeCurrentImmunisationRecords            = "CURRENT_IMMUNISATION_RECORDS"               // 現在的免疫記錄
	ProfessionalFileCodeCommonwealthStatutoryDeclaration      = "COMMONWEALTH_STATUTORY_DECLARATION"         // 聯邦法定聲明

	ProfessionalFileCodeAdditionalCertification = "ADDITIONAL_CERTIFICATION" // 附加證明
	ProfessionalFileCodeDisclosure              = "DISCLOSURE"               // 披露
	ProfessionalFileCodeSignedAgreement         = "SIGNED_AGREEMENT"         // 已簽署的協議

	ProfessionalFileCodeUpdatePrompt = "UPDATE_PROMPT" // 更新提示

	ProfessionalFileAiSuccessYes = "Y"
	ProfessionalFileAiSuccessNo  = "N"
)

// 專業人士文件
type ProfessionalFile struct {
	Id                 uint64 `json:"id" gorm:"primary_key"`
	UserId             uint64 `json:"userId" gorm:"index:userId_idx;not null"`               // 用戶ID
	FileCode           string `json:"fileCode" gorm:"type:varchar(255);not null"`            // 專業人士文件類型
	Mode               string `json:"mode" gorm:"type:varchar(255);not null"`                // 在OSS中的私有還是公開
	Bucket             string `json:"bucket" gorm:"type:varchar(255);not null"`              // Bucket
	Path               string `json:"path" gorm:"type:varchar(255);not null"`                // Bucket下的路徑
	Uuid               string `json:"uuid" gorm:"type:varchar(255);index:uuid_idx;not null"` // 唯一文件名
	OriginFileName     string `json:"originFileName" gorm:"type:varchar(255);not null"`      // 原文件名
	FileName           string `json:"fileName" gorm:"type:varchar(255);not null"`            // 唯一文件名
	FileType           string `json:"fileType" gorm:"type:varchar(255);not null"`            // 文件類型
	FileSize           uint32 `json:"fileSize" gorm:"not null"`                              // 文件大小
	ThumbnailPath      string `json:"thumbnailPath" gorm:"type:varchar(255);not null"`       // 縮略圖路徑
	ThumbnailFileSize  uint32 `json:"thumbnailFileSize" gorm:"not null"`                     // 縮略圖文件大小
	AiResultJson       string `json:"aiResultJson" gorm:"type:text;not null"`                // AI 提取信息結果（只有部分文件需要進行提取）
	AiModel            string `json:"aiModel" gorm:"type:varchar(255);not null"`             // AI 大模型名稱
	AiInputTokenUsage  int32  `json:"aiInputTokenUsage" gorm:"not null"`                     // AI 使用了多少個 Input token
	AiOutputTokenUsage int32  `json:"aiOutputUsageToken" gorm:"not null"`                    // AI 使用了多少個 Output token
	xmodel.Model
}

type ProfessionalFileAiResultCache struct {
	ExpiryDate  string `json:"expiryDate"`  // 到期日(YYYY-MM-DD)
	Number      string `json:"number"`      // 號碼
	Description string `json:"description"` // 描述
}

func (ProfessionalFile) TableName() string {
	return "professional_file"
}

func (ProfessionalFile) SwaggerDescription() string {
	return "專業人士文件"
}
