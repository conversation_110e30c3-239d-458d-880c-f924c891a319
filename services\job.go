package services

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"sort"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	JobNoFormat           = "J%07d"       // 工作編號格式
	JobScheduleNoFormat   = "S%05d%s"     // 工作排程編號格式 S(facilityId)(date)
	JobScheduleNoFormatNo = "%03d"        // 工作排程編號格式 (no)
	jobNoDateFormat       = "020106"      // 工作编号的时间格式
	JobProgressPending    = "PENDING"     // 待發佈
	JobProgressUpcoming   = "UPCOMING"    // 即將開始
	JobProgressInProgress = "IN_PROGRESS" // 進行中
	JobProgressComplete   = "COMPLETE"    // 已完成
	JobProgressCancel     = "CANCEL"      // 已取消

	// 機構分類狀態
	JobCategoryDraft      = "DRAFT"       // 草稿
	JobCategoryOpen       = "OPEN"        // 招聘中
	JobCategoryDisable    = "DISABLE"     // 暫停招聘
	JobCategoryUpcoming   = "UPCOMING"    // 待開始
	JobCategoryInProgress = "IN_PROGRESS" // 進行中
	JobCategoryComplete   = "COMPLETE"    // 已完成
	JobCategoryWaiting    = "WAITING"     // 等待發佈
	JobCategoryCancel     = "CANCEL"      // 已取消

	// 招聘狀態
	JobHiringStatusComplete          = "COMPLETE"           // 已完成
	JobHiringStatusPartiallyComplete = "PARTIALLY_COMPLETE" // 部分完成
	JobHiringStatusIncomplete        = "INCOMPLETE"         // 未完成

	JobTimeStatusNotPublish = "NOT_PUBLISH" // 未發佈 未到發佈時間
	JobTimeStatusHiring     = "HIRING"      // 招聘中 開始時間在1小時之後
	JobTimeStatusWillStart  = "WILL_START"  // 即將開始 開始時間在1小時以內
	JobTimeStatusInProgress = "IN_PROGRESS" // 進行中 已經開始 未結束
	JobTimeStatusComplete   = "COMPLETE"    // 已完成 已結束
)

// 工作不可用
var MsgJobNoAvailable = i18n.Message{
	ID:    "checker.job.no_available",
	Other: "The job is not available.",
}

// 工作未發佈
var MsgJobNotPublished = i18n.Message{
	ID:    "checker.job.not_published",
	Other: "The job is not published.",
}

// 招聘已結束
var MsgJobHiringEnded = i18n.Message{
	ID:    "checker.job.hiring_ended",
	Other: "The job hiring has ended.",
}

// 工作即將開始
var MsgJobWillStart = i18n.Message{
	ID:    "checker.job.will_start",
	Other: "The job is about to start",
}

// 邀請人數已達上限 - 機構
var MsgJobInviteMax = i18n.Message{
	ID:    "checker.job.invite_max",
	Other: "The job has reached the maximum number of people.",
}

// 工作所需人數已達上限 - 專業人士
var MsgJobRequiredMax = i18n.Message{
	ID:    "checker.job.required_max",
	Other: "The job has reached the maximum number of people.",
}

// 前端後端金額不一致錯誤信息
var MsgJobGrandTotalMismatch = i18n.Message{
	ID:    "checker.job.grand_total_mismatch",
	Other: "The calculated grand total does not match the provided amount.",
}

// 工作職位服務
var JobService = new(jobService)

type jobService struct{}

// 檢查工作職位ID是否存在
func (s *jobService) CheckIdExist(db *gorm.DB, m *model.Job, id uint64, facilityId ...uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job.id.does_not_exist",
		Other: "No such record, please try after reloading.",
	}
	var err error
	builder := db.Where("id = ?", id)
	if len(facilityId) > 0 {
		builder = builder.Where("facility_id = ?", facilityId[0])
	}
	err = builder.First(&m).Error
	if xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

// 檢查是否可以修改工作職位
func (s *jobService) CheckCanEdit(db *gorm.DB, facilityId uint64, jobId uint64) (bool, i18n.Message, error) {
	var err error
	job, err := s.first(db, facilityId, jobId)
	if err != nil {
		return false, i18n.Message{}, err
	}
	canNotEditMsg := i18n.Message{
		ID:    "checker.job.status.cannot_edit",
		Other: "The job status is not editable.",
	}

	// 檢查是否為模板，模板不能編輯
	if job.ScheduleTemplate == model.JobScheduleTemplateY {
		return false, canNotEditMsg, nil
	}

	// 檢查申請人數
	var applicantCount int32
	applicantCount, err = s.GetJobApplicantCount(db, facilityId, jobId, false)
	if err != nil {
		return false, i18n.Message{}, err
	}

	// 根據不同狀態和時間條件判斷是否可編輯
	switch job.Status {
	case model.JobStatusPending:
		// 草稿狀態可以編輯
		return true, i18n.Message{}, nil

	case model.JobStatusPublish:
		nowTime := time.Now().UTC().Truncate(time.Second)
		// 如果發佈時間還沒到，則可以編輯
		if job.PublishTime.After(nowTime) {
			return true, i18n.Message{}, nil
		}
		if job.PublishTime == nil {
			return false, i18n.Message{}, errors.New("publish time is nil")
		}
		if job.BeginTime == nil {
			return false, i18n.Message{}, errors.New("begin time is nil")
		}
		// 開始時間-1小時
		oneHourBeforeBegin := job.BeginTime.Add(-time.Hour)

		// 判斷是否為招聘中 Now < BeginTime-1h 且 無人申請
		if job.BeginTime.After(oneHourBeforeBegin) && applicantCount == 0 {
			return true, i18n.Message{}, nil
		}

		// 其他情況不能編輯
		return false, canNotEditMsg, nil

	default:
		// 其他狀態（如DISABLE、CANCEL、COMPLETE）不能編輯
		return false, canNotEditMsg, nil
	}
}

// 檢查是否可以修改班次分配方式
func (s *jobService) CheckCanEditShiftAllocation(db *gorm.DB, facilityId uint64, jobId uint64) (bool, i18n.Message, error) {
	var err error
	job, err := s.first(db, facilityId, jobId)
	if err != nil {
		return false, i18n.Message{}, err
	}

	if job.ShiftAllocation == model.JobShiftAllocationManual {
		// 班次分配方式為手動，不能修改
		return false, i18n.Message{
			ID:    "checker.job.shift_allocation.cannot_edit",
			Other: "The job shift allocation cannot be edited.",
		}, nil
	}

	return true, i18n.Message{}, nil
}

// 檢查班次時間範圍
func (s *jobService) CheckShiftTimeRange(jobShiftItems []JobShiftItem) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job.status.cannot_publish.shift_time_range",
		Other: "The job shift time range is not valid.",
	}

	if len(jobShiftItems) == 0 {
		return false, msg, nil
	}

	for _, item := range jobShiftItems {
		if item.BeginTime == nil || item.EndTime == nil {
			return false, msg, nil
		}
		beginTime := *item.BeginTime
		endTime := *item.EndTime
		if beginTime.After(endTime) {
			return false, msg, nil
		}
		// 判斷時間是否正確
		duration := endTime.Sub(beginTime).Hours()
		durationDecimal := decimal.NewFromFloat(duration)
		if item.Duration.GreaterThan(decimal.Zero) && item.Duration.LessThan(durationDecimal) {
			return false, msg, nil
		}
		// BreakDuration 和 PayHours 不能大於 Duration，PayHours 不能小於0， BreakDuration 不能大於 PayHours
		if item.PayHours.GreaterThan(durationDecimal) || item.BreakDuration.GreaterThan(durationDecimal) || item.PayHours.LessThan(decimal.Zero) || item.BreakDuration.GreaterThan(item.PayHours) {
			return false, msg, nil
		}
	}

	return true, i18n.Message{}, nil
}

// 檢查是否可以發佈工作
func (s *jobService) CheckCanPublish(oldStatus string, beginTime *time.Time, timezone string) (bool, i18n.Message, error) {
	var err error

	if beginTime == nil {
		return false, i18n.Message{
			ID:    "checker.job.status.cannot_publish.begin_time",
			Other: "The job begin time is required.",
		}, nil
	}
	canNotPublishMsg := i18n.Message{
		ID:    "checker.job.status.cannot_publish",
		Other: "The job status is not publishable.",
	}
	// 當前狀態不是草稿或招聘中不能發佈
	if oldStatus != model.JobStatusPending && oldStatus != model.JobStatusPublish {
		return false, canNotPublishMsg, nil
	}
	if timezone == "" {
		// 未發佈狀態，時區才會空，有服務地址但時區為空
		return false, i18n.Message{
			ID:    "checker.job.status.cannot_publish.timezone",
			Other: "Please select the service address information.",
		}, nil
	}

	tz, err := time.LoadLocation(timezone)
	if err != nil {
		return false, i18n.Message{}, err
	}

	startTime := beginTime.In(tz)

	// 距離開始時間1小時
	oneHourBeforeBegin := startTime.Add(-time.Hour)

	nowTime := time.Now().In(tz)

	// 判斷是否為 Now < (BeginTime - 1h)
	if nowTime.After(oneHourBeforeBegin) {
		// 工作開始時間需要大於現在時間1小時
		return false, i18n.Message{
			ID:    "checker.job.status.cannot_publish.begin_time",
			Other: "The job begin time must be greater than the current time by 1 hour.",
		}, nil
	}

	return true, i18n.Message{}, nil
}

// 檢查是否可以刪除工作職位
func (s *jobService) CheckCanDelete(db *gorm.DB, facilityId uint64, jobId uint64) (bool, i18n.Message, error) {
	var err error
	job, err := s.first(db, facilityId, jobId)
	if err != nil {
		return false, i18n.Message{}, err
	}
	canNotDeleteMsg := i18n.Message{
		ID:    "checker.job.cannot_delete",
		Other: "The job cannot be deleted.",
	}

	// 檢查是否為模板，模板不能刪除
	if job.ScheduleTemplate == model.JobScheduleTemplateY {
		return false, canNotDeleteMsg, nil
	}

	// 檢查申請人數
	var applicantCount int32
	applicantCount, err = s.GetJobApplicantCount(db, facilityId, jobId, false)
	if err != nil {
		return false, i18n.Message{}, err
	}

	// 根據不同狀態判斷是否可刪除
	switch job.Status {
	case model.JobStatusPending:
		// 草稿狀態可以刪除
		return true, i18n.Message{}, nil

	case model.JobStatusPublish:
		nowTime := time.Now().UTC().Truncate(time.Second)
		// 等待發佈 (PublishTime > now)
		if job.PublishTime.After(nowTime) {
			return true, i18n.Message{}, nil
		}
		// 招聘中 (now < BeginTime-1h) 且無人申請
		oneHourBeforeBegin := job.BeginTime.Add(-time.Hour)
		if nowTime.Before(oneHourBeforeBegin) && applicantCount == 0 {
			return true, i18n.Message{}, nil
		}

		// 其他情況不能刪除
		return false, canNotDeleteMsg, nil

	case model.JobStatusCancel:
		// 已取消且無人申請可刪除
		if applicantCount == 0 {
			return true, i18n.Message{}, nil
		}
		return false, i18n.Message{
			ID:    "checker.job.cannot_delete.application",
			Other: "The job has applicants, cannot be deleted.",
		}, nil

	default:
		// 其他狀態不能刪除
		return false, canNotDeleteMsg, nil
	}
}

// 檢查是否可以邀請專業人士
func (s *jobService) CheckCanInviteProfessional(db *gorm.DB, facilityId uint64, jobId uint64) (bool, i18n.Message, error) {
	var err error
	job, err := s.first(db, facilityId, jobId)
	if err != nil {
		return false, i18n.Message{}, err
	}

	if job.Status != model.JobStatusPublish {
		return false, MsgJobNotPublished, nil
	}

	// 需要檢查時間範圍
	timeStatus := s.GetJobTimeStatus(*job.PublishTime, *job.BeginTime, *job.EndTime)
	switch timeStatus {
	case JobTimeStatusNotPublish:
		return false, MsgJobWillStart, nil // 未發佈
	case JobTimeStatusWillStart:
		return false, MsgJobWillStart, nil // 即將開始 開始時間在1小時以內
	case JobTimeStatusInProgress, JobTimeStatusComplete:
		return false, MsgJobNoAvailable, nil // 進行中 已經開始 未結束 已完成 已結束
	}

	// 檢查是否為模板
	if job.ScheduleTemplate == model.JobScheduleTemplateY {
		return false, MsgJobNoAvailable, nil
	}

	// 檢查已邀請人數
	var applicantCount int64
	if err := db.Model(&model.JobApplication{}).
		Where("facility_id = ?", facilityId).
		Where("job_id = ?", jobId).
		Where("status IN ?", []string{model.JobApplicationStatusAccept, model.JobApplicationStatusInvite}).
		Count(&applicantCount).Error; err != nil {
		return false, i18n.Message{}, err
	}
	if applicantCount >= int64(job.NumberOfPeople) {
		return false, MsgJobInviteMax, nil
	}

	return true, i18n.Message{}, nil
}

// 檢查是否可以撤回邀請
func (s *jobService) CheckCanWithdrawInvite(db *gorm.DB, facilityId uint64, jobId uint64, jobApplicationId uint64) (bool, i18n.Message, error) {
	var err error
	var jobApplication model.JobApplication
	if err = db.Model(&model.JobApplication{}).
		Where("id = ?", jobApplicationId).
		Where("job_id = ?", jobId).
		Where("facility_id = ?", facilityId).
		First(&jobApplication).Error; xgorm.IsSqlErr(err) {
		return false, i18n.Message{}, err
	}
	if jobApplication.Status != model.JobApplicationStatusInvite {
		return false, i18n.Message{
			ID:    "checker.job.cannot_withdraw.status",
			Other: "This job application is not in an invitation status.",
		}, nil
	}

	return true, i18n.Message{}, nil
}

// 檢查是否可以接受邀請
func (s *jobService) CheckCanAcceptInvite(db *gorm.DB, facilityId uint64, jobId uint64, jobApplicationId uint64) (bool, i18n.Message, error) {
	var err error
	var jobApplication model.JobApplication
	if err = db.Model(&model.JobApplication{}).
		Where("id = ?", jobApplicationId).
		Where("job_id = ?", jobId).
		Where("facility_id = ?", facilityId).
		First(&jobApplication).Error; xgorm.IsSqlErr(err) {
		return false, i18n.Message{}, err
	}
	if jobApplication.Status != model.JobApplicationStatusInvite {
		return false, MsgJobNoAvailable, nil
	}

	var job model.Job
	if err = db.Model(&model.Job{}).
		Where("id = ?", jobId).
		Where("facility_id = ?", facilityId).
		First(&job).Error; xgorm.IsSqlErr(err) {
		return false, i18n.Message{}, err
	}

	if job.Status != model.JobStatusPublish {
		return false, MsgJobNotPublished, nil
	}

	// 檢查工作狀態
	timeStatus := s.GetJobTimeStatus(*job.PublishTime, *job.BeginTime, *job.EndTime)
	switch timeStatus {
	case JobTimeStatusNotPublish:
		return false, MsgJobWillStart, nil // 未發佈 未到發佈時間
	case JobTimeStatusWillStart:
		return false, MsgJobWillStart, nil // 即將開始 開始時間在1小時以內
	case JobTimeStatusInProgress, JobTimeStatusComplete:
		return false, MsgJobNoAvailable, nil // 進行中 已經開始 未結束 已完成 已結束
	}

	// 檢查已邀請人數
	var applicantCount int64
	if err = db.Model(&model.JobApplication{}).
		Where("facility_id = ?", facilityId).
		Where("job_id = ?", jobId).
		Where("status = ?", model.JobApplicationStatusAccept).
		Where("accept = ?", model.JobApplicationAcceptY).
		Where("deleted <> ?", model.JobApplicationDeletedY).
		Count(&applicantCount).Error; err != nil {
		return false, i18n.Message{}, err
	}
	if applicantCount >= int64(job.NumberOfPeople) {
		return false, MsgJobRequiredMax, nil
	}

	// 檢查時間段內容是否有已經確認的工作
	var jobShifts []model.JobShift
	if err = db.Model(&model.JobShift{}).
		Where("job_id = ?", jobId).
		Find(&jobShifts).Error; err != nil {
		return false, i18n.Message{}, err
	}
	timeHasJob := false
	for _, jobShift := range jobShifts {
		var otherJobShift model.JobShift
		err = db.Model(&model.JobApplication{}).
			Table("job_application AS ja").
			Joins("JOIN job AS j ON j.id = ja.job_id AND j.status = ?", model.JobStatusPublish).
			Joins("JOIN job_shift AS js ON js.job_id = j.id").
			Where("ja.user_id = ?", jobApplication.UserId).
			Where("ja.status = ?", model.JobApplicationStatusAccept).
			// 在時間段內
			Where("js.begin_time < ?", jobShift.EndTime).
			Where("js.end_time > ?", jobShift.BeginTime).
			First(&otherJobShift).Error

		if err != nil {
			if xgorm.IsNotFoundErr(err) {
				continue
			}
			return false, i18n.Message{}, err
		}

		if otherJobShift.Id != 0 {
			timeHasJob = true
			break
		}
	}
	if timeHasJob {
		return false, i18n.Message{
			ID:    "checker.job.cannot_accept.time_has_job",
			Other: "You already have another job scheduled during this time period.",
		}, nil
	}

	return true, i18n.Message{}, nil
}

// 檢查發佈時間是否正確
func (s *jobService) CheckPublishTime(db *gorm.DB, publishTime time.Time) (bool, i18n.Message, error) {
	nowTime := time.Now().UTC().Truncate(time.Second)
	if publishTime.Before(nowTime) {
		// 發佈時間不能早於當前時間
		return false, i18n.Message{
			ID:    "checker.job.cannot_invite.publish_time",
			Other: "The job has not been published yet.",
		}, nil
	}
	return true, i18n.Message{}, nil
}

// 檢查是否可以邀請
func (s *jobService) CheckCanInvite(db *gorm.DB, facilityId uint64, jobId uint64, jobApplicationId uint64) (bool, i18n.Message, error) {
	var err error
	var jobApplication model.JobApplication
	if err = db.Model(&model.JobApplication{}).
		Where("id = ?", jobApplicationId).
		Where("job_id = ?", jobId).
		Where("facility_id = ?", facilityId).
		First(&jobApplication).Error; err != nil {
		return false, i18n.Message{}, err
	}
	switch jobApplication.Status {
	case model.JobApplicationStatusInvite:
		// 已發送邀請
		return false, i18n.Message{
			ID:    "checker.job.cannot_invite.invite",
			Other: "You have already sent an invitation to this job.",
		}, nil
	case model.JobApplicationStatusAccept:
		// 已接受邀請 專業人士已接受邀請
		return false, i18n.Message{
			ID:    "checker.job.cannot_invite.accept",
			Other: "The professional has already accepted the invitation.",
		}, nil
	case model.JobApplicationStatusWithdraw:
		// 已撤回邀請
		return false, i18n.Message{
			ID:    "checker.job.cannot_invite.withdraw",
			Other: "The professional has already withdrawn the invitation.",
		}, nil
	case model.JobApplicationStatusProfessionalCancel:
		// 專業人士已取消申請
		return false, i18n.Message{
			ID:    "checker.job.cannot_invite.professional_cancel",
			Other: "The professional has already canceled the application.",
		}, nil
	case model.JobApplicationStatusApplicationCancel:
		// 已取消申請
		return false, i18n.Message{
			ID:    "checker.job.cannot_invite.application_cancel",
			Other: "You have already canceled the application.",
		}, nil
	case model.JobApplicationStatusFacilityCancel:
		// 機構已取消申請
		return false, i18n.Message{
			ID:    "checker.job.cannot_invite.facility_cancel",
			Other: "You have already canceled the application.",
		}, nil
	}
	var job model.Job
	if err = db.Model(&model.Job{}).
		Where("id = ?", jobId).
		Where("facility_id = ?", facilityId).
		First(&job).Error; err != nil {
		return false, i18n.Message{}, err
	}
	if job.Status != model.JobStatusPublish {
		return false, i18n.Message{
			ID:    "checker.job.cannot_invite.publish",
			Other: "This job is not published.",
		}, nil
	}
	nowTime := time.Now().UTC().Truncate(time.Second)
	if nowTime.After(*job.BeginTime) {
		// 工作已開始，不能邀請
		return false, i18n.Message{
			ID:    "checker.job.cannot_invite.begin_time",
			Other: "This job has already started.",
		}, nil
	}
	beginTime := job.BeginTime.Add(-time.Hour * 1)
	if nowTime.After(beginTime) {
		// 工作即將開始，不能邀請
		return false, i18n.Message{
			ID:    "checker.job.cannot_invite.will_start",
			Other: "This job will start soon.",
		}, nil
	}

	// 檢查已邀請人數
	var applicantCount int64
	if err = db.Model(&model.JobApplication{}).
		Where("facility_id = ?", facilityId).
		Where("job_id = ?", jobId).
		Where("status = ?", model.JobApplicationStatusAccept).
		Where("accept = ?", model.JobApplicationAcceptY).
		Where("deleted <> ?", model.JobApplicationDeletedY).
		Count(&applicantCount).Error; err != nil {
		return false, i18n.Message{}, err
	}
	if applicantCount >= int64(job.NumberOfPeople) {
		return false, MsgJobRequiredMax, nil
	}

	return true, i18n.Message{}, nil
}

// 檢查是否可以撤銷邀請
func (s *jobService) CheckCanRevokeInvite(db *gorm.DB, facilityId uint64, jobId uint64, jobApplicationId uint64) (bool, i18n.Message, error) {
	var err error
	var jobApplication model.JobApplication
	if err = db.Model(&model.JobApplication{}).
		Where("id = ?", jobApplicationId).
		Where("job_id = ?", jobId).
		Where("facility_id = ?", facilityId).
		First(&jobApplication).Error; err != nil {
		return false, i18n.Message{}, err
	}
	switch jobApplication.Status {
	case model.JobApplicationStatusAccept:
		// 專業人士已接受邀請
		return false, i18n.Message{
			ID:    "checker.job.cannot_revoke_invite.accept",
			Other: "The professional has already accepted the invitation.",
		}, nil
	case model.JobApplicationStatusDecline:
		// 已婉拒邀請
		return false, i18n.Message{
			ID:    "checker.job.cannot_revoke_invite.decline",
			Other: "The professional has already declined the invitation.",
		}, nil
	case model.JobApplicationStatusInvite:
		// 可以撤銷邀請
	default:
		// 其他狀態，不能撤銷邀請
		return false, i18n.Message{
			ID:    "checker.job.cannot_revoke_invite.other",
			Other: "The invitation cannot be revoked.",
		}, nil
	}

	return true, i18n.Message{}, nil
}

// 獲取工作職位狀態
func (s *jobService) GetJobTimeStatus(publishTime time.Time, beginTime time.Time, endTime time.Time) string {
	nowTime := time.Now().UTC().Truncate(time.Second)
	oneHourBeforeBegin := beginTime.Add(-time.Hour) // BeginTime-1h
	// publishTime -> oneHourBeforeBegin -> beginTime -> endTime

	// nowTime < publishTime
	if publishTime.After(nowTime) {
		return JobTimeStatusNotPublish // 未發佈 未到發佈時間
	}
	// nowTime < oneHourBeforeBegin
	if oneHourBeforeBegin.After(nowTime) {
		return JobTimeStatusHiring // 招聘中 開始時間在1小時之後
	}
	// oneHourBeforeBegin < nowTime < beginTime
	if oneHourBeforeBegin.Before(nowTime) && beginTime.After(nowTime) {
		return JobTimeStatusWillStart // 即將開始 開始時間在1小時以內
	}
	// beginTime < nowTime < endTime
	if beginTime.Before(nowTime) && endTime.After(nowTime) {
		return JobTimeStatusInProgress // 進行中 已經開始 未結束
	}
	return JobTimeStatusComplete // 已完成 已結束
}

// 檢查是否可以更新工作職位狀態
func (s *jobService) CheckCanUpdateStatus(db *gorm.DB, jobId uint64, targetStatus string) (bool, i18n.Message, error) {
	var err error
	var job model.Job
	if err = db.First(&job, jobId).Error; xgorm.IsSqlErr(err) {
		return false, i18n.Message{}, err
	}
	// 狀態不能更新
	msg := i18n.Message{
		ID:    "checker.job.status.cannot_update",
		Other: "The job status is not updatable.",
	}
	// 工作即將開始，不能更新狀態msg
	beginTimeMsg := i18n.Message{
		ID:    "checker.job.status.cannot_update.begin_time",
		Other: "The job is about to start, cannot be updated.",
	}
	// 已經完成，不能更新狀態msg
	completeMsg := i18n.Message{
		ID:    "checker.job.status.cannot_update.complete",
		Other: "The job has completed, cannot be updated.",
	}

	// 距離發佈時間60分鐘內，不能更新狀態
	var isIn60Minutes bool
	nowTime := time.Now().UTC()
	duration := (*job.BeginTime).Sub(nowTime).Minutes()
	// 是否在60分鐘內
	isIn60Minutes = duration <= 60
	isCompleted := nowTime.After(*job.EndTime)

	switch job.Status {
	case model.JobStatusPending:
		if targetStatus != model.JobStatusPublish {
			return false, msg, nil
		}
		if isIn60Minutes {
			return false, beginTimeMsg, nil
		}
		return true, i18n.Message{}, nil // 距離發佈時間60分鐘前，可以發佈
	case model.JobStatusPublish:
		switch targetStatus {
		case model.JobStatusDisable:
			if !isIn60Minutes {
				return true, i18n.Message{}, nil // 距離發佈時間60分鐘前，可以禁用
			}
			return false, msg, nil
		case model.JobStatusCancel:
			if isCompleted {
				return false, completeMsg, nil
			}
			return true, i18n.Message{}, nil // 未完成前，可以取消
		}
	case model.JobStatusDisable:
		switch targetStatus {
		case model.JobStatusPublish:
			if !isIn60Minutes {
				return true, i18n.Message{}, nil // 距離發佈時間60分鐘前，可以恢復發佈
			}
		case model.JobStatusCancel:
			return true, i18n.Message{}, nil // 已禁用，可以取消
		}
	case model.JobStatusComplete:
		return false, completeMsg, nil
	case model.JobStatusCancel:
		return false, msg, nil
	}
	return false, msg, nil
}

// 獲取工作職位申請人數
func (s *jobService) GetJobApplicantCount(db *gorm.DB, facilityId uint64, jobId uint64, onlyAccept bool) (int32, error) {
	var count int64
	builder := db.Model(&model.JobApplication{}).
		Where("facility_id = ?", facilityId).
		Where("job_id = ?", jobId)
	if onlyAccept {
		builder = builder.Where("accept = ?", model.JobApplicationAcceptY).
			Where("status = ?", model.JobApplicationStatusAccept)
	}
	if err := builder.Count(&count).Error; err != nil {
		return 0, err
	}
	return int32(count), nil
}

// 獲取工作職位
func (s *jobService) first(db *gorm.DB, facilityId uint64, jobId uint64) (model.Job, error) {
	var m model.Job
	if err := db.Model(&model.Job{}).
		Where("facility_id = ?", facilityId).
		Where("id = ?", jobId).
		First(&m).Error; err != nil {
		return m, err
	}
	return m, nil
}

// 生成工作編號
func (s *jobService) GenerateJobNo(db *gorm.DB, facilityId uint64, timezone string) (string, error) {
	var err error
	var facilityProfile model.FacilityProfile
	if err = db.Model(&model.FacilityProfile{}).
		Where("facility_id = ?", facilityId).
		Where("data_type = ?", model.FacilityProfileDataTypeApproved).
		Select([]string{"id", "no"}).
		First(&facilityProfile).Error; err != nil {
		return "", err
	}
	if facilityProfile.No == "" {
		return "", errors.New("facility no is empty")
	}
	facilityNo := facilityProfile.No
	nowTime := time.Now()
	if timezone != "" {
		tz, err := time.LoadLocation(timezone)
		if err != nil {
			return "", err
		}
		nowTime = nowTime.In(tz)
	}

	dayNo := nowTime.Format(jobNoDateFormat) // 日月年
	jobNoPrefix := fmt.Sprintf("%s%s", facilityNo, dayNo)

	var m model.Job
	var jobNo string
	if err = db.Model(&model.Job{}).
		Select("MAX(job_no) AS job_no").
		Where("facility_id = ?", facilityId).
		Where("schedule_template = ?", model.JobScheduleTemplateN).
		Where("job_no LIKE ?", fmt.Sprintf("%s%%", jobNoPrefix)).
		First(&m).Error; xgorm.IsSqlErr(err) {
		return "", err
	}
	num := 0
	if m.JobNo != "" {
		numStr := strings.TrimPrefix(m.JobNo, jobNoPrefix)
		num, err = strconv.Atoi(numStr)
		if err != nil {
			return "", err
		}
	}

	jobNo = fmt.Sprintf("%s%02d", jobNoPrefix, num+1)
	return jobNo, nil
}

// 生成工作排程編號
func (s *jobService) GenerateJobScheduleNo(db *gorm.DB, facilityId uint64, timezone string) (string, error) {
	var m model.Job

	tz, err := time.LoadLocation(timezone)
	if err != nil {
		return "", err
	}
	nowTime := time.Now().In(tz)
	prefix := fmt.Sprintf(JobScheduleNoFormat, facilityId, nowTime.Format(jobNoDateFormat))
	var jobNo string
	if err := db.Model(&model.Job{}).
		Select("MAX(job_no) AS job_no").
		Where("facility_id = ?", facilityId).
		Where("schedule_template = ?", model.JobScheduleTemplateY).
		Where("job_no LIKE ?", fmt.Sprintf("%%%s%%", prefix)).
		First(&m).Error; xgorm.IsSqlErr(err) {
		return "", err
	}
	if m.JobNo == "" {
		jobNo = fmt.Sprintf(JobScheduleNoFormatNo, 0)
	} else {
		jobNo = m.JobNo
	}
	numStr := strings.TrimPrefix(jobNo, prefix)
	num, err := strconv.Atoi(numStr)
	if err != nil {
		return "", err
	}
	jobNo = fmt.Sprintf(JobScheduleNoFormatNo, num+1)
	return fmt.Sprintf("%s%s", prefix, jobNo), nil
}

// 獲取班次時間範圍
func (s *jobService) GetShiftTimeRange(shiftTimeItems []JobShiftItem) (*time.Time, *time.Time) {
	if len(shiftTimeItems) == 0 {
		return nil, nil
	}
	var beginTime *time.Time
	var endTime *time.Time
	hasEmpty := false

	for _, item := range shiftTimeItems {
		if item.BeginTime == nil || item.EndTime == nil {
			hasEmpty = true
		}
		if item.BeginTime != nil && (beginTime == nil || item.BeginTime.Before(*beginTime)) {
			beginTime = item.BeginTime
		}
		if item.EndTime != nil && (endTime == nil || item.EndTime.After(*endTime)) {
			endTime = item.EndTime
		}
	}
	if hasEmpty {
		return nil, nil
	}
	return beginTime, endTime
}
func (s *jobService) GetShiftDuration(jobShiftItems []JobShiftItem) (decimal.Decimal, decimal.Decimal, decimal.Decimal, error) {
	var duration decimal.Decimal
	var breakDuration decimal.Decimal
	var payHours decimal.Decimal
	for _, item := range jobShiftItems {
		duration = duration.Add(item.Duration).Round(1)
		breakDuration = breakDuration.Add(item.BreakDuration).Round(1)
		payHours = payHours.Add(item.PayHours).Round(1)
	}
	return duration, breakDuration, payHours, nil
}

// 獲取時間間隔
func (s *jobService) GetTimeDuration(beginTime string, endTime string) (decimal.Decimal, error) {
	var hours decimal.Decimal
	start, err := time.Parse(xtool.DateTimeSecA1, beginTime)
	if err != nil {
		return hours, err
	}
	end, err := time.Parse(xtool.DateTimeSecA1, endTime)
	if err != nil {
		return hours, err
	}
	duration := end.Sub(start).Hours()
	// 小數點後保留1位
	return decimal.NewFromFloat(duration).Round(1), nil
}

// 更新班次時間
func (s *jobService) UpdateJobShiftItems(db *gorm.DB, facilityId uint64, jobId uint64, shiftItems []JobShiftItem) error {
	var err error
	// 先刪除原有的班次時間和津貼
	if err = db.Where("facility_id = ? AND job_id = ?", facilityId, jobId).Delete(&model.JobShift{}).Error; err != nil {
		return err
	}
	if err = db.Where("facility_id = ? AND job_id = ?", facilityId, jobId).Delete(&model.JobAllowance{}).Error; err != nil {
		return err
	}
	if len(shiftItems) == 0 {
		return nil
	}

	// 收集所有津貼ID
	allowanceIds := make([]uint64, 0)
	for _, item := range shiftItems {
		for _, allowanceReq := range item.Allowances {
			allowanceIds = append(allowanceIds, allowanceReq.AllowanceId)
		}
	}

	// 批量查詢 AttractsSuperannuation
	allowanceMap, err := s.GetAllowanceAttractsSuperannuationMap(db, allowanceIds)
	if err != nil {
		return err
	}

	// 計算所有津貼金額並處理誤差分配
	shiftAllowanceAmounts, jobAllowancesData, err := s.calculateAllowanceAmountsWithErrorHandling(shiftItems, allowanceMap)
	if err != nil {
		return err
	}

	// 準備創建班次時間數據
	var jobShifts []model.JobShift
	for i, item := range shiftItems {
		var beginTime *time.Time
		var endTime *time.Time
		if item.BeginTime != nil {
			beginTime = item.BeginTime
		}
		if item.EndTime != nil {
			endTime = item.EndTime
		}

		jobShift := model.JobShift{
			Id:               0,
			FacilityId:       facilityId,
			JobId:            jobId,
			BeginTime:        beginTime,
			EndTime:          endTime,
			Duration:         item.Duration,
			BreakDuration:    item.BreakDuration,
			PayHours:         item.PayHours,
			ShiftPeriod:      item.ShiftPeriod,
			HourlyRate:       item.HourlyRate,
			AllowanceAmount:  shiftAllowanceAmounts[i],
			BreakTimePayable: item.BreakTimePayable,
		}
		jobShifts = append(jobShifts, jobShift)
	}

	// 批量創建班次時間
	if err = db.Create(&jobShifts).Error; err != nil {
		return err
	}

	// 準備津貼數據並設置正確的 JobShiftId
	var jobAllowances []model.JobAllowance
	for i, shiftAllowanceData := range jobAllowancesData {
		for _, allowanceData := range shiftAllowanceData {
			allowance := model.JobAllowance{
				FacilityId:             facilityId,
				JobId:                  jobId,
				JobShiftId:             jobShifts[i].Id,
				AllowanceId:            allowanceData.AllowanceId,
				AllowanceName:          allowanceData.AllowanceName,
				AllowanceType:          allowanceData.AllowanceType,
				AttractsSuperannuation: allowanceData.AttractsSuperannuation,
				BaseAmount:             allowanceData.BaseAmount,
				Amount:                 allowanceData.Amount,
			}
			jobAllowances = append(jobAllowances, allowance)
		}
	}

	// 批量創建津貼記錄
	if len(jobAllowances) > 0 {
		if err = db.Create(&jobAllowances).Error; err != nil {
			return err
		}
	}

	return nil
}

// 更新工作福利
func (s *jobService) UpdateJobBenefitItems(db *gorm.DB, facilityId uint64, jobId uint64, benefits string) error {
	var err error
	// 先刪除原有的工作福利
	if err = db.Where("facility_id = ? AND job_id = ?", facilityId, jobId).Delete(&model.JobBenefit{}).Error; err != nil {
		return err
	}
	benefitIds := xtool.SplitStringToUint64(benefits)
	if len(benefitIds) == 0 {
		return nil
	}

	// 創建新的工作福利
	var jobBenefits []model.JobBenefit
	for _, id := range benefitIds {
		jobBenefit := model.JobBenefit{
			FacilityId: facilityId,
			JobId:      jobId,
			BenefitId:  id,
		}
		jobBenefits = append(jobBenefits, jobBenefit)
	}
	if err = db.Create(&jobBenefits).Error; err != nil {
		return err
	}
	return nil
}

// 創建工作職位請求
type JobCreateReq struct {
	Draft               string          `json:"draft" binding:"omitempty,oneof=Y N"`                                                                                                     // 是否為草稿 Y N
	FacilityId          uint64          `json:"facilityId" binding:"required"`                                                                                                           // 所屬機構Id
	PositionProfession  string          `json:"positionProfession" binding:"required,max=255,oneof=MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER"`           // 職位專業 MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER
	NumberOfPeople      int32           `json:"numberOfPeople" binding:"required,min=1"`                                                                                                 // 所需人數
	ServiceLocationId   uint64          `json:"serviceLocationId" binding:"required"`                                                                                                    // 服務地點Id
	MinExperienceLevel  string          `json:"minExperienceLevel" binding:"required_if=PositionProfession MEDICAL_PRACTITIONER,required_if=PositionProfession REGISTERED_NURSE,max=32"` // 最低職級要求
	PreferredGrade      string          `json:"preferredGrade" binding:"required_if=PositionProfession MEDICAL_PRACTITIONER,max=32"`                                                     // 首選級別 selection_type PREFERRED_GRADE(Profession = Medical Practitioner) 其他專業不填
	Qualification       string          `json:"qualification" binding:"required_if=PositionProfession PERSONAL_CARE_WORKER,max=1024"`                                                    // 護理資格 selection_type PROFESSIONAL_PERSONAL_CARE_WORKER_QUALIFICATION(Profession = Personal Care Worker)
	Specialisation      string          `json:"specialisation" binding:"required,max=64"`                                                                                                // 專業要求
	Language            string          `json:"language" binding:"omitempty,max=255"`                                                                                                    // 語言要求 多個以逗號分隔 selection_type LANGUAGE
	LanguageRequirement string          `json:"languageRequirement" binding:"omitempty,oneof=Y N"`                                                                                       // 語言要求類型 Y=必須 N=可選
	SupervisionLevel    string          `json:"supervisionLevel" binding:"omitempty,oneof=FULLY_SUPERVISED PARTIALLY_SUPERVISED NO_SUPERVISION"`                                         // 監督級別 FULLY_SUPERVISED PARTIALLY_SUPERVISED NO_SUPERVISION
	GrandTotal          decimal.Decimal `json:"grandTotal" binding:"required"`                                                                                                           // 累計
	SplitType           string          `json:"splitType" binding:"required,oneof=NO DATE SHIFT"`                                                                                        // 分拆 NO DATE SHIFT

	Benefits            string          `json:"benefits" binding:"omitempty,max=1024"`                                                                                                   // 福利 多個以逗號分隔
	ShiftAllocation     string          `json:"shiftAllocation" binding:"required,oneof=AUTOMATIC MANUAL"`                                                                               // 班次分配方式
	Remark              string          `json:"remark" binding:"omitempty,max=1024"`                                                                                                     // 備註
	JobShiftItems       []JobShiftItem  `json:"jobShiftItems" binding:"required,min=1,dive"`                                                                                             // 班次時間
	PublishTime         *time.Time      `json:"publishTime" binding:"required_if=PublishNow N,required_without=PublishNow"`                                                              // 發佈時間
	PublishNow          string          `json:"publishNow" binding:"omitempty,oneof=Y N"`                                                                                                // 立即發佈 Y N
	CreatedUserId       uint64          `json:"-"`                                                                                                                                       // 創建者Id
	UpdatedUserId       uint64          `json:"-"`                                                                                                                                       // 更新者Id
	JobScheduleId       uint64          `json:"-"`                                                                                                                                       // 工作發佈計劃Id
	ScheduleTemplate    string          `json:"-"`                                                                                                                                       // 更新者Id
}

// 創建工作職位草稿請求
type JobCreateDraftReq struct {
	Draft               string          `json:"draft" binding:"required,oneof=Y N"`                                                                                            // 是否為草稿 Y N
	FacilityId          uint64          `json:"facilityId" binding:"required"`                                                                                                 // 所屬機構Id
	PositionProfession  string          `json:"positionProfession" binding:"required,max=255,oneof=MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER"` // 職位專業 MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER
	NumberOfPeople      int32           `json:"numberOfPeople" binding:"omitempty,min=1"`                                                                                      // 所需人數
	ServiceLocationId   uint64          `json:"serviceLocationId" binding:"omitempty"`                                                                                         // 服務地點Id
	MinExperienceLevel  string          `json:"minExperienceLevel" binding:"omitempty,max=32"`                                                                                 // 最低職級要求
	PreferredGrade      string          `json:"preferredGrade" binding:"omitempty,max=32"`                                                                                     // 首選級別 selection_type PREFERRED_GRADE(Profession = Medical Practitioner) 其他專業不填
	Qualification       string          `json:"qualification" binding:"omitempty,max=1024"`                                                                                    // 護理資格 selection_type PROFESSIONAL_PERSONAL_CARE_WORKER_QUALIFICATION(Profession = Personal Care Worker)
	Specialisation      string          `json:"specialisation" binding:"omitempty,max=64"`                                                                                     // 專業要求
	Language            string          `json:"language" binding:"omitempty,max=255"`                                                                                          // 語言要求 多個逗號分割 selection_type LANGUAGE
	LanguageRequirement string          `json:"languageRequirement" binding:"omitempty,oneof=Y N"`                                                                             // 語言要求類型 Y=必須 N=可選
	SupervisionLevel    string          `json:"supervisionLevel" binding:"omitempty,oneof=FULLY_SUPERVISED PARTIALLY_SUPERVISED NO_SUPERVISION"`                               // 監督級別 FULLY_SUPERVISED PARTIALLY_SUPERVISED NO_SUPERVISION
	GrandTotal          decimal.Decimal `json:"grandTotal" binding:"omitempty"`                                                                                                // 累計
	SplitType           string          `json:"splitType" binding:"omitempty,oneof=NO DATE SHIFT"`                                                                             // 分拆 NO DATE SHIFT

	Benefits            string          `json:"benefits" binding:"omitempty,max=1024"`                                                                                         // 福利
	ShiftAllocation     string          `json:"shiftAllocation" binding:"omitempty,oneof=AUTOMATIC MANUAL"`                                                                    // 班次分配方式
	Remark              string          `json:"remark" binding:"omitempty,max=1024"`                                                                                           // 備註
	JobShiftItems       []JobShiftItem  `json:"jobShiftItems" binding:"omitempty,dive"`                                                                                        // 班次時間
	PublishTime         *time.Time      `json:"publishTime" binding:"omitempty"`                                                                                               // 發佈時間
	PublishNow          string          `json:"publishNow" binding:"omitempty,oneof=Y N"`                                                                                      // 立即發佈 Y N
	CreatedUserId       uint64          `json:"-"`                                                                                                                             // 創建者Id
	UpdatedUserId       uint64          `json:"-"`                                                                                                                             // 更新者Id
	JobScheduleId       uint64          `json:"-"`                                                                                                                             // 工作發佈計劃Id
	ScheduleTemplate    string          `json:"-"`                                                                                                                             // 更新者Id
}

// 班次時間
type JobShiftItem struct {
	JobId            uint64            `json:"-"`                                            // 工作職位Id
	JobShiftId       uint64            `json:"jobShiftId"`                                   // 工作班次Id 如果為0則為新增
	BeginTime        *time.Time        `json:"beginTime" binding:"omitempty"`                // 工作開始時間 (UTC 2006-01-02T15:04:05Z)
	EndTime          *time.Time        `json:"endTime" binding:"omitempty"`                  // 工作結束時間 (UTC 2006-01-02T15:04:05Z)
	Duration         decimal.Decimal   `json:"duration"`                                     // 總時長（小時）
	BreakDuration    decimal.Decimal   `json:"breakDuration"`                                // 休息時間（小時）
	PayHours         decimal.Decimal   `json:"payHours"`                                     // 支付時長（小時）
	ShiftPeriod      string            `json:"shiftPeriod"`                                  // 班次時間段
	HourlyRate       decimal.Decimal   `json:"hourlyRate"`                                   // 時薪
	AllowanceAmount  decimal.Decimal   `json:"allowanceAmount"`                              // 津貼總金額
	BreakTimePayable string            `json:"breakTimePayable" binding:"required,oneof=Y N"` // 休息時間是否支付薪酬 Y N
	Allowances       []JobAllowanceReq `json:"allowances" binding:"omitempty,dive" gorm:"-"` // 津貼設定
}

// 工作津貼請求
type JobAllowanceReq struct {
	AllowanceId            uint64          `json:"allowanceId" binding:"required"`                          // 津貼Id
	AllowanceName          string          `json:"allowanceName" binding:"required"`                        // 津貼名稱
	AllowanceType          string          `json:"allowanceType" binding:"required,oneof=HOURLY,SHIFT,JOB"` // 津貼類型
	Amount                 decimal.Decimal `json:"-"`                                                       // 計算後的津貼金額（內部使用，不從前端傳入）
	AttractsSuperannuation string          `json:"-"`                                                       // 納入退休金 Y N (從數據庫獲取，不從前端傳入)
	BaseAmount             decimal.Decimal `json:"baseAmount" binding:"required,min=0"`                     // 原始津貼金額（前端傳入，用於計算）
}

// 創建工作職位響應
type JobCreateResp struct {
	JobIds []uint64 `json:"jobIds"` // 工作職位Id
}

// 創建工作職位
func (s *jobService) Create(db *gorm.DB, req JobCreateReq) (JobCreateResp, error) {
	var err error
	var resp JobCreateResp
	if req.ScheduleTemplate == model.JobScheduleTemplateY {
		// 先鎖定此機構JobSchedule，用於生成JobNo(模板編號用戶不可見)
		err = JobScheduleService.LockFacilityJobScheduleRecord(db, req.FacilityId)
	} else {
		// 先鎖定此機構Job，用於生成JobNo
		err = s.LockFacilityJobRecord(db, req.FacilityId)
	}
	if err != nil {
		return resp, err
	}

	facilityProfile, err := FacilityProfileService.GetCurrentFacilityProfile(db, req.FacilityId)
	if err != nil {
		return resp, err
	}

	var serviceLocation model.ServiceLocation
	var timezone string
	if req.ServiceLocationId != 0 {
		if err = db.First(&serviceLocation, req.ServiceLocationId).Error; err != nil {
			return resp, err
		}
		timezone = serviceLocation.Timezone
	}

	var jobShiftItemGroups [][]JobShiftItem
	if req.Draft == "Y" {
		jobShiftItemGroups = [][]JobShiftItem{req.JobShiftItems}
	} else {
		jobShiftItemGroups, err = s.SplitJobShiftItems(req.SplitType, req.JobShiftItems, timezone)
		if err != nil {
			return resp, err
		}
	}

	for _, jobShiftItems := range jobShiftItemGroups {
		var m model.Job
		_ = copier.Copy(&m, req)
		m.BeginTime, m.EndTime = s.GetShiftTimeRange(jobShiftItems)
		m.Duration = decimal.Zero
		m.PayHours = decimal.Zero
		if m.BeginTime != nil && m.EndTime != nil {
			m.Duration, _, m.PayHours, err = s.GetShiftDuration(jobShiftItems)
			if err != nil {
				return resp, err
			}
		}

		nowTime := time.Now().UTC().Truncate(time.Second)
		m.CreateTime = nowTime
		m.UpdateTime = nil
		m.PaymentTerms = facilityProfile.PaymentTerms
		m.FacilityProfileId = facilityProfile.Id
		if req.ScheduleTemplate == "" {
			m.ScheduleTemplate = model.JobScheduleTemplateN
		}

		if req.Draft == "Y" {
			m.Status = model.JobStatusPending
			m.SplitType = req.SplitType
			m.PublishTime = req.PublishTime
		} else {
			m.Status = model.JobStatusPublish
			m.SplitType = model.JobSplitTypeNo
			if req.PublishNow == "Y" {
				m.PublishTime = &nowTime
			} else {
				m.PublishTime = req.PublishTime
			}
		}
		// 生成工作編號/工作排程編號
		if req.ScheduleTemplate == model.JobScheduleTemplateY {
			m.JobNo, err = s.GenerateJobScheduleNo(db, req.FacilityId, timezone)
		} else {
			m.JobNo, err = s.GenerateJobNo(db, req.FacilityId, timezone)
		}
		if err != nil {
			return resp, err
		}

		// 生成ShiftTimeType
		var shiftTimeType, weekdayType string
		shiftTimeType, weekdayType, err = s.GenerateShiftTimeAndWeekdays(db, req.FacilityId, jobShiftItems, timezone)
		if err != nil {
			return resp, err
		}
		m.ShiftTimeType = shiftTimeType
		m.WeekdayType = weekdayType

		// 計算並設置最低和最高時薪
		m.MinHourlyRate, m.MaxHourlyRate = s.CalculateHourlyRateRange(jobShiftItems)

		if err = db.Create(&m).Error; err != nil {
			return resp, err
		}

		if err = s.UpdateJobShiftItems(db, req.FacilityId, m.Id, jobShiftItems); err != nil {
			return resp, err
		}
		if err = s.UpdateJobBenefitItems(db, req.FacilityId, m.Id, req.Benefits); err != nil {
			return resp, err
		}
		resp.JobIds = append(resp.JobIds, m.Id)
	}

	return resp, nil
}

// 生成ShiftTimeType
// 將工作所涵蓋的 AM PM Night 用逗號拼接,要按順序 AM,PM,NIGHT
func (s *jobService) GenerateShiftTimeAndWeekdays(db *gorm.DB, facilityId uint64, jobShiftItems []JobShiftItem, timezone string) (string, string, error) {
	var shiftTimes []model.ShiftTime
	if err := db.Where("facility_id = ?", facilityId).Find(&shiftTimes).Error; err != nil {
		return "", "", err
	}

	// 收集所有班次的時間類型
	allShiftTimeTypes := make([]string, 0)
	allWeekdayTypes := make([]string, 0)
	for _, jobShiftItem := range jobShiftItems {
		if jobShiftItem.BeginTime == nil || jobShiftItem.EndTime == nil || timezone == "" {
			continue
		}
		shiftTimeTypes, weekdayTypes, err := s.GetShiftTimeTypeWeekdayType(shiftTimes, jobShiftItem, timezone)
		if err != nil {
			return "", "", err
		}
		allShiftTimeTypes = append(allShiftTimeTypes, shiftTimeTypes...)
		allWeekdayTypes = append(allWeekdayTypes, weekdayTypes...)
	}

	// 去重
	allShiftTimeTypes = lo.Uniq(allShiftTimeTypes)
	allWeekdayTypes = lo.Uniq(allWeekdayTypes)

	// 創建一個映射，記錄每個ShiftTime.Name在原始shiftTimes中的位置
	nameOrderMap := make(map[string]int)
	for i, st := range shiftTimes {
		nameOrderMap[st.Name] = i
	}

	// 按照shiftTimes中的順序排序
	sort.Slice(allShiftTimeTypes, func(i, j int) bool {
		// 按照原始shiftTimes的順序排序
		return nameOrderMap[allShiftTimeTypes[i]] < nameOrderMap[allShiftTimeTypes[j]]
	})
	// allWeekdayTypes 從小到大排序
	sort.Slice(allWeekdayTypes, func(i, j int) bool {
		return allWeekdayTypes[i] < allWeekdayTypes[j]
	})

	// 將結果組合成字符串
	shiftTimeType := strings.Join(allShiftTimeTypes, ",")
	weekdayType := strings.Join(allWeekdayTypes, ",")
	return shiftTimeType, weekdayType, nil
}

// 獲取班次時間類型和星期類型
func (s *jobService) GetShiftTimeTypeWeekdayType(shiftTimes []model.ShiftTime, jobShiftItem JobShiftItem, timezone string) ([]string, []string, error) {
	shiftTimeTypes := make([]string, 0)
	weekdayTypes := make([]string, 0)

	// 轉換時區
	tz, err := time.LoadLocation(timezone)
	if err != nil {
		return nil, nil, err
	}

	beginTime := jobShiftItem.BeginTime.In(tz)
	endTime := jobShiftItem.EndTime.In(tz)

	beginDate := beginTime.Format(xtool.DateDayA)
	nextDate := beginTime.AddDate(0, 0, 1).Format(xtool.DateDayA)

	// 完全匹配ShiftTime 1個
	// 部分匹配ShiftTime 多個
	matchShiftTime := make([]string, 0)
	partialMatchShiftTime := make([]string, 0)

	for _, shiftTime := range shiftTimes {
		var shiftTimeBeginTime, shiftTimeEndTime1, shiftTimeEndTime2 time.Time
		if shiftTime.BeginTime != "" {
			shiftTimeBeginTime, err = time.ParseInLocation(xtool.DateTimeSecA1, fmt.Sprintf("%s %s:00", beginDate, shiftTime.BeginTime), tz)
			if err != nil {
				return nil, nil, err
			}
		}
		if shiftTime.EndTime1 != "" {
			if shiftTime.NextDay1 == "Y" {
				shiftTimeEndTime1, err = time.ParseInLocation(xtool.DateTimeSecA1, fmt.Sprintf("%s %s:00", nextDate, shiftTime.EndTime1), tz)
			} else {
				shiftTimeEndTime1, err = time.ParseInLocation(xtool.DateTimeSecA1, fmt.Sprintf("%s %s:00", beginDate, shiftTime.EndTime1), tz)
			}
			if err != nil {
				return nil, nil, err
			}
		}
		if shiftTime.EndTime2 != "" {
			if shiftTime.NextDay2 == "Y" {
				shiftTimeEndTime2, err = time.ParseInLocation(xtool.DateTimeSecA1, fmt.Sprintf("%s %s:00", nextDate, shiftTime.EndTime2), tz)
			} else {
				shiftTimeEndTime2, err = time.ParseInLocation(xtool.DateTimeSecA1, fmt.Sprintf("%s %s:00", beginDate, shiftTime.EndTime2), tz)
			}
			if err != nil {
				return nil, nil, err
			}
		}

		// 規則
		// 一、無shiftTime.EndTime2
		if shiftTime.EndTime2 == "" {
			// 1. beginTime - endTime 在 shiftTimeBeginTime - shiftTimeEndTime1 之間 完全匹配
			// 使用 !Before (大於等於) 和 !After (小於等於) 來實現正確的比較
			if !beginTime.Before(shiftTimeBeginTime) && !endTime.After(shiftTimeEndTime1) {
				matchShiftTime = append(matchShiftTime, shiftTime.Name)
			} else if beginTime.Before(shiftTimeEndTime1) && endTime.After(shiftTimeBeginTime) {
				// 2. beginTime - endTime 部分時間在 shiftTimeBeginTime - shiftTimeEndTime1 之間 部分匹配
				// 檢查是否有重疊部分
				partialMatchShiftTime = append(partialMatchShiftTime, shiftTime.Name)
			}
		} else {
			// 二、有shiftTime.EndTime2
			// 1. beginTime 在 shiftTimeBeginTime - shiftTimeEndTime1 之間 並且 endTime 在 shiftTimeEndTime1 - shiftTimeEndTime2 之間 完全匹配
			// 使用 !Before (大於等於) 和 !After (小於等於) 來實現正確的比較
			if !beginTime.Before(shiftTimeBeginTime) && !beginTime.After(shiftTimeEndTime1) &&
				!endTime.Before(shiftTimeEndTime1) && !endTime.After(shiftTimeEndTime2) {
				matchShiftTime = append(matchShiftTime, shiftTime.Name)
			} else if (!endTime.Before(shiftTimeEndTime1) && !beginTime.After(shiftTimeEndTime2)) ||
				(beginTime.Before(shiftTimeEndTime2) && endTime.After(shiftTimeEndTime1)) {
				// 2. beginTime或endTime 在 shiftTimeEndTime1 - shiftTimeEndTime2 之間 部分匹配
				// 修正為檢查是否有重疊部分
				partialMatchShiftTime = append(partialMatchShiftTime, shiftTime.Name)
			} else if beginTime.Before(shiftTimeEndTime1) && endTime.After(shiftTimeBeginTime) {
				// 3. 檢查是否有其他部分匹配的情況
				// 檢查與第一段時間是否有重疊
				partialMatchShiftTime = append(partialMatchShiftTime, shiftTime.Name)
			}
		}
	}

	// 優先使用完全匹配的結果，如果沒有則使用部分匹配的結果
	if len(matchShiftTime) > 0 {
		shiftTimeTypes = append(shiftTimeTypes, matchShiftTime...)
	} else if len(partialMatchShiftTime) > 0 {
		shiftTimeTypes = append(shiftTimeTypes, partialMatchShiftTime...)
	}

	// 去重
	shiftTimeTypes = lo.Uniq(shiftTimeTypes)

	// 獲取beginTime - endTime 的所有weekday, 1-7
	for beginTime.Before(endTime) {
		weekdayTypes = append(weekdayTypes, fmt.Sprintf("%d", ISOWeekday(beginTime.Weekday())))
		weekdayTypes = append(weekdayTypes, fmt.Sprintf("%d", ISOWeekday(endTime.Weekday())))
		beginTime = beginTime.AddDate(0, 0, 1)
	}
	// 去重
	weekdayTypes = lo.Uniq(weekdayTypes)

	return shiftTimeTypes, weekdayTypes, nil
}

// 工作職位列表請求
type JobListReq struct {
	FacilityId         uint64 `form:"facilityId" binding:"required"`                                                                 // 所屬機構Id
	PositionProfession string `form:"positionProfession" binding:"omitempty,max=255"`                                                // 職位專業
	ServiceLocationId  uint64 `form:"serviceLocationId"`                                                                             // 服務地點Id
	JobNo              string `form:"jobNo"`                                                                                         // 工作編號
	BeginTime          string `form:"beginTime"`                                                                                     // 工作開始時間
	EndTime            string `form:"endTime"`                                                                                       // 工作結束時間
	JobScheduleId      uint64 `form:"jobScheduleId"`                                                                                 // 工作排程Id
	Progress           string `form:"progress" binding:"omitempty,oneof=PENDING UPCOMING IN_PROGRESS COMPLETE CANCEL"`               // 進度 PENDING UPCOMING IN_PROGRESS COMPLETE CANCEL
	Status             string `form:"status" binding:"omitempty,splitin=PENDING PUBLISH DISABLE CANCEL COMPLETE"`                    // 職位狀態 PENDING PUBLISH DISABLE CANCEL COMPLETE
	JobCategory        string `form:"jobCategory" binding:"omitempty,oneof=DRAFT OPEN UPCOMING IN_PROGRESS COMPLETE WAITING CANCEL"` // 工作分類 DRAFT=草稿 OPEN=招聘中 UPCOMING=待開始 IN_PROGRESS=進行中 COMPLETE=已完成 WAITING=待開始 CANCEL=已取消
	HiringStatus       string `form:"hiringStatus" binding:"omitempty,oneof=COMPLETE PARTIALLY_COMPLETE INCOMPLETE"`                 // 招聘狀態 COMPLETE=已完成 PARTIALLY_COMPLETE=部分完成 INCOMPLETE=未完成
}

// JobListResp 工作職位列表響應
type JobListResp struct {
	JobId                  uint64                 `json:"jobId"`                                // 工作職位Id
	FacilityId             uint64                 `json:"facilityId"`                           // 所屬機構Id
	JobScheduleId          uint64                 `json:"jobScheduleId,omitempty"`              // 工作排程Id
	JobNo                  string                 `json:"jobNo"`                                // 工作編號
	PositionProfession     string                 `json:"positionProfession"`                   // 職位專業
	NumberOfPeople         int32                  `json:"numberOfPeople"`                       // 所需人數
	ServiceLocationId      uint64                 `json:"serviceLocationId"`                    // 服務地點Id
	ServiceLocationAddress string                 `json:"serviceLocationAddress"`               // 服務地點地址
	Timezone               string                 `json:"timezone"`                             // 服務地點時區
	MinExperienceLevel     string                 `json:"minExperienceLevel"`                   // 最低職級要求
	PreferredGrade         string                 `json:"preferredGrade"`                       // 首選級別
	Qualification          string                 `json:"qualification"`                        // 護理資格
	Specialisation         string                 `json:"specialisation"`                       // 專業要求
	Language               string                 `json:"language"`                             // 語言要求
	SupervisionLevel       string                 `json:"supervisionLevel"`                     // 監督級別
	MinHourlyRate          decimal.Decimal        `json:"minHourlyRate"`                        // 最低時薪
	MaxHourlyRate          decimal.Decimal        `json:"maxHourlyRate"`                        // 最高時薪
	Benefits               string                 `json:"benefits"`                             // 福利
	ShiftAllocation        string                 `json:"shiftAllocation"`                      // 班次分配方式
	Remark                 string                 `json:"remark"`                               // 備註
	CancelReason           string                 `json:"cancelReason"`                         // 取消原因
	Status                 string                 `json:"status"`                               // 職位狀態 PENDING PUBLISH IN_PROGRESS COMPLETE CANCEL
	BeginTime              *time.Time             `json:"beginTime"`                            // 工作開始時間
	EndTime                *time.Time             `json:"endTime"`                              // 工作結束時間
	JobShiftItems          []JobShiftItem         `json:"jobShiftItems" gorm:"-"`               // 班次時間
	ApplicantInfo          []JobListApplicantInfo `json:"applicantInfo" gorm:"-"`               // 申請人信息
	ApplicantCount         int32                  `json:"applicantCount"`                       // 申請人數
	AcceptedCount          int32                  `json:"acceptedCount"`                        // 確認人數
	Progress               string                 `json:"progress,omitempty" gorm:"-"`          // 進度 PENDING=待發佈(草稿) UPCOMING=待開始 IN_PROGRESS=進行中 COMPLETE=已完成 CANCEL=已取消
	JobCategory            string                 `json:"jobCategory,omitempty" gorm:"-"`       // 工作分類 DRAFT=草稿 OPEN=招聘中 UPCOMING=待開始 IN_PROGRESS=進行中 COMPLETE=已完成 WAITING=待開始 CANCEL=已取消
	ProfessionalNames      string                 `json:"professionalNames,omitempty" gorm:"-"` // 專業人士姓名
	RelatedDocumentIds     string                 `json:"relatedDocumentIds"`
	RelatedInvoiceIds      string                 `json:"relatedInvoiceIds"`
	PublishTime            *time.Time             `json:"-"` // 發佈時間
}

// calculateJobProgress 根據狀態和時間計算工作進度
func calculateJobProgress(status string, beginTime, endTime time.Time, timezone string) string {
	if timezone == "" {
		return ""
	}
	tz, err := time.LoadLocation(timezone)
	if err != nil {
		return ""
	}
	nowTime := time.Now().In(tz)

	switch status {
	case model.JobStatusPending:
		return JobProgressPending
	case model.JobStatusPublish:
		// 如果開始時間大於當前時間，則為即將開始
		if beginTime.After(nowTime) {
			return JobProgressUpcoming
		} else if endTime.After(nowTime) {
			// 如果結束時間大於當前時間，則為進行中
			return JobProgressInProgress
		} else {
			// 如果結束時間小於當前時間，則為已完成
			return JobProgressComplete
		}
	case model.JobStatusComplete:
		return JobProgressComplete
	case model.JobStatusCancel:
		return JobProgressCancel
	default:
		// 對於其他狀態（如DISABLE），根據時間判斷
		if beginTime.After(nowTime) {
			return JobProgressUpcoming
		} else if endTime.After(nowTime) {
			return JobProgressInProgress
		} else {
			return JobProgressComplete
		}
	}
}

// 更新工作進度
func (r *JobListResp) UpdateProgress(timezone string) {
	if r.BeginTime == nil || r.EndTime == nil || timezone == "" {
		r.Progress = ""
		return
	}
	r.Progress = calculateJobProgress(r.Status, *r.BeginTime, *r.EndTime, timezone)
}

// 更新工作分類
func (r *JobListResp) UpdateJobCategory(db *gorm.DB) error {
	var err error
	if r.BeginTime == nil || r.EndTime == nil {

	}
	m := model.Job{
		Id:             r.JobId,
		FacilityId:     r.FacilityId,
		Status:         r.Status,
		BeginTime:      r.BeginTime,
		EndTime:        r.EndTime,
		NumberOfPeople: r.NumberOfPeople,
		PublishTime:    r.PublishTime,
	}
	r.JobCategory, err = JobService.GetJobCategory(db, m, r.Timezone)
	if err != nil {
		return err
	}
	return nil
}

// 拆分工作班次
func (s *jobService) SplitJobShiftItems(splitType string, jobShiftItems []JobShiftItem, timezone string) ([][]JobShiftItem, error) {
	var result [][]JobShiftItem

	// 如果沒有班次項目，返回空結果
	if len(jobShiftItems) == 0 {
		return result, nil
	}
	tz, err := time.LoadLocation(timezone)
	if err != nil {
		return nil, err
	}

	switch splitType {
	case model.JobSplitTypeNo:
		// 不拆分 - 所有班次合併為一個工作
		result = append(result, jobShiftItems)

	case model.JobSplitTypeDate:
		// 按日期拆分 - 同一天的班次合併為一個工作
		dateGroups := make(map[string][]JobShiftItem)
		var dateOrder []string           // 用於記錄日期的出現順序
		var crossDayItems []JobShiftItem // 用於存儲跨天的班次

		for _, item := range jobShiftItems {
			// 如果開始或結束時間為空，則跳過
			if item.BeginTime == nil || item.EndTime == nil {
				return nil, errors.New("beginTime or endTime is empty")
			}

			// 解析開始時間獲取日期部分
			beginTime := (*item.BeginTime).In(tz)
			endTime := (*item.EndTime).In(tz)

			beginDate := beginTime.Format(xtool.DateDayA)
			endDate := endTime.Format(xtool.DateDayA)

			// 如果開始日期和結束日期相同，則添加到該日期的組中
			if beginDate == endDate {
				// 如果這是第一次看到這個日期，記錄下來
				if _, exists := dateGroups[beginDate]; !exists {
					dateOrder = append(dateOrder, beginDate)
				}
				dateGroups[beginDate] = append(dateGroups[beginDate], item)
			} else {
				// 如果開始日期和結束日期不同，則作為單獨的班次
				crossDayItems = append(crossDayItems, item)
			}
		}

		// 按照日期的原始順序添加分组
		for _, date := range dateOrder {
			items := dateGroups[date]
			if len(items) > 0 {
				result = append(result, items)
			}
		}

		// 添加跨天的班次，每個作為單獨一組
		for _, item := range crossDayItems {
			result = append(result, []JobShiftItem{item})
		}

	case model.JobSplitTypeShift:
		// 按班次拆分 - 每個班次作為單獨的工作
		for _, item := range jobShiftItems {
			if item.BeginTime != nil && item.EndTime != nil {
				result = append(result, []JobShiftItem{item})
			}
		}

	default:
		// 默認不拆分
		return nil, errors.New("splitType is invalid")
	}

	return result, nil
}

type JobListApplicantInfo struct {
	JobId                   uint64 `json:"-"`                       // 工作Id
	JobApplicationId        uint64 `json:"jobApplicationId"`        // 工作申請人Id
	ProfessionalId          uint64 `json:"professionalId"`          // 專業人士Id
	ProfessionalName        string `json:"professionalName"`        // 專業人士姓名
	ProfessionalPhotoFileId uint64 `json:"professionalPhotoFileId"` // 專業人士照片文件Id
}

type JobApplicantName struct {
	JobId             uint64 // 工作Id
	ProfessionalNames string // 專業人士姓名
}

// 獲取工作職位列表
func (s *jobService) List(db *gorm.DB, req JobListReq, pageSet *xresp.PageSet, sortSet xresp.SortingSet, forTarget string) ([]JobListResp, error) {
	var err error
	var resp []JobListResp

	// 篩選data_type
	notDataType := model.DocumentDataTypeSystemToFacility
	if forTarget == "FACILITY" {
		notDataType = model.DocumentDataTypeProfessionalToSystem
	}
	subBuilder := db.
		Table("document AS sub_d").
		Joins("JOIN job_application AS sub_ja ON sub_ja.id = sub_d.job_application_id").
		Select([]string{
			"sub_ja.job_id",
			fmt.Sprintf("GROUP_CONCAT(DISTINCT IF(sub_d.category = '%s', sub_d.id, NULL) SEPARATOR ', ') AS related_document_ids", model.DocumentCategoryConfirmation),
			fmt.Sprintf("GROUP_CONCAT(DISTINCT IF(sub_d.category = '%s' AND sub_d.data_type <> '%s', sub_d.id, NULL) SEPARATOR ', ') AS related_invoice_ids", model.DocumentCategoryInvoice, notDataType),
		}).
		Where("sub_d.progress = ? OR sub_d.progress = ?", model.DocumentProgressSent, model.DocumentProgressConfirm). // 只出提交和確認的賬單
		Group("sub_ja.job_id")

	builder := db.Table("job AS j").
		Joins("LEFT JOIN service_location AS sl ON j.service_location_id = sl.id").
		Joins("LEFT JOIN job_application AS ja ON j.id = ja.job_id AND ja.facility_id = j.facility_id AND ja.deleted <> ?", model.JobApplicationDeletedY).
		Joins("LEFT JOIN job_benefit AS jb ON j.id = jb.job_id AND j.facility_id = jb.facility_id").
		Joins("LEFT JOIN benefit AS b ON jb.benefit_id = b.id").
		Joins("LEFT JOIN (?) AS d ON d.job_id = j.id", subBuilder).
		Select([]string{
			"j.id AS job_id",
			"j.facility_id",
			"j.job_schedule_id",
			"j.job_no",
			"j.position_profession",
			"j.number_of_people",
			"j.service_location_id",
			"sl.timezone",
			"IF(sl.address_extra IS NOT NULL AND sl.address_extra != '', CONCAT(sl.address_extra, ' ', sl.address), sl.address) AS service_location_address",
			"j.min_experience_level",
			"j.preferred_grade",
			"j.qualification",
			"j.specialisation",
			"j.language",
			"j.supervision_level",
			"j.min_hourly_rate",
			"j.max_hourly_rate",
			"j.shift_allocation",
			"j.remark",
			"j.cancel_reason",
			"j.status",
			"j.begin_time",
			"j.end_time",
			"j.publish_time",
			"jac.applicant_count",
			"jac.accepted_count",
			"GROUP_CONCAT(DISTINCT CASE WHEN b.name IS NOT NULL AND b.name != '' THEN b.name ELSE NULL END SEPARATOR ',') AS benefits",
			"IFNULL(d.related_document_ids, '') AS related_document_ids",
			"IFNULL(d.related_invoice_ids, '') AS related_invoice_ids",
		}).
		Where("j.facility_id = ?", req.FacilityId).
		Where("j.schedule_template = ?", model.JobScheduleTemplateN)

	if req.JobNo != "" {
		builder = builder.Where("j.job_no LIKE ?", "%"+req.JobNo+"%")
	}

	if req.PositionProfession != "" {
		builder = builder.Where("j.position_profession LIKE ?", "%"+req.PositionProfession+"%")
	}
	if req.ServiceLocationId != 0 {
		builder = builder.Where("j.service_location_id = ?", req.ServiceLocationId)
	}
	if req.BeginTime != "" {
		builder = builder.Where("j.end_time >= ?", req.BeginTime)
	}
	if req.EndTime != "" {
		builder = builder.Where("j.begin_time <= ?", req.EndTime)
	}
	if req.JobScheduleId != 0 {
		builder = builder.Where("j.job_schedule_id = ?", req.JobScheduleId)
	}

	// 根據Status進行篩選
	if req.Status != "" {
		statusList := strings.Split(req.Status, ",")
		builder = builder.Where("j.status IN (?)", statusList)
	}

	hasJacSelect := false

	acceptedCountBuilder := db.Table("job_application AS ja").
		Select([]string{
			"ja.job_id",
			"ja.facility_id",
			"COUNT(ja.id) AS applicant_count",
			"SUM(CASE WHEN ja.accept = 'Y' THEN 1 ELSE 0 END) AS accepted_count",
		}).
		Where("ja.facility_id = ?", req.FacilityId).
		Where("ja.deleted <> ?", model.JobApplicationDeletedY).
		Where("ja.status NOT IN (?)", []string{model.JobApplicationStatusProfessionalCancel, model.JobApplicationStatusFacilityCancel, model.JobApplicationStatusApplicationCancel}).
		Group("ja.job_id")

	nowTime := time.Now().UTC().Truncate(time.Second)
	nowStr := nowTime.Format(xtool.DateTimeSecA1)
	// 根據JobCategory進行篩選
	if req.JobCategory != "" {
		switch req.JobCategory {
		case JobCategoryDraft: // 草稿
			builder = builder.Where("j.status = ?", model.JobStatusPending)
		case JobCategoryOpen: // 招聘中
			// Status: PUBLISH / DISABLE
			// (PublishTime) < Now < (BeginTime - 1h)
			// 未招滿人
			hasJacSelect = true
			builder = builder.Where("j.status IN (?, ?)", model.JobStatusPublish, model.JobStatusDisable).
				Where("j.publish_time <= ?", nowStr).                          // 發佈時間小於當前時間
				Where("DATE_SUB(j.begin_time, INTERVAL 1 HOUR) > ?", nowTime). // 開始時間減1小時後，大於當前時間
				Joins("LEFT JOIN (?) AS jac ON j.id = jac.job_id AND j.facility_id = jac.facility_id", acceptedCountBuilder).
				Where("(jac.accepted_count IS NULL OR jac.accepted_count < j.number_of_people)")
		case JobCategoryUpcoming: // 待開始
			// Status: PUBLISH
			// (BeginTime - 1h) < Now < BeginTime 或 已招滿人
			hasJacSelect = true
			builder = builder.Where("j.status = ?", model.JobStatusPublish).
				Where("j.begin_time > ?", nowStr).
				Joins("LEFT JOIN (?) AS jac ON j.id = jac.job_id AND j.facility_id = jac.facility_id", acceptedCountBuilder).
				Where("(DATE_SUB(j.begin_time, INTERVAL 1 HOUR) <= ?) OR (jac.accepted_count >= j.number_of_people)", nowStr)
		case JobCategoryInProgress: // 進行中
			// Status: PUBLISH
			// BeginTime < Now < EndTime
			builder = builder.Where("j.status = ?", model.JobStatusPublish).
				Where("j.begin_time <= ?", nowStr).
				Where("j.end_time > ?", nowStr)
		case JobCategoryComplete: // 已結束
			// Status: PUBLISH
			// EndTime < Now
			builder = builder.Where("j.status = ?", model.JobStatusPublish).
				Where("j.end_time < ?", nowStr)
		case JobCategoryWaiting: // 等待發佈
			// Status: PUBLISH
			// Now < PublishTime
			builder = builder.Where("j.status = ?", model.JobStatusPublish).
				Where("j.publish_time > ?", nowStr)
		case JobCategoryCancel: // 已取消
			// Status: CANCEL
			builder = builder.Where("j.status = ?", model.JobStatusCancel)
		}
	}

	if req.Progress != "" {
		// TODO：有問題，數據不對
		switch req.Progress {
		case JobProgressPending:
			builder = builder.Where("j.status = ?", model.JobStatusPending)
		case JobProgressUpcoming:
			// status = PUBLISH 並且 begin_time 大於 當前時間
			builder = builder.Where("j.status = ?", model.JobStatusPublish).
				Where("j.begin_time > ?", nowStr)
		case JobProgressInProgress:
			// status = PUBLISH 並且 begin_time 小於 當前時間 並且 end_time 大於 當前時間
			builder = builder.Where("j.status = ?", model.JobStatusPublish).
				Where("j.begin_time < ?", nowStr).
				Where("j.end_time > ?", nowStr)
		case JobProgressComplete:
			// status = COMPLETE 或 status = PUBLISH 並且 end_time 小於 當前時間
			builder = builder.Where("j.status = ? OR (j.status = ? AND j.end_time < ?)", model.JobStatusComplete, model.JobStatusPublish, nowStr)
		case JobProgressCancel:
			builder = builder.Where("j.status = ?", model.JobStatusCancel)
		}
	}

	if !hasJacSelect {
		builder = builder.
			Joins("LEFT JOIN (?) AS jac ON j.id = jac.job_id AND j.facility_id = jac.facility_id", acceptedCountBuilder)
	}
	if req.HiringStatus != "" {
		switch req.HiringStatus {
		case JobHiringStatusComplete:
			builder = builder.Where("jac.accepted_count = j.number_of_people")
		case JobHiringStatusPartiallyComplete:
			builder = builder.Where("jac.accepted_count > 0 AND jac.accepted_count < j.number_of_people")
		case JobHiringStatusIncomplete:
			builder = builder.Where("jac.accepted_count = 0 OR jac.accepted_count IS NULL")
		}
	}

	sortKeyList := map[string]string{
		"hourlyRate": "j.max_hourly_rate", // 最高時薪 降序
		"createTime": "j.create_time",
		"beginTime":  "j.begin_time",
	}

	if err = builder.
		Group("j.id").
		Scopes(xresp.AddOrder(sortSet, sortKeyList)).
		Order("FIELD(j.position_profession, 'MEDICAL_PRACTITIONER', 'ENROLLED_NURSE', 'REGISTERED_NURSE', 'PERSONAL_CARE_WORKER')").
		Order("j.id").
		Scopes(xresp.Paginate(pageSet)).
		Find(&resp).Error; err != nil {
		return resp, err
	}

	jobIds := make([]uint64, 0)
	benefitIds := make([]uint64, 0)
	for _, item := range resp {
		jobIds = append(jobIds, item.JobId)
		if item.Benefits != "" {
			benefitIds = append(benefitIds, xtool.SplitStringToUint64(item.Benefits)...)
		}
	}
	var jobShiftItemsMap map[uint64][]JobShiftItem
	var applicantInfoMap map[uint64][]JobListApplicantInfo
	var professionSectionMap map[string]string
	var experienceLevelSectionMap map[string]string
	var preferredGradeSectionMap map[string]string
	var languageSectionMap map[string]string
	var qualificationSectionMap map[string]string
	var specialisationSectionMap map[string]string
	var supervisionLevelSectionMap map[string]string
	var acceptedApplicantMap map[uint64]string
	if len(resp) > 0 {
		professionSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
		if err != nil {
			return resp, err
		}
		experienceLevelSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeExperienceLevelMedicalPractitioner, model.SelectionTypeExperienceLevelRegisteredNurse})
		if err != nil {
			return resp, err
		}
		preferredGradeSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypePreferredGrade})
		if err != nil {
			return resp, err
		}
		languageSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeLanguage})
		if err != nil {
			return resp, err
		}
		qualificationSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalPersonalCareWorkerQualification})
		if err != nil {
			return resp, err
		}
		var medicalPractitionerSectionCode []string
		medicalPractitionerSectionCode, err = SelectionService.GetCodeByType(db, model.SelectionTypePreferredSpecialtyMedicalPractitioner)
		if err != nil {
			return resp, err
		}
		specialisationSelectionTypes := []string{model.SelectionTypePreferredSpecialtyNurse, model.SelectionTypePreferredSpecialtyPersonalCareWorker}
		specialisationSelectionTypes = append(specialisationSelectionTypes, medicalPractitionerSectionCode...)
		specialisationSectionMap, err = SelectionService.GetCodeNameMap(db, specialisationSelectionTypes)
		if err != nil {
			return resp, err
		}
		supervisionLevelSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeSupervisionRequirement})
		if err != nil {
			return resp, err
		}

		jobShiftItemsMap, err = s.GetJobShiftItems(db, req.FacilityId, jobIds)
		if err != nil {
			return resp, err
		}
		applicantInfoMap, err = s.GetJobApplicantInfo(db, req.FacilityId, jobIds)
		if err != nil {
			return resp, err
		}
		acceptedApplicantMap, err = s.GetJobAcceptedApplicantNames(db, jobIds)
		if err != nil {
			return resp, err
		}
	}
	for i, item := range resp {
		if jobShiftItems, ok := jobShiftItemsMap[item.JobId]; ok {
			resp[i].JobShiftItems = jobShiftItems
		} else {
			resp[i].JobShiftItems = make([]JobShiftItem, 0)
		}
		// 如果工作是自動分配，並且發佈時間在2小時前，則顯示申請人信息
		if item.ShiftAllocation == model.JobShiftAllocationAutomatic && item.PublishTime != nil {
			twoHoursAgo := item.PublishTime.Add(time.Hour * 2)
			if twoHoursAgo.Before(nowTime) {
				if applicantInfo, ok := applicantInfoMap[item.JobId]; ok {
					resp[i].ApplicantInfo = applicantInfo
				}
			}
		} else if item.ShiftAllocation == model.JobShiftAllocationManual {
			if applicantInfo, ok := applicantInfoMap[item.JobId]; ok {
				resp[i].ApplicantInfo = applicantInfo
			}
		}

		if resp[i].ApplicantInfo == nil {
			resp[i].ApplicantInfo = make([]JobListApplicantInfo, 0)
		}

		if acceptedApplicant, ok := acceptedApplicantMap[item.JobId]; ok {
			resp[i].ProfessionalNames = acceptedApplicant
		}

		// 更新Progress字段
		resp[i].UpdateProgress(item.Timezone)

		if err = resp[i].UpdateJobCategory(db); err != nil {
			return resp, err
		}

		if item.PositionProfession != "" {
			resp[i].PositionProfession = professionSectionMap[item.PositionProfession]
		}
		if item.MinExperienceLevel != "" {
			resp[i].MinExperienceLevel = experienceLevelSectionMap[item.MinExperienceLevel]
		}
		if item.PreferredGrade != "" {
			resp[i].PreferredGrade = preferredGradeSectionMap[item.PreferredGrade]
		}
		if item.Specialisation != "" {
			resp[i].Specialisation = specialisationSectionMap[item.Specialisation]
		}
		if item.Language != "" {
			languageList := strings.Split(item.Language, ",")
			var languageNameList []string
			for _, language := range languageList {
				languageNameList = append(languageNameList, languageSectionMap[language])
			}
			resp[i].Language = strings.Join(languageNameList, ",")
		}
		if item.Qualification != "" {
			qualificationList := strings.Split(item.Qualification, ",")
			var qualificationNameList []string
			for _, qualification := range qualificationList {
				qualificationNameList = append(qualificationNameList, qualificationSectionMap[qualification])
			}
			resp[i].Qualification = strings.Join(qualificationNameList, ",")
		}
		if item.SupervisionLevel != "" {
			resp[i].SupervisionLevel = supervisionLevelSectionMap[item.SupervisionLevel]
		}

		// 清理數據
		if req.JobCategory == JobCategoryDraft {
			resp[i].ApplicantCount = 0
			resp[i].JobCategory = ""
			resp[i].Progress = ""
		}
	}
	return resp, nil
}

// 加載工作已接受專業人士姓名
func (s *jobService) GetJobAcceptedApplicantNames(db *gorm.DB, jobIds []uint64) (map[uint64]string, error) {
	var applicantList []JobApplicantName
	if err := db.Table("job_application AS ja").
		Joins("JOIN professional AS p ON ja.professional_id = p.id").
		Select([]string{
			"ja.job_id",
			"GROUP_CONCAT(DISTINCT CONCAT(p.first_name, ' ', p.last_name) SEPARATOR ',') AS professional_names",
		}).
		Where("ja.accept = ?", model.JobApplicationAcceptY).
		Where("ja.status = ?", model.JobApplicationStatusAccept).
		Where("ja.job_id IN (?)", jobIds).
		Order("ja.accept_time").
		Group("ja.job_id").
		Find(&applicantList).Error; err != nil {
		return nil, err
	}
	acceptedApplicantMap := make(map[uint64]string)
	for _, item := range applicantList {
		acceptedApplicantMap[item.JobId] = item.ProfessionalNames
	}
	return acceptedApplicantMap, nil
}
func (s *jobService) LoadJobShiftItems(db *gorm.DB, facilityId, jobId uint64) ([]JobShiftItem, error) {
	var resp []JobShiftItem
	if err := db.Model(&model.JobShift{}).
		Select([]string{
			"id AS job_shift_id",
			"begin_time",
			"end_time",
			"duration",
			"break_duration",
			"pay_hours",
			"shift_period",
			"hourly_rate",
			"allowance_amount",
			"break_time_payable",
		}).
		Where("job_id = ?", jobId).
		Where("facility_id = ?", facilityId).
		Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

// 獲取工作班次時間
func (s *jobService) GetJobShiftItems(db *gorm.DB, facilityId uint64, jobIds []uint64) (map[uint64][]JobShiftItem, error) {
	shiftTimesMap := make(map[uint64][]JobShiftItem)
	var shiftTimes []JobShiftItem
	if err := db.Table("job_shift").
		Select([]string{
			"id AS job_shift_id",
			"job_id",
			"begin_time",
			"end_time",
			"duration",
			"break_duration",
			"pay_hours",
			"shift_period",
			"hourly_rate",
			"allowance_amount",
		}).
		Where("job_id IN (?)", jobIds).Where("facility_id = ?", facilityId).Find(&shiftTimes).Error; err != nil {
		return shiftTimesMap, err
	}

	for _, shiftTime := range shiftTimes {
		if _, ok := shiftTimesMap[shiftTime.JobId]; !ok {
			shiftTimesMap[shiftTime.JobId] = make([]JobShiftItem, 0)
		}
		shiftTime.Allowances = make([]JobAllowanceReq, 0)
		shiftTimesMap[shiftTime.JobId] = append(shiftTimesMap[shiftTime.JobId], shiftTime)
	}

	// 添加津貼
	var allowances []model.JobAllowance
	if err := db.Table("job_allowance").
		Where("facility_id = ?", facilityId).
		Where("job_id IN (?)", jobIds).
		Order("id").
		Find(&allowances).Error; err != nil {
		return shiftTimesMap, err
	}
	shiftAllowanceMap := make(map[uint64][]JobAllowanceReq) // JobShiftId -> []JobAllowanceReq
	for _, allowance := range allowances {
		if _, ok := shiftAllowanceMap[allowance.JobShiftId]; !ok {
			shiftAllowanceMap[allowance.JobShiftId] = make([]JobAllowanceReq, 0)
		}
		allowanceReq := JobAllowanceReq{
			AllowanceId:            allowance.AllowanceId,
			AllowanceName:          allowance.AllowanceName,
			AllowanceType:          allowance.AllowanceType,
			Amount:                 allowance.Amount,
			AttractsSuperannuation: allowance.AttractsSuperannuation,
			BaseAmount:             allowance.BaseAmount,
		}
		shiftAllowanceMap[allowance.JobShiftId] = append(shiftAllowanceMap[allowance.JobShiftId], allowanceReq)
	}

	for _, allowance := range allowances {
		for i, shiftTime := range shiftTimesMap[allowance.JobId] {
			if _, ok := shiftAllowanceMap[shiftTime.JobShiftId]; !ok {
				shiftTimesMap[allowance.JobId][i].Allowances = make([]JobAllowanceReq, 0)
			} else {
				shiftTimesMap[allowance.JobId][i].Allowances = shiftAllowanceMap[shiftTime.JobShiftId]
			}
		}
	}

	return shiftTimesMap, nil
}

// 獲取工作班次的津貼設定
func (s *jobService) GetJobShiftAllowances(db *gorm.DB, jobIds []uint64) (map[uint64][]JobAllowanceReq, error) {
	shiftAllowanceMap := make(map[uint64][]JobAllowanceReq)
	var shiftTimes []JobShiftItem
	if err := db.Table("job_shift").
		Select([]string{
			"id AS job_shift_id",
			"job_id",
			"begin_time",
			"end_time",
			"duration",
			"break_duration",
			"pay_hours",
			"shift_period",
			"hourly_rate",
			"allowance_amount",
		}).
		Where("job_id IN (?)", jobIds).Find(&shiftTimes).Error; err != nil {
		return shiftAllowanceMap, err
	}

	// 添加津貼
	var allowances []model.JobAllowance
	if err := db.Table("job_allowance").
		Where("job_id IN (?)", jobIds).
		Order("id").
		Find(&allowances).Error; err != nil {
		return shiftAllowanceMap, err
	}
	for _, allowance := range allowances {
		if _, ok := shiftAllowanceMap[allowance.JobShiftId]; !ok {
			shiftAllowanceMap[allowance.JobShiftId] = make([]JobAllowanceReq, 0)
		}
		allowanceReq := JobAllowanceReq{
			AllowanceId:            allowance.AllowanceId,
			AllowanceName:          allowance.AllowanceName,
			AllowanceType:          allowance.AllowanceType,
			Amount:                 allowance.Amount,
			AttractsSuperannuation: allowance.AttractsSuperannuation,
			BaseAmount:             allowance.BaseAmount,
		}
		shiftAllowanceMap[allowance.JobShiftId] = append(shiftAllowanceMap[allowance.JobShiftId], allowanceReq)
	}

	return shiftAllowanceMap, nil
}

// 加載工作申請人信息
func (s *jobService) LoadJobApplicantInfo(db *gorm.DB, facilityId, jobId uint64) ([]JobListApplicantInfo, error) {
	var resp []JobListApplicantInfo
	if err := db.Table("job_application AS ja").
		Joins("JOIN professional AS p ON ja.professional_id = p.id").
		Joins("JOIN user AS u ON p.user_id = u.id").
		Joins("JOIN professional_file AS f ON p.user_id = u.id AND f.file_code = ?", model.ProfessionalFileCodePhoto).
		Select([]string{
			"ja.id AS job_application_id",
			"ja.job_id",
			"ja.professional_id",
			"CONCAT(p.first_name, ' ', p.last_name) AS professional_name",
			"f.id AS professional_photo_file_id",
		}).
		Where("ja.job_id = ?", jobId).
		Where("ja.facility_id = ?", facilityId).
		Where("ja.status NOT IN (?)", []string{model.JobApplicationStatusWithdraw, model.JobApplicationStatusApplicationCancel, model.JobApplicationStatusFacilityCancel, model.JobApplicationStatusProfessionalCancel}).
		Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

// 獲取工作申請人信息
func (s *jobService) GetJobApplicantInfo(db *gorm.DB, facilityId uint64, jobIds []uint64) (map[uint64][]JobListApplicantInfo, error) {
	applicantInfoMap := make(map[uint64][]JobListApplicantInfo)
	var applicantInfo []JobListApplicantInfo
	if err := db.Table("job_application AS ja").
		Joins("JOIN professional AS p ON ja.professional_id = p.id").
		Joins("JOIN user AS u ON p.user_id = u.id").
		Joins("JOIN professional_file_relation AS pfr ON pfr.professional_id = p.id ").
		Joins("JOIN professional_file AS pf ON pfr.professional_file_id = pf.id AND pf.user_id = p.user_id AND pf.file_code = ?", model.ProfessionalFileCodePhoto).
		Select([]string{
			"ja.id AS job_application_id",
			"ja.job_id",
			"ja.professional_id",
			"CONCAT(p.first_name, ' ', p.last_name) AS professional_name",
			"pf.id AS professional_photo_file_id",
		}).
		Where("ja.job_id IN (?)", jobIds).
		Where("ja.facility_id = ?", facilityId).
		Where("ja.deleted <> ?", model.JobApplicationDeletedY).
		Group("ja.id").
		Find(&applicantInfo).Error; err != nil {
		return applicantInfoMap, err
	}
	for _, item := range applicantInfo {
		if _, ok := applicantInfoMap[item.JobId]; !ok {
			applicantInfoMap[item.JobId] = make([]JobListApplicantInfo, 0)
		}
		applicantInfoMap[item.JobId] = append(applicantInfoMap[item.JobId], item)
	}
	return applicantInfoMap, nil
}

// JobSearchReq 工作職位搜索請求
type JobSearchReq struct {
	FacilityId         uint64 `form:"facilityId" binding:"required"`                                                                 // 所屬機構Id
	PositionProfession string `form:"positionProfession" binding:"omitempty,max=255"`                                                // 職位專業
	Progress           string `form:"progress" binding:"omitempty,oneof=PENDING UPCOMING IN_PROGRESS COMPLETE CANCEL"`               // 進度
	ServiceLocationId  uint64 `form:"serviceLocationId"`                                                                             // 服務地點Id
	Specialisation     string `form:"specialisation" binding:"omitempty,max=64"`                                                     // 專業要求
	SelectedId         uint64 `form:"selectedId"`                                                                                    // 優先選擇ID
	Limit              int    `form:"limit"`                                                                                         // 限制返回數量
	BeginTime          string `form:"beginTime"`                                                                                     // 工作開始時間
	EndTime            string `form:"endTime"`                                                                                       // 工作結束時間
	Status             string `form:"status" binding:"omitempty,oneof=PENDING PUBLISH DISABLE CANCEL COMPLETE"`                      // 職位狀態 PENDING PUBLISH DISABLE CANCEL COMPLETE
	JobCategory        string `form:"jobCategory" binding:"omitempty,oneof=DRAFT OPEN UPCOMING IN_PROGRESS COMPLETE WAITING CANCEL"` // 工作分類 DRAFT=草稿 OPEN=招聘中 UPCOMING=待開始 IN_PROGRESS=進行中 COMPLETE=已完成 WAITING=待開始 CANCEL=已取消
	HiringStatus       string `form:"hiringStatus" binding:"omitempty,oneof=COMPLETE PARTIALLY_COMPLETE INCOMPLETE"`                 // 招聘狀態 COMPLETE=已完成 PARTIALLY_COMPLETE=部分完成 INCOMPLETE=未完成
}

// JobSearchResp 工作職位搜索響應
type JobSearchResp struct {
	JobId                  uint64                 `json:"jobId"`                          // 工作職位Id
	FacilityId             uint64                 `json:"facilityId"`                     // 所屬機構Id
	JobNo                  string                 `json:"jobNo"`                          // 工作編號
	PositionProfession     string                 `json:"positionProfession"`             // 職位名稱
	NumberOfPeople         int32                  `json:"numberOfPeople"`                 // 所需人數
	ServiceLocationId      uint64                 `json:"serviceLocationId"`              // 服務地點Id
	Timezone               string                 `json:"timezone"`                       // 服務地點時區
	ServiceLocationAddress string                 `json:"serviceLocationAddress"`         // 服務地點地址
	LocationCity           string                 `json:"locationCity"`                   // 城市
	MinExperienceLevel     string                 `json:"minExperienceLevel"`             // 最低職級要求
	PreferredGrade         string                 `json:"preferredGrade"`                 // 首選級別
	Qualification          string                 `json:"qualification"`                  // 護理資格
	Specialisation         string                 `json:"specialisation"`                 // 專業要求
	Language               string                 `json:"language"`                       // 語言要求
	SupervisionLevel       string                 `json:"supervisionLevel"`               // 監督級別
	MinHourlyRate          decimal.Decimal        `json:"minHourlyRate"`                  // 最低時薪
	MaxHourlyRate          decimal.Decimal        `json:"maxHourlyRate"`                  // 最高時薪
	Benefits               string                 `json:"benefits"`                       // 福利
	ShiftAllocation        string                 `json:"shiftAllocation"`                // 班次分配方式
	Remark                 string                 `json:"remark"`                         // 備註
	Status                 string                 `json:"status"`                         // 職位狀態
	BeginTime              *time.Time             `json:"beginTime"`                      // 工作開始時間
	EndTime                *time.Time             `json:"endTime"`                        // 工作結束時間
	JobShiftItems          []JobShiftItem         `json:"jobShiftItems" gorm:"-"`         // 班次時間
	ApplicantInfo          []JobListApplicantInfo `json:"applicantInfo" gorm:"-"`         // 申請人信息
	ApplicantCount         int32                  `json:"applicantCount"`                 // 申請人數
	Progress               string                 `json:"progress,omitempty" gorm:"-"`    // 進度 PENDING=待發佈(草稿) UPCOMING=待開始 IN_PROGRESS=進行中 COMPLETE=已完成 CANCEL=已取消
	JobCategory            string                 `json:"jobCategory,omitempty" gorm:"-"` // 工作分類 DRAFT=草稿 OPEN=招聘中 UPCOMING=待開始 IN_PROGRESS=進行中 COMPLETE=已完成 WAITING=待開始 CANCEL=已取消
}

// 更新工作進度
func (r *JobSearchResp) UpdateProgress(timezone string) {
	if r.Timezone == "" || r.BeginTime == nil || r.EndTime == nil {
		r.Progress = ""
		return
	}
	r.Progress = calculateJobProgress(r.Status, *r.BeginTime, *r.EndTime, timezone)
}

// 更新工作分類
func (r *JobSearchResp) UpdateJobCategory(db *gorm.DB) error {
	var err error
	if r.Timezone == "" {
		r.JobCategory = ""
		return nil
	}

	m := model.Job{
		Id:         r.JobId,
		FacilityId: r.FacilityId,
		Status:     r.Status,
		BeginTime:  r.BeginTime,
		EndTime:    r.EndTime,
	}
	r.JobCategory, err = JobService.GetJobCategory(db, m, r.Timezone)
	if err != nil {
		return err
	}
	return nil
}

// 搜索工作職位
func (s *jobService) Search(db *gorm.DB, req JobSearchReq) ([]JobSearchResp, error) {
	var err error
	var resp []JobSearchResp
	builder := db.Table("job AS j").
		Joins("LEFT JOIN service_location AS sl ON j.service_location_id = sl.id").
		Joins("LEFT JOIN job_application AS ja ON j.id = ja.job_id AND ja.facility_id = j.facility_id AND ja.deleted <> ?", model.JobApplicationDeletedY).
		Joins("LEFT JOIN job_benefit AS jb ON j.id = jb.job_id AND j.facility_id = jb.facility_id").
		Joins("LEFT JOIN benefit AS b ON jb.benefit_id = b.id").
		Select([]string{
			"j.id AS job_id",
			"j.facility_id",
			"j.job_no",
			"j.position_profession",
			"j.number_of_people",
			"j.service_location_id",
			"sl.timezone",
			"IF(sl.address_extra IS NOT NULL AND sl.address_extra != '', CONCAT(sl.address_extra, ' ', sl.address), sl.address) AS service_location_address",
			"j.min_experience_level",
			"j.preferred_grade",
			"j.qualification",
			"j.specialisation",
			"j.language",
			"j.supervision_level",
			"j.min_hourly_rate",
			"j.max_hourly_rate",
			"j.shift_allocation",
			"j.remark",
			"j.status",
			"j.begin_time",
			"j.end_time",
			"COUNT(DISTINCT ja.id) AS applicant_count",
			"GROUP_CONCAT(DISTINCT CASE WHEN b.name IS NOT NULL AND b.name != '' THEN b.name ELSE NULL END SEPARATOR ',') AS benefits",
		}).
		Where("j.facility_id = ?", req.FacilityId)

	if req.PositionProfession != "" {
		builder = builder.Where("j.position_profession LIKE ?", "%"+req.PositionProfession+"%")
	}
	if req.ServiceLocationId != 0 {
		builder = builder.Where("j.service_location_id = ?", req.ServiceLocationId)
	}
	if req.Specialisation != "" {
		builder = builder.Where("j.specialisation = ?", req.Specialisation)
	}
	if req.BeginTime != "" {
		builder = builder.Where("CONVERT_TZ(j.end_time, 'UTC', sl.timezone) >= ?", req.BeginTime)
	}
	if req.EndTime != "" {
		builder = builder.Where("CONVERT_TZ(j.begin_time, 'UTC', sl.timezone) <= ?", req.EndTime)
	}

	// 根據Status進行篩選
	if req.Status != "" {
		builder = builder.Where("j.status = ?", req.Status)
	}

	hasJacSelect := false
	acceptedCountBuilder := db.Table("job_application AS ja").
		Select([]string{
			"ja.job_id",
			"ja.facility_id",
			"SUM(CASE WHEN ja.accept = 'Y' THEN 1 ELSE 0 END) AS accepted_count",
		}).
		Where("ja.facility_id = ?", req.FacilityId).
		Group("ja.job_id")

	// 根據JobCategory進行篩選
	if req.JobCategory != "" {
		nowTime := time.Now().UTC().Truncate(time.Second)
		nowStr := nowTime.Format(xtool.DateTimeSecA1)
		switch req.JobCategory {
		case JobCategoryDraft: // 草稿
			builder = builder.Where("j.status = ?", model.JobStatusPending)
		case JobCategoryOpen: // 招聘中
			// Status: PUBLISH / DISABLE
			// (PublishTime) < Now < (BeginTime - 1h)
			// 未招滿人
			hasJacSelect = true
			builder = builder.Where("j.status IN (?, ?)", model.JobStatusPublish, model.JobStatusDisable).
				Where("j.publish_time <= ?", nowStr).                         // 發佈時間小於當前時間
				Where("DATE_SUB(j.begin_time, INTERVAL 1 HOUR) > ?", nowStr). // 開始時間減1小時後，大於當前時間
				Joins("LEFT JOIN (?) AS jac ON j.id = jac.job_id AND j.facility_id = jac.facility_id", acceptedCountBuilder).
				Where("(jac.accepted_count IS NULL OR jac.accepted_count < j.number_of_people)")
		case JobCategoryUpcoming: // 待開始
			// Status: PUBLISH
			// (BeginTime - 1h) < Now < BeginTime 或 已招滿人
			hasJacSelect = true

			builder = builder.Where("j.status = ?", model.JobStatusPublish).
				Where("j.begin_time > ?", nowStr).
				Joins("LEFT JOIN (?) AS jac ON j.id = jac.job_id AND j.facility_id = jac.facility_id", acceptedCountBuilder).
				Where("(DATE_SUB(j.begin_time, INTERVAL 1 HOUR) <= ?) OR (jac.accepted_count >= j.number_of_people)", nowStr)
		case JobCategoryInProgress: // 進行中
			// Status: PUBLISH
			// BeginTime < Now < EndTime
			builder = builder.Where("j.status = ?", model.JobStatusPublish).
				Where("j.begin_time <= ?", nowStr).
				Where("j.end_time > ?", nowStr)
		case JobCategoryComplete: // 已結束
			// Status: PUBLISH
			// EndTime < Now
			builder = builder.Where("j.status = ?", model.JobStatusPublish).
				Where("j.end_time < ?", nowStr)
		case JobCategoryWaiting: // 等待發佈
			// Status: PUBLISH
			// Now < PublishTime
			builder = builder.Where("j.status = ?", model.JobStatusPublish).
				Where("j.publish_time > ?", nowStr)
		case JobCategoryCancel: // 已取消
			// Status: CANCEL
			builder = builder.Where("j.status = ?", model.JobStatusCancel)
		}
	}

	if req.Progress != "" {
		// TODO：有問題，數據不對
		nowStr := time.Now().UTC().Format(xtool.DateTimeSecA1)
		switch req.Progress {
		case JobProgressPending:
			builder = builder.Where("j.status = ?", model.JobStatusPending)
		case JobProgressUpcoming:
			// status = PUBLISH 並且 begin_time 大於 當前時間
			builder = builder.Where("j.status = ?", model.JobStatusPublish).
				Where("j.begin_time > ?", nowStr)
		case JobProgressInProgress:
			// status = PUBLISH 並且 begin_time 小於 當前時間 並且 end_time 大於 當前時間
			builder = builder.Where("j.status = ?", model.JobStatusPublish).
				Where("j.begin_time < ?", nowStr).
				Where("j.end_time > ?", nowStr)
		case JobProgressComplete:
			// status = COMPLETE 或 status = PUBLISH 並且 end_time 小於 當前時間
			builder = builder.Where("j.status = ? OR (j.status = ? AND j.end_time < ?)", model.JobStatusComplete, model.JobStatusPublish, nowStr)
		case JobProgressCancel:
			builder = builder.Where("j.status = ?", model.JobStatusCancel)
		}
	}

	if req.HiringStatus != "" {
		if !hasJacSelect {
			builder = builder.
				Joins("LEFT JOIN (?) AS jac ON j.id = jac.job_id AND j.facility_id = jac.facility_id", acceptedCountBuilder)
		}
		switch req.HiringStatus {
		case JobHiringStatusComplete:
			builder = builder.Where("jac.accepted_count = j.number_of_people")
		case JobHiringStatusPartiallyComplete:
			builder = builder.Where("jac.accepted_count < j.number_of_people")
		case JobHiringStatusIncomplete:
			builder = builder.Where("jac.accepted_count = 0 OR jac.accepted_count IS NULL")
		}
	}

	// 處理優先選擇的ID
	if req.SelectedId != 0 {
		builder = builder.Order(fmt.Sprintf("IF(j.id = %d,0,1)", req.SelectedId))
	}
	if req.Limit > 0 {
		builder = builder.Limit(req.Limit)
	}
	if err = builder.Group("j.id").Order("j.id").Find(&resp).Error; err != nil {
		return resp, err
	}

	// 獲取各種代碼名稱映射關係
	var professionSectionMap map[string]string
	var experienceLevelSectionMap map[string]string
	var preferredGradeSectionMap map[string]string
	var languageSectionMap map[string]string
	var qualificationSectionMap map[string]string
	if len(resp) > 0 {
		professionSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
		if err != nil {
			return resp, err
		}
		experienceLevelSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeExperienceLevelMedicalPractitioner, model.SelectionTypeExperienceLevelRegisteredNurse})
		if err != nil {
			return resp, err
		}
		preferredGradeSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypePreferredGrade})
		if err != nil {
			return resp, err
		}
		languageSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeLanguage})
		if err != nil {
			return resp, err
		}
		qualificationSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalPersonalCareWorkerQualification})
		if err != nil {
			return resp, err
		}
	}
	jobIds := make([]uint64, 0)
	for _, item := range resp {
		jobIds = append(jobIds, item.JobId)
	}
	jobShiftItemsMap, err := s.GetJobShiftItems(db, req.FacilityId, jobIds)
	if err != nil {
		return resp, err
	}
	applicantInfoMap, err := s.GetJobApplicantInfo(db, req.FacilityId, jobIds)
	if err != nil {
		return resp, err
	}
	// 加載班次時間和申請人信息
	for i, item := range resp {
		if jobShiftItems, ok := jobShiftItemsMap[item.JobId]; ok {
			resp[i].JobShiftItems = jobShiftItems
		} else {
			resp[i].JobShiftItems = make([]JobShiftItem, 0)
		}

		if applicantInfo, ok := applicantInfoMap[item.JobId]; ok {
			resp[i].ApplicantInfo = applicantInfo
		} else {
			resp[i].ApplicantInfo = make([]JobListApplicantInfo, 0)
		}

		// 更新Progress字段
		resp[i].UpdateProgress(item.Timezone)

		// job_category
		if err = resp[i].UpdateJobCategory(db); err != nil {
			return resp, err
		}

		if item.PositionProfession != "" {
			resp[i].PositionProfession = professionSectionMap[item.PositionProfession]
		}
		if item.MinExperienceLevel != "" {
			resp[i].MinExperienceLevel = experienceLevelSectionMap[item.MinExperienceLevel]
		}
		if item.PreferredGrade != "" {
			resp[i].PreferredGrade = preferredGradeSectionMap[item.PreferredGrade]
		}
		if item.Language != "" {
			languageList := strings.Split(item.Language, ",")
			var languageNameList []string
			for _, language := range languageList {
				languageNameList = append(languageNameList, languageSectionMap[language])
			}
			resp[i].Language = strings.Join(languageNameList, ",")
		}
		if item.Qualification != "" {
			qualificationList := strings.Split(item.Qualification, ",")
			var qualificationNameList []string
			for _, qualification := range qualificationList {
				qualificationNameList = append(qualificationNameList, qualificationSectionMap[qualification])
			}
			resp[i].Qualification = strings.Join(qualificationNameList, ",")
		}

		// 清理數據
		if req.JobCategory == JobCategoryDraft {
			resp[i].ApplicantCount = 0
			resp[i].JobCategory = ""
			resp[i].Progress = ""
		}
	}
	return resp, nil
}

// 編輯工作職位請求
type JobEditReq struct {
	Draft              string `json:"draft" binding:"required,oneof=Y N"`                                                                                                      // 是否為草稿 Y N
	FacilityId         uint64 `json:"facilityId" binding:"required"`                                                                                                           // 所屬機構Id
	JobId              uint64 `json:"jobId" binding:"required"`                                                                                                                // 工作職位Id
	PositionProfession string `json:"positionProfession" binding:"required,max=255,oneof=MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER"`           // 職位專業 selectionType=PROFESSIONAL_PROFESSION
	NumberOfPeople     int32  `json:"numberOfPeople" binding:"required"`                                                                                                       // 所需人數
	ServiceLocationId  uint64 `json:"serviceLocationId" binding:"required"`                                                                                                    // 服務地點Id
	MinExperienceLevel string `json:"minExperienceLevel" binding:"required_if=PositionProfession MEDICAL_PRACTITIONER,required_if=PositionProfession REGISTERED_NURSE,max=32"` // 最低職級要求 selectionType=EXPERIENCE_LEVEL_MEDICAL_PRACTITIONER/EXPERIENCE_LEVEL_REGISTERED_NURSE/EXPERIENCE_LEVEL_ENROLLED_NURSE

	PreferredGrade      string          `json:"preferredGrade" binding:"required_if=PositionProfession MEDICAL_PRACTITIONER,max=32"`             // 首選級別 selection_type PREFERRED_GRADE(Profession = Medical Practitioner) 其他專業不填
	Qualification       string          `json:"qualification" binding:"required_if=PositionProfession PERSONAL_CARE_WORKER,max=1024"`            // 護理資格 selection_type PROFESSIONAL_PERSONAL_CARE_WORKER_QUALIFICATION(Profession = Personal Care Worker)
	Specialisation      string          `json:"specialisation" binding:"required,max=64"`                                                        // 專業要求 selectionType=PREFERRED_SPECIALTY_MEDICAL_PRACTITIONER/PREFERRED_SPECIALTY_NURSE/PREFERRED_SPECIALTY_PERSONAL_CARE_WORKER
	Language            string          `json:"language" binding:"omitempty,max=255"`                                                            // 語言要求 多個逗號分割 selectionType=LANGUAGE
	LanguageRequirement string          `json:"languageRequirement" binding:"omitempty,oneof=Y N"`                                               // 語言要求類型 Y=必須 N=可選
	SupervisionLevel    string          `json:"supervisionLevel" binding:"omitempty,oneof=FULLY_SUPERVISED PARTIALLY_SUPERVISED NO_SUPERVISION"` // 監督級別 FULLY_SUPERVISED PARTIALLY_SUPERVISED NO_SUPERVISION
	SplitType           string          `json:"splitType" binding:"required,oneof=NO DATE SHIFT"`                                                // 分拆 NO DATE SHIFT

	Benefits            string          `json:"benefits" binding:"omitempty,max=1024"`                                                           // 福利
	ShiftAllocation     string          `json:"shiftAllocation" binding:"required,oneof=AUTOMATIC MANUAL"`                                       // 班次分配方式
	Remark              string          `json:"remark" binding:"omitempty,max=1024"`                                                             // 備註
	JobShiftItems       []JobShiftItem  `json:"jobShiftItems" binding:"required,min=1,dive"`                                                     // 班次時間
	PublishTime         *time.Time      `json:"publishTime" binding:"required_if=PublishNow N,required_without=PublishNow"`                      // 發佈時間
	PublishNow          string          `json:"publishNow" binding:"omitempty,oneof=Y N"`                                                        // 立即發佈 Y N
	GrandTotal          decimal.Decimal `json:"grandTotal" binding:"required"`                                                                   // 前端計算的總金額
	UpdatedUserId       uint64          `json:"-"`                                                                                               // 更新者Id
}

// 編輯工作職位草稿請求
type JobEditDraftReq struct {
	Draft               string          `json:"draft" binding:"required,oneof=Y N"`                                                                                            // 是否為草稿 Y N
	FacilityId          uint64          `json:"facilityId" binding:"required"`                                                                                                 // 所屬機構Id
	JobId               uint64          `json:"jobId" binding:"required"`                                                                                                      // 工作職位Id
	PositionProfession  string          `json:"positionProfession" binding:"required,max=255,oneof=MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER"` // 職位專業 selectionType=PROFESSIONAL_PROFESSION
	NumberOfPeople      int32           `json:"numberOfPeople" binding:"omitempty"`                                                                                            // 所需人數
	ServiceLocationId   uint64          `json:"serviceLocationId" binding:"omitempty"`                                                                                         // 服務地點Id
	MinExperienceLevel  string          `json:"minExperienceLevel" binding:"omitempty,max=32"`                                                                                 // 最低職級要求 selectionType=EXPERIENCE_LEVEL_MEDICAL_PRACTITIONER/EXPERIENCE_LEVEL_REGISTERED_NURSE/EXPERIENCE_LEVEL_ENROLLED_NURSE
	PreferredGrade      string          `json:"preferredGrade" binding:"omitempty,max=32"`                                                                                     // 首選級別 selection_type PREFERRED_GRADE(Profession = Medical Practitioner) 其他專業不填
	Qualification       string          `json:"qualification" binding:"omitempty,max=1024"`                                                                                    // 護理資格 selection_type PROFESSIONAL_PERSONAL_CARE_WORKER_QUALIFICATION(Profession = Personal Care Worker)
	Specialisation      string          `json:"specialisation" binding:"omitempty,max=64"`                                                                                     // 專業要求 selectionType=PREFERRED_SPECIALTY_MEDICAL_PRACTITIONER/PREFERRED_SPECIALTY_NURSE/PREFERRED_SPECIALTY_PERSONAL_CARE_WORKER
	Language            string          `json:"language" binding:"omitempty,max=255"`                                                                                          // 語言要求 多個逗號分割 selectionType=LANGUAGE
	LanguageRequirement string          `json:"languageRequirement" binding:"omitempty,oneof=Y N"`                                                                             // 語言要求類型 Y=必須 N=可選
	SupervisionLevel    string          `json:"supervisionLevel" binding:"omitempty,oneof=FULLY_SUPERVISED PARTIALLY_SUPERVISED NO_SUPERVISION"`                               // 監督級別 FULLY_SUPERVISED PARTIALLY_SUPERVISED NO_SUPERVISION
	HourlyRate          decimal.Decimal `json:"hourlyRate" binding:"omitempty"`                                                                                                // 時薪
	SplitType           string          `json:"splitType" binding:"omitempty,oneof=NO DATE SHIFT"`                                                                             // 分拆 NO DATE SHIFT

	Benefits            string          `json:"benefits" binding:"omitempty,max=1024"`                                                                                         // 福利
	ShiftAllocation     string          `json:"shiftAllocation" binding:"omitempty,oneof=AUTOMATIC MANUAL"`                                                                    // 班次分配方式
	Remark              string          `json:"remark" binding:"omitempty,max=1024"`                                                                                           // 備註
	JobShiftItems       []JobShiftItem  `json:"jobShiftItems" binding:"omitempty,dive"`                                                                                        // 班次時間
	PublishTime         *time.Time      `json:"publishTime" binding:"omitempty"`                                                                                               // 發佈時間
	PublishNow          string          `json:"publishNow" binding:"omitempty,oneof=Y N"`                                                                                      // 立即發佈 Y N
	UpdatedUserId       uint64          `json:"-"`                                                                                                                             // 更新者Id
}

// 編輯工作職位
func (s *jobService) Edit(db *gorm.DB, req JobEditReq) error {
	var err error

	var serviceLocation model.ServiceLocation
	var timezone string
	if req.ServiceLocationId > 0 {
		if err = db.First(&serviceLocation, req.ServiceLocationId).Error; err != nil {
			return err
		}
		timezone = serviceLocation.Timezone
	}

	facilityProfile, err := FacilityProfileService.GetCurrentFacilityProfile(db, req.FacilityId)
	if err != nil {
		return err
	}

	var jobShiftItemGroups [][]JobShiftItem
	if req.Draft == "Y" {
		jobShiftItemGroups = [][]JobShiftItem{req.JobShiftItems}
	} else {
		jobShiftItemGroups, err = s.SplitJobShiftItems(req.SplitType, req.JobShiftItems, timezone)
	}

	// 檢查工作職位是否存在
	var oldJob model.Job
	if err = db.First(&oldJob, req.JobId).Error; err != nil {
		return err
	}
	nowTime := time.Now().UTC().Truncate(time.Second)

	for i, jobShiftItems := range jobShiftItemGroups {
		var job model.Job
		if i > 0 {
			var jobNo string
			jobNo, err = s.GenerateJobNo(db, oldJob.FacilityId, timezone)
			if err != nil {
				return err
			}
			job = model.Job{
				FacilityId:         oldJob.FacilityId,
				CreatedUserId:      req.UpdatedUserId,
				UpdatedUserId:      req.UpdatedUserId,
				JobScheduleId:      oldJob.JobScheduleId,
				ScheduleTemplate:   oldJob.ScheduleTemplate,
				JobNo:              jobNo,
				PositionProfession: oldJob.PositionProfession,
				PaymentTerms:       facilityProfile.PaymentTerms,
				FacilityProfileId:  facilityProfile.Id,
				CreateTime:         nowTime,
				UpdateTime:         &nowTime,
			}
		} else {
			job = oldJob
		}

		// 複製請求到模型
		_ = copier.Copy(&job, req)

		// 獲取班次時間範圍
		job.BeginTime, job.EndTime = s.GetShiftTimeRange(jobShiftItems)
		job.Duration = decimal.Zero
		job.PayHours = decimal.Zero
		if job.BeginTime != nil && job.EndTime != nil {
			job.Duration, _, job.PayHours, err = s.GetShiftDuration(jobShiftItems)
			if err != nil {
				return err
			}
		}
		// 設置更新時間
		job.UpdateTime = &nowTime
		if req.Draft == "Y" {
			job.Status = model.JobStatusPending
			job.SplitType = req.SplitType
			job.PublishTime = req.PublishTime
		} else {
			job.Status = model.JobStatusPublish
			job.SplitType = model.JobSplitTypeNo
			if req.PublishNow == "Y" {
				job.PublishTime = &nowTime
			} else {
				job.PublishTime = req.PublishTime
			}
		}

		// 生成ShiftTimeType
		var shiftTimeType, weekdayType string
		shiftTimeType, weekdayType, err = s.GenerateShiftTimeAndWeekdays(db, req.FacilityId, jobShiftItems, serviceLocation.Timezone)
		if err != nil {
			return err
		}
		job.ShiftTimeType = shiftTimeType
		job.WeekdayType = weekdayType

		// 計算並設置 MinHourlyRate 和 MaxHourlyRate
		job.MinHourlyRate, job.MaxHourlyRate = s.CalculateHourlyRateRange(jobShiftItems)

		// 保存工作職位
		if err = db.Save(&job).Error; err != nil {
			return err
		}

		// 刪除舊的班次時間
		if err = db.Where("job_id = ? AND facility_id = ?", job.Id, req.FacilityId).Delete(&model.JobShift{}).Error; err != nil {
			return err
		}

		// 創建新的班次時間
		if err = s.UpdateJobShiftItems(db, req.FacilityId, job.Id, jobShiftItems); err != nil {
			return err
		}
		// 創建新的工作福利
		if err = s.UpdateJobBenefitItems(db, req.FacilityId, job.Id, req.Benefits); err != nil {
			return err
		}
	}
	return nil
}

// 查詢工作職位請求
type JobInquireReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"` // 所屬機構Id
	JobId      uint64 `form:"jobId" binding:"required"`      // 工作職位Id
}

// 查詢工作職位請求
type JobInquireReqBySystem struct {
	JobId uint64 `form:"jobId" binding:"required"` // 工作職位Id
}

// 查詢工作職位響應
type JobInquireResp struct {
	JobId                  uint64          `json:"jobId"`                   // 工作職位Id
	FacilityId             uint64          `json:"facilityId"`              // 所屬機構Id
	FacilityName           string          `json:"facilityName"`            // 所屬機構名稱
	CreatedUserId          uint64          `json:"createdUserId"`           // 創建者Id
	UpdatedUserId          uint64          `json:"updatedUserId"`           // 更新者Id
	JobNo                  string          `json:"jobNo"`                   // 工作編號
	PositionProfession     string          `json:"positionProfession"`      // 職位專業
	PositionProfessionName string          `json:"positionProfessionName"`  // 職位專業名稱
	NumberOfPeople         int32           `json:"numberOfPeople"`          // 所需人數
	AcceptedCount          int32           `json:"acceptedCount"`           // 已取錄人數
	ServiceLocationId      uint64          `json:"serviceLocationId"`       // 服務地點Id
	Timezone               string          `json:"timezone"`                // 服務地點時區
	ServiceLocationAddress string          `json:"serviceLocationAddress"`  // 服務地點地址
	MinExperienceLevel     string          `json:"minExperienceLevel"`      // 最低職級要求
	MinExperienceLevelName string          `json:"minExperienceLevelName"`  // 最低職級要求名稱
	PreferredGrade         string          `json:"preferredGrade"`          // 首選級別
	PreferredGradeName     string          `json:"preferredGradeName"`      // 首選級別名稱
	Qualification          string          `json:"qualification"`           // 護理資格
	QualificationName      string          `json:"qualificationName"`       // 護理資格名稱
	Specialisation         string          `json:"specialisation"`          // 專業要求
	SpecialisationName     string          `json:"specialisationName"`      // 專業要求名稱
	Language               string          `json:"language"`                // 語言要求
	LanguageName           string          `json:"languageName"`            // 語言要求名稱
	SupervisionLevel       string          `json:"supervisionLevel"`        // 監督級別
	SupervisionLevelName   string          `json:"supervisionLevelName"`    // 監督級別名稱
	MinHourlyRate          decimal.Decimal `json:"minHourlyRate"`           // 最低時薪
	MaxHourlyRate          decimal.Decimal `json:"maxHourlyRate"`           // 最高時薪

	Benefits               string          `json:"benefits"`                // 福利
	BenefitsName           string          `json:"benefitsName"`            // 福利名稱
	ShiftAllocation        string          `json:"shiftAllocation"`         // 班次分配方式
	Remark                 string          `json:"remark"`                  // 備註
	CancelReason           string          `json:"cancelReason"`            // 取消原因
	Status                 string          `json:"status"`                  // 職位狀態 PENDING PUBLISH DISABLE COMPLETE CANCEL
	PaymentTerms           string          `json:"paymentTerms"`            // 付款條件
	BeginTime              *time.Time      `json:"beginTime"`               // 工作開始時間
	EndTime                *time.Time      `json:"endTime"`                 // 工作結束時間
	CreateTime             time.Time       `json:"createTime"`              // 創建時間
	UpdateTime             *time.Time      `json:"updateTime"`              // 更新時間
	PublishTime            *time.Time      `json:"publishTime"`             // 發佈時間
	PublishNow             string          `json:"publishNow"`              // 是否立即發佈
	JobShiftItems          []JobShiftItem  `json:"jobShiftItems" gorm:"-"`  // 班次時間
	ApplicantCount         int32           `json:"applicantCount" gorm:"-"` // 申請人數
	Progress               string          `json:"progress" gorm:"-"`       // 進度 PENDING UPCOMING IN_PROGRESS COMPLETE CANCEL
	Duration               decimal.Decimal `json:"duration"`                // 總工作時長（小時）
	PayHours               decimal.Decimal `json:"payHours"`                // 支付時長（小時）
	SplitType              string          `json:"splitType"`               // 分拆類型 NO DATE SHIFT
	JobCategory            string          `json:"jobCategory"`             // 工作分類 DRAFT=草稿 OPEN=招聘中 UPCOMING=待開始 IN_PROGRESS=進行中 WAITING=等待發佈 CANCEL=已取消
}

// 更新工作進度
func (r *JobInquireResp) UpdateProgress() {
	if r.Timezone == "" || r.BeginTime == nil || r.EndTime == nil {
		r.Progress = ""
		return
	}
	r.Progress = calculateJobProgress(r.Status, *r.BeginTime, *r.EndTime, r.Timezone)
}

// 查詢工作職位詳情
func (s *jobService) Inquire(db *gorm.DB, req JobInquireReq) (JobInquireResp, error) {
	var err error
	var resp JobInquireResp
	var m model.Job
	if err = db.First(&m, req.JobId).Error; err != nil {
		return resp, err
	}
	_ = copier.Copy(&resp, m)
	resp.JobId = m.Id

	var facilityProfile model.FacilityProfile
	if err = db.First(&facilityProfile, m.FacilityProfileId).Error; err != nil {
		return resp, err
	}
	resp.FacilityName = facilityProfile.Name

	var serviceLocation model.ServiceLocation
	if m.ServiceLocationId > 0 {
		if err = db.First(&serviceLocation, m.ServiceLocationId).Error; err != nil {
			return resp, err
		}
	}
	resp.Timezone = serviceLocation.Timezone
	resp.ServiceLocationAddress = serviceLocation.Address
	if serviceLocation.AddressExtra != "" {
		resp.ServiceLocationAddress = fmt.Sprintf("%s %s", serviceLocation.AddressExtra, serviceLocation.Address)
	}

	// 加載工作班次時間
	jobShiftItemsMap, err := s.GetJobShiftItems(db, req.FacilityId, []uint64{resp.JobId})
	if err != nil {
		return resp, err
	}
	if jobShiftItems, ok := jobShiftItemsMap[resp.JobId]; ok {
		resp.JobShiftItems = jobShiftItems
	} else {
		resp.JobShiftItems = make([]JobShiftItem, 0)
	}

	// 獲取福利的名稱
	if err = db.Table("benefit AS b").
		Joins("JOIN job_benefit AS jb ON b.id = jb.benefit_id").
		Where("jb.job_id = ?", resp.JobId).
		Select("COALESCE(GROUP_CONCAT(b.id), '') AS benefits").
		Pluck("benefits", &resp.Benefits).Error; err != nil {
		return resp, err
	}

	if m.Status != model.JobStatusPending {
		// 加載申請人信息
		applicantCount, err := s.GetJobApplicantCount(db, req.FacilityId, resp.JobId, false)
		if err != nil {
			return resp, err
		}
		resp.ApplicantCount = applicantCount

		// 更新Progress字段
		resp.UpdateProgress()

		// 獲取工作分類
		resp.JobCategory, err = s.GetJobCategory(db, m, resp.Timezone)
		if err != nil {
			return resp, err
		}
	}

	if resp.PositionProfession != "" {
		positionProfession, err := SelectionService.FindByCode(db, resp.PositionProfession)
		if xgorm.IsSqlErr(err) {
			return resp, err
		}
		if positionProfession != nil {
			resp.PositionProfessionName = positionProfession.Name
		}
	}

	if resp.MinExperienceLevel != "" {
		minExperienceLevel, err := SelectionService.FindByCode(db, resp.MinExperienceLevel)
		if xgorm.IsSqlErr(err) {
			return resp, err
		}
		if minExperienceLevel != nil {
			resp.MinExperienceLevelName = minExperienceLevel.Name
		}
	}

	if resp.PreferredGrade != "" {
		preferredGrade, err := SelectionService.FindByCode(db, resp.PreferredGrade)
		if xgorm.IsSqlErr(err) {
			return resp, err
		}
		if preferredGrade != nil {
			resp.PreferredGradeName = preferredGrade.Name
		}
	}

	if resp.Specialisation != "" {
		specialisation, err := SelectionService.FindByCode(db, resp.Specialisation)
		if xgorm.IsSqlErr(err) {
			return resp, err
		}
		if specialisation != nil {
			resp.SpecialisationName = specialisation.Name
		}
	}

	if resp.Qualification != "" {
		qualificationSectionMap, err := SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalPersonalCareWorkerQualification})
		if err != nil {
			return resp, err
		}
		qualificationList := strings.Split(resp.Qualification, ",")
		var qualificationNameList []string
		for _, qualification := range qualificationList {
			qualificationNameList = append(qualificationNameList, qualificationSectionMap[qualification])
		}
		resp.QualificationName = strings.Join(qualificationNameList, ",")
	}

	if resp.SupervisionLevel != "" {
		supervisionLevel, err := SelectionService.FindByCode(db, resp.SupervisionLevel)
		if xgorm.IsSqlErr(err) {
			return resp, err
		}
		if supervisionLevel != nil {
			resp.SupervisionLevelName = supervisionLevel.Name
		}
	}

	if resp.Language != "" {
		languageSectionMap, err := SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeLanguage})
		if err != nil {
			return resp, err
		}
		languageList := strings.Split(resp.Language, ",")
		var languageNameList []string
		for _, language := range languageList {
			languageNameList = append(languageNameList, languageSectionMap[language])
		}
		resp.LanguageName = strings.Join(languageNameList, ",")
	}
	if resp.Benefits != "" {
		// 獲取福利的名稱
		if err = db.Table("benefit AS b").
			Joins("JOIN job_benefit AS jb ON b.id = jb.benefit_id").
			Where("jb.job_id = ?", resp.JobId).
			Select("COALESCE(GROUP_CONCAT(b.name ORDER BY b.id ASC SEPARATOR ', '),'') AS benefits").
			Pluck("benefits", &resp.BenefitsName).Error; err != nil {
			return resp, err
		}
	}

	return resp, nil
}

// 獲取工作分類
func (s *jobService) GetJobCategory(db *gorm.DB, job model.Job, timezone string) (string, error) {
	var err error
	tz := time.UTC // 預設時區為UTC
	if timezone != "" {
		tz, err = time.LoadLocation(timezone)
		if err != nil {
			return "", err
		}
	}

	nowTime := time.Now().In(tz)

	// 1. 檢查特殊狀態
	if job.Status == model.JobStatusPending {
		return JobCategoryDraft, nil // 草稿
	} else if job.Status == model.JobStatusDisable {
		return JobCategoryDisable, nil // 暫停招聘
	} else if job.Status == model.JobStatusCancel {
		return JobCategoryCancel, nil // 已取消
	} else if job.Status == model.JobStatusComplete {
		return JobCategoryComplete, nil // 已標記為完成
	}

	// 除了草稿狀態外，各時間點都必須存在
	if job.BeginTime == nil || job.EndTime == nil || job.PublishTime == nil {
		return "", fmt.Errorf("job time fields cannot be nil")
	}

	// 2. 檢查是否已結束 (EndTime < Now)
	if nowTime.After(*job.EndTime) {
		return JobCategoryComplete, nil // 已結束
	}

	// 3. 檢查是否進行中 (BeginTime < Now < EndTime)
	if nowTime.After(*job.BeginTime) && nowTime.Before(*job.EndTime) {
		return JobCategoryInProgress, nil // 進行中
	}

	// 4. 檢查已經錄取的申請人數
	applicantCount, err := s.GetJobApplicantCount(db, job.FacilityId, job.Id, true)
	if err != nil {
		return "", err
	}

	// 5. 檢查是否待開始
	oneHourBeforeBegin := job.BeginTime.Add(-time.Hour)

	// 情況1: 已招滿人也算待開始
	if nowTime.Before(*job.BeginTime) && applicantCount >= job.NumberOfPeople {
		return JobCategoryUpcoming, nil // 待開始（已招滿人）
	}

	// 情況2: 距離開始時間小於1小時
	if nowTime.After(oneHourBeforeBegin) && nowTime.Before(*job.BeginTime) {
		return JobCategoryUpcoming, nil // 待開始（即將開始）
	}

	// 6. 檢查是否等待發佈 (Now < PublishTime)
	if nowTime.Before(*job.PublishTime) {
		return JobCategoryWaiting, nil // 等待發佈
	}

	// 7. 檢查是否招聘中
	// 條件: 已發佈、未招滿、距離開始還有超過1小時
	if nowTime.After(*job.PublishTime) &&
		nowTime.Before(oneHourBeforeBegin) &&
		applicantCount < job.NumberOfPeople {
		return JobCategoryOpen, nil // 招聘中
	}

	// 所有條件都不滿足，返回空字符串
	return "", nil
}

// 刪除工作職位請求
type JobDeleteReq struct {
	FacilityId uint64 `json:"facilityId" binding:"required"` // 所屬機構Id
	JobId      uint64 `json:"jobId" binding:"required"`      // 工作職位Id
}

// 刪除工作職位
func (s *jobService) Delete(db *gorm.DB, req JobDeleteReq) error {
	var err error
	if err = db.Where("facility_id = ?", req.FacilityId).Where("job_id = ?", req.JobId).Delete(&model.JobShift{}).Error; err != nil {
		return err
	}
	if err = db.Where("facility_id = ?", req.FacilityId).Where("id = ?", req.JobId).Delete(&model.Job{}).Error; err != nil {
		return err
	}
	return nil
}

// 更新工作職位狀態請求
type JobUpdateStatusReq struct {
	FacilityId   uint64 `json:"facilityId" binding:"required"`                                           // 所屬機構Id
	JobId        uint64 `json:"jobId" binding:"required"`                                                // 工作職位Id
	Status       string `json:"status" binding:"required,oneof=PENDING PUBLISH DISABLE COMPLETE CANCEL"` // 職位狀態 PENDING PUBLISH DISABLE COMPLETE CANCEL
	CancelReason string `json:"cancelReason" binding:"required_if=Status CANCEL"`                        // 取消原因
	ReqUserId    uint64 `json:"-"`
}

// 更新工作職位狀態
func (s *jobService) UpdateStatus(db *gorm.DB, req JobUpdateStatusReq) error {
	var err error

	// 檢查工作職位是否存在
	var job model.Job
	if err = db.Clauses(clause.Locking{Strength: "UPDATE"}).First(&job, req.JobId).Error; err != nil {
		return err
	}
	cancelReason := ""
	if req.Status == model.JobStatusCancel {
		cancelReason = req.CancelReason
	}
	nowTime := time.Now().UTC().Truncate(time.Second)

	updateMap := map[string]interface{}{
		"status":        req.Status,
		"update_time":   nowTime,
		"cancel_reason": cancelReason,
	}
	if req.Status == model.JobStatusPublish && job.FacilityProfileId == 0 {
		// 記錄當時檔案
		var facilityProfile model.FacilityProfile
		if err = db.Where("facility_id = ?", req.FacilityId).Where("data_type = ?", model.FacilityProfileDataTypeApproved).First(&facilityProfile).Error; err != nil {
			return err
		}
		updateMap["facility_profile_id"] = facilityProfile.Id
	}
	// 更新狀態
	if err = db.Model(&job).
		Where("facility_id = ?", req.FacilityId).
		Where("id = ?", req.JobId).
		Updates(updateMap).Error; err != nil {
		return err
	}

	if req.Status == model.JobStatusCancel {
		// 取消 Job 時,取消所有申請了的 Application
		var applications []model.JobApplication
		if err = db.Clauses(clause.Locking{Strength: "UPDATE"}).Where("job_id = ? AND facility_id = ?", req.JobId, req.FacilityId).Where("status <>  ?", model.JobApplicationStatusWithdraw).Find(&applications).Error; err != nil {
			return err
		}
		for _, application := range applications {
			switch application.Status {
			case model.JobApplicationStatusAccept:
				job.AcceptedCount = job.AcceptedCount - 1
				application.Status = model.JobApplicationStatusFacilityCancel
			case model.JobApplicationStatusApply, model.JobApplicationStatusChatting, model.JobApplicationStatusInvite:
				application.Status = model.JobApplicationStatusApplicationCancel
			default:
				// 其它 application 無需取消
				continue
			}
			application.CancelTime = &nowTime
			application.CancelReason = cancelReason
			application.CancelUserId = req.ReqUserId
			if err = db.Save(&application).Error; err != nil {
				return err
			}
		}
	}
	return nil
}

// 鎖定機構工作記錄
func (s *jobService) LockFacilityJobRecord(db *gorm.DB, facilityId uint64) error {
	if err := db.
		Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("facility_id = ?", facilityId).
		Find(&[]model.Job{}).Error; xgorm.IsSqlErr(err) {
		return err
	}
	return nil
}

// 鎖定工作
func (s *jobService) LockJob(db *gorm.DB, jobId uint64) error {
	if err := db.
		Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id = ?", jobId).
		Find(&[]model.Job{}).Error; xgorm.IsSqlErr(err) {
		return err
	}
	return nil
}

type JobSearchForProfessionalReq struct {
	CenterLocationLat decimal.Decimal `form:"centerLocationLat" binding:"required"`              // 中心點緯度
	CenterLocationLng decimal.Decimal `form:"centerLocationLng" binding:"required"`              // 中心點經度
	MinLocationLat    decimal.Decimal `form:"minLocationLat"`                                    // 傳0代表不篩選,最左邊的緯度
	MaxLocationLat    decimal.Decimal `form:"maxLocationLat"`                                    // 傳0代表不篩選,最右邊的緯度
	MinLocationLng    decimal.Decimal `form:"minLocationLng"`                                    // 傳0代表不篩選,最上邊的經度
	MaxLocationLng    decimal.Decimal `form:"maxLocationLng"`                                    // 傳0代表不篩選,最下邊的經度
	LocationState     string          `form:"locationState"`                                     // 州
	LocationCity      string          `form:"locationCity"`                                      // 城市
	MinHourlyRate     decimal.Decimal `form:"minHourlyRate"`                                     // 最小時薪
	Radius            decimal.Decimal `form:"radius"`                                            // 半徑 km
	BeginDate         string          `form:"beginDate" binding:"omitempty,datetime=2006-01-02"` // 開始日期
	EndDate           string          `form:"endDate" binding:"omitempty,datetime=2006-01-02"`   // 結束日期
	FacilityId        uint64          `form:"facilityId"`
	ServiceLocationId uint64          `form:"serviceLocationId"`
	WeekdayType       string          `form:"weekdayType" binding:"omitempty,splitin=1 2 3 4 5 6 7"` // 星期幾，逗號拼接, 要按順序 1,2,3,4,5,6,7
	ShiftTimeType     string          `form:"shiftTimeType" binding:"omitempty,oneof=AM PM NIGHT"`   // 班次時間 AM,PM,NIGHT
	JobId             uint64          `form:"jobId"`
	WithOutJobId      uint64          `form:"withOutJobId"` // 排除的工作職位Id
	ReqUserId         uint64          `form:"-"`            // 請求者Id
}

type JobSearchForProfessionalResp struct {
	JobId                  uint64                                  `json:"jobId"`
	JobApplicationId       uint64                                  `json:"jobApplicationId"`
	PositionProfession     string                                  `json:"positionProfession"`
	PositionProfessionName string                                  `json:"positionProfessionName"`
	FacilityId             uint64                                  `json:"facilityId"`
	FacilityName           string                                  `json:"facilityName"`
	ServiceLocationId      uint64                                  `json:"serviceLocationId"`
	ServiceLocationAddress string                                  `json:"serviceLocationAddress"`
	Timezone               string                                  `json:"timezone"`
	ServiceLocationLat     decimal.Decimal                         `json:"serviceLocationLat"`
	ServiceLocationLng     decimal.Decimal                         `json:"serviceLocationLng"`
	ServiceLocationState   string                                  `json:"serviceLocationState"`
	ServiceLocationCity    string                                  `json:"serviceLocationCity"`
	MinExperienceLevel     string                                  `json:"minExperienceLevel"`
	MinExperienceLevelName string                                  `json:"minExperienceLevelName"`
	PreferredGrade         string                                  `json:"preferredGrade"`
	PreferredGradeName     string                                  `json:"preferredGradeName"`
	Specialisation         string                                  `json:"specialisation"`
	SpecialisationName     string                                  `json:"specialisationName"`
	Language               string                                  `json:"language"`
	LanguageName           string                                  `json:"languageName"`
	LanguageRequirement    string                                  `json:"languageRequirement"`
	SupervisionLevel       string                                  `json:"supervisionLevel"`
	SupervisionLevelName   string                                  `json:"supervisionLevelName"`
	MinHourlyRate          decimal.Decimal                         `json:"minHourlyRate"`
	MaxHourlyRate          decimal.Decimal                         `json:"maxHourlyRate"`
	Duration               decimal.Decimal                         `json:"duration"`

	Remark                 string                                  `json:"remark"`
	Distance               decimal.Decimal                         `json:"distance"`     // 距離中心點的距離 (km)
	BenefitsName           string                                  `json:"benefitsName"` // 福利名稱
	ShiftTime              []JobSearchForProfessionalShiftTimeResp `json:"shiftTime" gorm:"-"`
}

// 專業人士搜索工作職位 - 專業人士
func (s *jobService) JobSearchForProfessional(db *gorm.DB, req JobSearchForProfessionalReq, pageSet *xresp.PageSet, sortSet xresp.SortingSet) ([]JobSearchForProfessionalResp, error) {
	var err error
	var resp []JobSearchForProfessionalResp

	var professional model.Professional
	if err = db.Where("data_type = ?", model.ProfessionalDataTypeApproved).Where("user_id = ?", req.ReqUserId).First(&professional).Error; err != nil {
		return resp, err
	}
	var preferredSpecialities []string
	profile, err := professional.UnmarshalProfile(professional.ProfileJson)
	if err != nil {
		return resp, err
	}
	if len(profile.PreferredSpecialities) > 0 {
		for _, speciality := range profile.PreferredSpecialities {
			preferredSpecialities = append(preferredSpecialities, speciality.Speciality)
		}
	}

	// 創建用於搜索的點
	centerPoint := fmt.Sprintf("POINT(%s %s)", req.CenterLocationLng.String(), req.CenterLocationLat.String())

	builder := db.Table("job AS j").
		Select([]string{
			"j.id AS job_id",
			"ja.id AS job_application_id",
			"j.position_profession",
			"j.facility_id",
			"f.break_time_duration_minute AS facility_break_time_duration_minute",
			"f.break_time_every_hour AS facility_break_time_every_hour",
			"fp.name AS facility_name",
			"j.service_location_id",
			"IF(sl.address_extra IS NOT NULL AND sl.address_extra != '', CONCAT(sl.address_extra, ' ', sl.address), sl.address) AS service_location_address",
			"sl.timezone",
			"sl.location_lat AS service_location_lat",
			"sl.location_lng AS service_location_lng",
			"sl.location_state AS service_location_state",
			"sl.location_city AS service_location_city",
			"j.min_experience_level",
			"j.preferred_grade",
			"j.qualification",
			"j.specialisation",
			"j.language",
			"j.language_requirement",
			"j.supervision_level",
			"j.min_hourly_rate",
			"j.max_hourly_rate",
			"j.duration",
			"j.break_time_payable",
			"j.remark",
			"COALESCE(GROUP_CONCAT(b.name ORDER BY b.id ASC SEPARATOR ', '),'') AS benefits_name",
			fmt.Sprintf("ROUND(ST_Distance_Sphere(sl.location_point, ST_GeomFromText('%s', 4326)) / 1000, 2) AS distance", centerPoint),
		}).
		Joins("JOIN facility AS f ON j.facility_id = f.id").
		Joins("JOIN facility_profile fp ON j.facility_id = fp.facility_id AND fp.data_type = ?", model.FacilityProfileDataTypeApproved).
		Joins("JOIN service_location sl ON j.service_location_id = sl.id").
		Joins("LEFT JOIN job_application ja ON j.id = ja.job_id AND ja.status <> ? AND ja.deleted <> ? AND ja.user_id = ?", model.JobApplicationStatusWithdraw, model.JobApplicationDeletedY, req.ReqUserId).
		Joins("LEFT JOIN job_benefit AS jb ON j.id = jb.job_id AND j.facility_id = jb.facility_id").
		Joins("LEFT JOIN benefit AS b ON jb.benefit_id = b.id").
		Joins("LEFT JOIN facility_blacklist AS fb ON j.facility_id = fb.facility_id AND fb.user_id = ?", req.ReqUserId).
		Where("j.status = ?", model.JobStatusPublish).
		Where("ja.id IS NULL").
		Where("j.position_profession = ?", professional.Profession).
		Where("fb.id IS NULL") // 不顯示被拉入黑名單的機構的工作職位

	// 1. 發佈時間小於等於現在時間
	// 2. 開始時間大於1小時後
	nowTime := time.Now().UTC().Truncate(time.Second)
	oneHourAfter := nowTime.Add(time.Hour).Format(xtool.DateTimeSecA1) // 1小時後
	builder = builder.Where("j.publish_time <= ?", nowTime).
		Where("j.begin_time > ?", oneHourAfter)

	if req.FacilityId > 0 {
		builder = builder.Where("j.facility_id = ?", req.FacilityId)
	}
	if req.ServiceLocationId > 0 {
		builder = builder.Where("j.service_location_id = ?", req.ServiceLocationId)
	}
	if req.JobId > 0 {
		builder = builder.Where("j.id = ?", req.JobId)
	}
	if req.WithOutJobId > 0 {
		builder = builder.Where("j.id <> ?", req.WithOutJobId)
	}
	if professional.Profession == model.ProfessionalProfessionMedicalPractitioner {
		// TODO 這裡的匹配需要修改
		//if professional.PreferredGrade != "" {
		//	var preferredGrades []model.Selection
		//	if err = db.Where("selection_type = ?", model.SelectionTypePreferredGrade).Order("seq").Find(&preferredGrades).Error; err != nil {
		//		return resp, err
		//	}
		//	filteredGrade := make([]string, 0)
		//	for _, grade := range preferredGrades {
		//		filteredGrade = append(filteredGrade, grade.Code)
		//		if grade.Code == professional.PreferredGrade {
		//			break
		//		}
		//	}
		//	builder = builder.Where("j.preferred_grade IN (?)", filteredGrade)
		//}
		if professional.ExperienceLevel != "" {
			builder = builder.Where("j.min_experience_level <= ?", professional.ExperienceLevel)
		}
		//if professional.PreferredSpecialities != "" {
		//	builder = builder.Where("j.specialisation IN (?)", strings.Split(professional.PreferredSpecialities, ","))
		//}
	}
	if professional.Profession == model.ProfessionalProfessionEnrolledNurse {
		if len(preferredSpecialities) > 0 {
			builder = builder.Where("j.specialisation IN (?)", preferredSpecialities)
		}
	}
	if professional.Profession == model.ProfessionalProfessionRegisteredNurse {
		if professional.ExperienceLevel != "" {
			builder = builder.Where("j.min_experience_level <= ?", professional.ExperienceLevel)
		}
		if len(preferredSpecialities) > 0 {
			builder = builder.Where("j.specialisation IN (?)", preferredSpecialities)
		}
	}
	if professional.Profession == model.ProfessionalProfessionPersonalCareWorker {
		if professional.ExperienceLevel != "" {
			builder = builder.Where("j.min_experience_level <= ?", professional.ExperienceLevel)
		}
		if len(preferredSpecialities) > 0 {
			builder = builder.Where("j.specialisation IN (?)", preferredSpecialities)
		}
	}
	// 如果指定了半徑，按半徑過濾
	if !req.Radius.IsZero() {
		builder = builder.Where(fmt.Sprintf("ST_Distance_Sphere(sl.location_point, ST_GeomFromText('%s', 4326)) / 1000 <= ?", centerPoint), req.Radius.String())
	}
	// 如果指定方形區域,按方形區域過濾
	if !req.MinLocationLat.IsZero() && !req.MaxLocationLat.IsZero() && !req.MinLocationLng.IsZero() && !req.MaxLocationLng.IsZero() {
		minLat, _ := req.MinLocationLat.Float64()
		maxLat, _ := req.MaxLocationLat.Float64()
		minLng, _ := req.MinLocationLng.Float64()
		maxLng, _ := req.MaxLocationLng.Float64()
		builder = builder.Where("sl.location_lat BETWEEN ? AND ?", minLat, maxLat).
			Where("sl.location_lng BETWEEN ? AND ?", minLng, maxLng)
	}
	// 如果指定了州，按州過濾
	if req.LocationState != "" {
		builder = builder.Where("sl.location_state = ?", req.LocationState)
	}

	// 如果指定了城市，按城市過濾
	if req.LocationCity != "" {
		builder = builder.Where("sl.location_city = ?", req.LocationCity)
	}

	// 如果指定了最小時薪，按最小時薪過濾
	if !req.MinHourlyRate.IsZero() {
		builder = builder.Where("j.min_hourly_rate >= ?", req.MinHourlyRate.String())
	}

	if req.BeginDate != "" {
		builder = builder.Where("DATE_FORMAT(CONVERT_TZ(j.begin_time, 'UTC', sl.timezone), '%Y-%m-%d') >= ?", req.BeginDate)
	}

	if req.EndDate != "" {
		builder = builder.Where("DATE_FORMAT(CONVERT_TZ(j.end_time, 'UTC', sl.timezone), '%Y-%m-%d') <= ?", req.EndDate)
	}

	// 如果指定了星期幾，按星期幾過濾
	if req.WeekdayType != "" {
		allWeekday := []string{"1", "2", "3", "4", "5", "6", "7"}
		reqWeekdayType := strings.Split(req.WeekdayType, ",")
		// 找出不在可用範圍內的weekday
		notInWeekday := make([]string, 0)

		// 遍歷所有星期，找出不在請求中的星期
		for _, weekday := range allWeekday {
			found := false
			for _, reqWeekday := range reqWeekdayType {
				if weekday == reqWeekday {
					found = true
					break
				}
			}
			if !found {
				notInWeekday = append(notInWeekday, weekday)
			}
		}

		// 使用不在請求中的星期構建過濾條件
		if len(notInWeekday) > 0 {
			// 檢查工作的星期類型是否包含不可用的星期
			for _, weekday := range notInWeekday {
				builder = builder.Where("NOT FIND_IN_SET(?, j.weekday_type)", weekday)
			}
		}
	}

	// 如果指定了班次時間，按班次時間過濾
	if req.ShiftTimeType != "" {
		builder = builder.Where("j.shift_time_type = ?", req.ShiftTimeType)
	}

	// 查詢工作可用性設置
	jobAvailabilitySettings, err := ProfessionalJobAvailabilityService.Inquire(db, req.ReqUserId)
	if err != nil {
		return resp, err
	}
	if len(jobAvailabilitySettings.Settings) > 0 {
		builder = builder.Joins("LEFT JOIN job_shift AS js ON j.id = js.job_id")
		jobSql := db.Select("DISTINCT c_j.id").Table("job_shift AS c_js").Joins("JOIN job AS c_j ON c_js.job_id = c_j.id").
			Joins("JOIN service_location AS c_sl ON c_j.service_location_id = c_sl.id").
			Where("c_j.status = ?", model.JobStatusPublish).
			Where("c_j.position_profession = ?", professional.Profession)

		whereAvailableDateSqlArr := make([]*gorm.DB, 0)
		whereUnavailableDateSqlArr := make([]*gorm.DB, 0)
		whereAvailableWeekdaySqlArr := make([]*gorm.DB, 0)

		for _, jobAvailabilitySetting := range jobAvailabilitySettings.Settings {
			beginTime := jobAvailabilitySetting.BeginDate + " 00:00:00"
			endTime := jobAvailabilitySetting.EndDate + " 23:59:59"

			beginTimeField := fmt.Sprintf("CONVERT_TZ(%s, 'UTC', %s)", "c_js.begin_time", "c_sl.timezone")
			endTimeField := fmt.Sprintf("CONVERT_TZ(%s, 'UTC', %s)", "c_js.end_time", "c_sl.timezone")
			switch jobAvailabilitySetting.FilterType {
			case model.ProfessionalJobAvailabilityFilterTypeAvailableDate:
				// 找出不在可用範圍內的數據
				whereAvailableDateSqlArr = append(whereAvailableDateSqlArr, db.Where(fmt.Sprintf("%s NOT BETWEEN ? AND ?", beginTimeField), beginTime, endTime).
					Or(fmt.Sprintf("%s NOT BETWEEN ? AND ?", endTimeField), beginTime, endTime))
			case model.ProfessionalJobAvailabilityFilterTypeUnavailableDate:
				// 找出在不可用範圍內的數據
				whereUnavailableDateSqlArr = append(whereUnavailableDateSqlArr, db.Where(fmt.Sprintf("%s BETWEEN ? AND ?", beginTimeField), beginTime, endTime).
					Or(fmt.Sprintf("%s BETWEEN ? AND ?", endTimeField), beginTime, endTime))
			case model.ProfessionalJobAvailabilityFilterTypeAvailableWeekday:
				// 找出不在可用範圍內的數據,在mysql將begin Time和end Time轉換為星期幾
				whereAvailableWeekdaySqlArr = append(whereAvailableWeekdaySqlArr, db.Where(fmt.Sprintf("NOT FIND_IN_SET(WEEKDAY(%s)+1, ?)", beginTimeField), jobAvailabilitySetting.Value).
					Or(fmt.Sprintf("NOT FIND_IN_SET(WEEKDAY(%s)+1, ?)", endTimeField), jobAvailabilitySetting.Value))
			}
		}
		whereSettingSql := db
		if len(whereAvailableWeekdaySqlArr) > 0 {
			for _, d := range whereAvailableWeekdaySqlArr {
				whereSettingSql = whereSettingSql.Or(d)
			}
		}
		if len(whereAvailableDateSqlArr) > 0 {
			whereAvailableDateSql := db
			for _, d := range whereAvailableDateSqlArr {
				whereAvailableDateSql = whereAvailableDateSql.Where(d)
			}
			whereSettingSql = whereSettingSql.Or(whereAvailableDateSql)
		}
		if len(whereUnavailableDateSqlArr) > 0 {
			whereUnavailableDateSql := db
			for _, d := range whereUnavailableDateSqlArr {
				whereUnavailableDateSql = whereUnavailableDateSql.Or(d)
			}
			whereSettingSql = whereSettingSql.Or(whereUnavailableDateSql)
		}
		jobSql = jobSql.Where(whereSettingSql)
		builder = builder.Where("j.id NOT IN (?)", jobSql)
	}

	// 添加按距離排序
	sortKeyList := map[string]string{
		"hourlyRate": "j.max_hourly_rate", // 按最高時薪降序
		"duration":   "j.duration",
		"distance":   "distance",
		"beginTime":  "j.begin_time",
	}

	sortSet.DefaultSortingSql = "distance" // sortSet 沒有傳入時,使用 距離排序
	if err = builder.
		Group("j.id").
		Scopes(xresp.AddOrder(sortSet, sortKeyList)).
		Order("j.id").
		Find(&resp).Error; err != nil {
		return resp, err
	}

	// 篩選符合用戶語言需求的工作
	userLangs := strings.Split(professional.Language, ",")
	userLangSet := make(map[string]struct{}, len(userLangs))
	for _, lang := range userLangs {
		userLangSet[strings.TrimSpace(lang)] = struct{}{}
	}
	result := make([]JobSearchForProfessionalResp, 0)
	for _, job := range resp {
		jobLangs := strings.Split(job.Language, ",")
		for i := range jobLangs {
			jobLangs[i] = strings.TrimSpace(jobLangs[i])
		}
		if s.userMatchesJob(userLangSet, jobLangs, job.LanguageRequirement) {
			result = append(result, job)
		}
	}

	// 手動分頁處理
	totalCount := len(result)
	offset := (pageSet.PageIndex - 1) * pageSet.PageSize
	if offset >= totalCount {
		result = []JobSearchForProfessionalResp{}
	} else {
		end := offset + pageSet.PageSize
		if end > totalCount {
			end = totalCount
		}
		result = result[offset:end]
	}

	// 將篩選/分頁結果賦值給 resp
	resp = result

	// 更新分頁信息
	pageSet.Total = int64(totalCount)
	pageSet.Total = int64((totalCount + pageSet.PageSize - 1) / pageSet.PageSize)

	// 獲取各種代碼名稱映射關係
	var professionSectionMap map[string]string
	var experienceLevelSectionMap map[string]string
	var preferredGradeSectionMap map[string]string
	var languageSectionMap map[string]string
	var specialisationSectionMap map[string]string
	var supervisionLevelSectionMap map[string]string

	if len(resp) > 0 {
		professionSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
		if err != nil {
			return resp, err
		}
		experienceLevelSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeExperienceLevelMedicalPractitioner, model.SelectionTypeExperienceLevelRegisteredNurse})
		if err != nil {
			return resp, err
		}
		preferredGradeSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypePreferredGrade})
		if err != nil {
			return resp, err
		}
		languageSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeLanguage})
		if err != nil {
			return resp, err
		}
		var medicalPractitionerSectionCode []string
		medicalPractitionerSectionCode, err = SelectionService.GetCodeByType(db, model.SelectionTypePreferredSpecialtyMedicalPractitioner)
		if err != nil {
			return resp, err
		}
		specialisationSelectionTypes := []string{model.SelectionTypePreferredSpecialtyNurse, model.SelectionTypePreferredSpecialtyPersonalCareWorker}
		specialisationSelectionTypes = append(specialisationSelectionTypes, medicalPractitionerSectionCode...)
		specialisationSectionMap, err = SelectionService.GetCodeNameMap(db, specialisationSelectionTypes)
		if err != nil {
			return resp, err
		}
		supervisionLevelSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeSupervisionRequirement})
		if err != nil {
			return resp, err
		}
	}

	jobIds := make([]uint64, 0)
	for _, item := range resp {
		jobIds = append(jobIds, item.JobId)
	}

	shiftTimesMap, err := JobShiftTimeService.SetJobShiftTimeList(db, jobIds)
	if len(jobIds) > 0 {
		for i, job := range resp {
			resp[i].ShiftTime = shiftTimesMap[job.JobId]

			// 進行代碼值到名稱的轉換
			if job.PositionProfession != "" {
				resp[i].PositionProfessionName = professionSectionMap[job.PositionProfession]
			}
			if job.MinExperienceLevel != "" {
				resp[i].MinExperienceLevelName = experienceLevelSectionMap[job.MinExperienceLevel]
			}
			if job.PreferredGrade != "" {
				resp[i].PreferredGradeName = preferredGradeSectionMap[job.PreferredGrade]
			}
			if job.Specialisation != "" {
				resp[i].SpecialisationName = specialisationSectionMap[job.Specialisation]
			}
			if job.SupervisionLevel != "" {
				resp[i].SupervisionLevelName = supervisionLevelSectionMap[job.SupervisionLevel]
			}
			if job.Language != "" {
				languageList := strings.Split(job.Language, ",")
				var languageNameList []string
				for _, language := range languageList {
					languageNameList = append(languageNameList, languageSectionMap[language])
				}
				resp[i].LanguageName = strings.Join(languageNameList, ",")
			}
		}
	}

	return resp, nil
}
func (s *jobService) userMatchesJob(userLangSet map[string]struct{}, jobLangs []string, requirement string) bool {
	if requirement == "N" {
		return true
	}

	for _, lang := range jobLangs {
		if _, ok := userLangSet[lang]; !ok {
			return false
		}
	}
	return true
}

type ProfessionalJobInquireReq struct {
	JobId     uint64 `form:"jobId" binding:"required"` // 工作職位Id
	ReqUserId uint64 `form:"-"`
}

// JobInquireResp 查詢工作職位響應
type ProfessionalJobInquireResp struct {
	JobId                  uint64                                  `json:"jobId"`                  // 工作職位Id
	FacilityId             uint64                                  `json:"facilityId"`             // 所屬機構Id
	JobApplicationId       uint64                                  `json:"jobApplicationId"`       // 工作申請Id
	FacilityName           string                                  `json:"facilityName"`           // 所屬機構名稱
	JobNo                  string                                  `json:"jobNo"`                  // 工作編號
	PositionProfession     string                                  `json:"positionProfession"`     // 職位專業
	NumberOfPeople         int32                                   `json:"numberOfPeople"`         // 所需人數
	ServiceLocationId      uint64                                  `json:"serviceLocationId"`      // 服務地點Id
	ServiceLocationAddress string                                  `json:"serviceLocationAddress"` // 服務地點地址
	ServiceLocationLat     decimal.Decimal                         `json:"serviceLocationLat"`
	ServiceLocationLng     decimal.Decimal                         `json:"serviceLocationLng"`
	ServiceLocationState   string                                  `json:"serviceLocationState"`
	ServiceLocationCity    string                                  `json:"serviceLocationCity"`
	Timezone               string                                  `json:"timezone"`
	MinExperienceLevel     string                                  `json:"minExperienceLevel"`              // 最低職級要求
	PreferredGrade         string                                  `json:"preferredGrade"`                  // 首選級別
	Qualification          string                                  `json:"qualification"`                   // 護理資格
	Specialisation         string                                  `json:"specialisation"`                  // 專業要求
	Language               string                                  `json:"language"`                        // 語言要求
	SupervisionLevel       string                                  `json:"supervisionLevel"`                // 監督級別
	HourlyRate             decimal.Decimal                         `json:"hourlyRate"`                      // 時薪

	ShiftAllocation        string                                  `json:"shiftAllocation"`                 // 班次分配方式
	Remark                 string                                  `json:"remark"`                          // 備註
	Status                 string                                  `json:"status"`                          // 職位狀態 PENDING PUBLISH DISABLE COMPLETE CANCEL
	PaymentTerms           string                                  `json:"paymentTerms"`                    // 付款條件
	BeginTime              string                                  `json:"beginTime"`                       // 工作開始時間
	EndTime                string                                  `json:"endTime"`                         // 工作結束時間
	JobShiftItems          []JobSearchForProfessionalShiftTimeResp `json:"jobShiftItems" gorm:"-"`          // 班次時間
	PreferredGradeName     string                                  `json:"preferredGradeName" gorm:"-"`     // 首選級別名稱
	MinExperienceLevelName string                                  `json:"minExperienceLevelName" gorm:"-"` // 最低職級要求名稱
	PositionProfessionName string                                  `json:"positionProfessionName" gorm:"-"` // 職位專業名稱
	SupervisionLevelName   string                                  `json:"supervisionLevelName" gorm:"-"`   // 監督級別名稱
	LanguageName           string                                  `json:"languageName" gorm:"-"`           // 語言名稱
	QualificationName      string                                  `json:"qualificationName" gorm:"-"`      // 護理資格名稱
	SpecialisationName     string                                  `json:"specialisationName"`              // 專業名稱
	BenefitsName           string                                  `json:"benefitsName"`                    // 福利名稱
	Duration               decimal.Decimal                         `json:"duration"`                        // 總工作時長（小時）
	PayHours               decimal.Decimal                         `json:"payHours"`                        // 支付時長（小時）
	SplitType              string                                  `json:"splitType"`                       // 分拆類型 NO DATE SHIFT
	AlreadyApplied         string                                  `json:"alreadyApplied"`                  // 是否已申請
	ApplicationStatus      string                                  `json:"applicationStatus"`               // 申請狀態 APPLY, CHATTING, INVITE, REJECT, DECLINE, ACCEPT, UPCOMING, APPLICATION_CANCEL FACILITY_CANCEL, PROFESSIONAL_CANCEL
}

// 檢查工作職位ID是否存在(對於Professional來講) - 專業人士
func (s *jobService) CheckIdExistForProfessional(db *gorm.DB, id uint64, reqUserId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job.id.does_not_exist",
		Other: "No such record, please try after reloading.",
	}
	var err error

	// 曾經申請就可以查看,不考慮後續的篩選
	var applicant model.JobApplication
	if err = db.Where("job_id = ?", id).Where("user_id = ?", reqUserId).First(&applicant).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if applicant.Id > 0 {
		return true, i18n.Message{}, nil
	}

	result, err := s.JobSearchForProfessional(db, JobSearchForProfessionalReq{
		ReqUserId: reqUserId,
		JobId:     id,
	}, &xresp.PageSet{}, xresp.SortingSet{})
	if err != nil {
		return false, i18n.Message{}, err
	}
	if len(result) == 0 {
		return false, msg, nil
	}
	return true, msg, nil
}

// 檢查專業人士是否可以申請工作職位 - 專業人士
func (s *jobService) CheckJobCanApplyByProfessional(db *gorm.DB, jobId uint64, reqUserId uint64) (bool, i18n.Message, error) {
	// 已申請
	appliedMsg := i18n.Message{
		ID:    "alerter.job.already_apply",
		Other: "You have already applied for this job.",
	}
	// 已招聘滿
	fullMsg := i18n.Message{
		ID:    "alerter.job.full",
		Other: "This job is full.",
	}
	// 已過招聘時間
	expiredMsg := i18n.Message{
		ID:    "alerter.job.expired",
		Other: "The job has expired.",
	}
	// 此職位沒有正在招聘
	notRecruitingMsg := i18n.Message{
		ID:    "alerter.job.not_recruiting",
		Other: "This job is not recruiting.",
	}
	// 職位未發佈
	notPublishedMsg := i18n.Message{
		ID:    "alerter.job.not_published",
		Other: "This job is not published.",
	}

	var err error
	var jobApplication model.JobApplication
	if err = db.Where("job_id = ?", jobId).Where("status <> ?", model.JobApplicationStatusWithdraw).Where("user_id = ?", reqUserId).First(&jobApplication).Error; xgorm.IsSqlErr(err) {
		return false, i18n.Message{}, err
	}
	if jobApplication.Id > 0 {
		return false, appliedMsg, nil
	}

	var job model.Job
	if err = db.Where("id = ?", jobId).First(&job).Error; err != nil {
		return false, i18n.Message{}, err
	}

	if job.Status != model.JobStatusPublish {
		return false, notRecruitingMsg, nil
	}

	if job.ServiceLocationId == 0 {
		return false, notPublishedMsg, nil
	}

	var serviceLocation model.ServiceLocation
	if err = db.Where("id = ?", job.ServiceLocationId).First(&serviceLocation).Error; err != nil {
		return false, i18n.Message{}, err
	}

	tz, err := time.LoadLocation(serviceLocation.Timezone)
	if err != nil {
		return false, i18n.Message{}, err
	}

	nowTime := time.Now().In(tz)
	oneHourAfter := nowTime.Add(time.Hour) // 1小時後
	if job.BeginTime.In(tz).Before(oneHourAfter) {
		// 過了招聘時間
		return false, expiredMsg, nil
	}

	if job.AcceptedCount >= job.NumberOfPeople {
		return false, fullMsg, nil
	}

	return true, i18n.Message{}, nil
}

// 專業人士查詢工作職位 - 專業人士
func (s *jobService) InquireForProfessional(db *gorm.DB, req ProfessionalJobInquireReq) (ProfessionalJobInquireResp, error) {
	var err error
	var resp ProfessionalJobInquireResp
	var job model.Job
	if err = db.First(&job, req.JobId).Error; err != nil {
		return resp, err
	}
	_ = copier.Copy(&resp, job)
	resp.JobId = job.Id
	var serviceLocation model.ServiceLocation
	if job.ServiceLocationId > 0 {
		if err = db.First(&serviceLocation, job.ServiceLocationId).Error; err != nil {
			return resp, err
		}
	}
	resp.ServiceLocationAddress = serviceLocation.Address
	resp.Timezone = serviceLocation.Timezone
	resp.ServiceLocationLat = serviceLocation.LocationLat
	resp.ServiceLocationLng = serviceLocation.LocationLng
	resp.ServiceLocationState = serviceLocation.LocationState
	resp.ServiceLocationCity = serviceLocation.LocationCity
	if serviceLocation.AddressExtra != "" {
		resp.ServiceLocationAddress = fmt.Sprintf("%s %s", serviceLocation.AddressExtra, serviceLocation.Address)
	}

	var facilityProfile model.FacilityProfile
	if err = db.Where("facility_id = ?", job.FacilityId).Where("data_type = ?", model.FacilityProfileDataTypeApproved).First(&facilityProfile).Error; err != nil {
		return resp, err
	}
	resp.FacilityName = facilityProfile.Name

	// 加載工作班次時間
	var shiftTimes []JobSearchForProfessionalShiftTimeResp
	if err = db.Table("job_shift").
		Select([]string{
			"job_id",
			"begin_time",
			"end_time",
			"duration",
			"break_duration",
			"pay_hours",
			"shift_period",
			"hourly_rate",
		}).
		Where("job_id = ?", job.Id).Order("id").Find(&shiftTimes).Error; err != nil {
		return resp, err
	}
	resp.JobShiftItems = shiftTimes

	// 獲取首選級別的名稱
	if resp.PreferredGrade != "" {
		var selection model.Selection
		if err = db.Where("selection_type = ?", model.SelectionTypePreferredGrade).
			Where("code = ?", resp.PreferredGrade).
			First(&selection).Error; err != nil {
			return resp, err
		}
		resp.PreferredGradeName = selection.Name
	}

	// 獲取最低職級要求的名稱
	if resp.MinExperienceLevel != "" {
		var selection model.Selection
		if err = db.Where("selection_type in (?)", []string{model.SelectionTypeExperienceLevelMedicalPractitioner, model.SelectionTypeExperienceLevelRegisteredNurse}).
			Where("code = ?", resp.MinExperienceLevel).
			First(&selection).Error; err != nil {
			return resp, err
		}
		resp.MinExperienceLevelName = selection.Name
	}

	// 獲取職位專業的名稱
	if resp.PositionProfession != "" {
		var selection model.Selection
		if err = db.Where("selection_type = ?", model.SelectionTypeProfessionalProfession).
			Where("code = ?", resp.PositionProfession).
			First(&selection).Error; err != nil {
			return resp, err
		}
		resp.PositionProfessionName = selection.Name
	}

	// 獲取監督級別的名稱
	if resp.SupervisionLevel != "" {
		var selection model.Selection
		if err = db.Where("selection_type = ?", model.SelectionTypeSupervisionRequirement).
			Where("code = ?", resp.SupervisionLevel).
			First(&selection).Error; err != nil {
			return resp, err
		}
		resp.SupervisionLevelName = selection.Name
	}

	if resp.Language != "" {
		languageSectionMap, err := SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeLanguage})
		if err != nil {
			return resp, err
		}
		languageList := strings.Split(job.Language, ",")
		var languageNameList []string
		for _, language := range languageList {
			languageNameList = append(languageNameList, languageSectionMap[language])
		}
		resp.LanguageName = strings.Join(languageNameList, ",")
	}

	// 獲取護理資格的名稱
	if resp.PositionProfession == model.JobPositionProfessionPersonalCareWorker && resp.Qualification != "" {
		qualificationSectionMap, err := SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalPersonalCareWorkerQualification})
		if err != nil {
			return resp, err
		}
		qualificationList := strings.Split(resp.Qualification, ",")
		var qualificationNameList []string
		for _, qualification := range qualificationList {
			qualificationNameList = append(qualificationNameList, qualificationSectionMap[qualification])
		}
		resp.Qualification = strings.Join(qualificationNameList, ",")
	}

	if job.Specialisation != "" {
		var medicalPractitionerSectionCode []string
		medicalPractitionerSectionCode, err = SelectionService.GetCodeByType(db, model.SelectionTypePreferredSpecialtyMedicalPractitioner)
		if err != nil {
			return resp, err
		}
		specialisationSelectionTypes := []string{model.SelectionTypePreferredSpecialtyNurse, model.SelectionTypePreferredSpecialtyPersonalCareWorker}
		specialisationSelectionTypes = append(specialisationSelectionTypes, medicalPractitionerSectionCode...)
		specialisationSectionMap, err := SelectionService.GetCodeNameMap(db, specialisationSelectionTypes)
		if err != nil {
			return resp, err
		}
		resp.SpecialisationName = specialisationSectionMap[job.Specialisation]
	}

	// 獲取福利的名稱
	if err = db.Table("benefit AS b").
		Joins("JOIN job_benefit AS jb ON b.id = jb.benefit_id").
		Where("jb.job_id = ?", resp.JobId).
		Select("COALESCE(GROUP_CONCAT(b.name ORDER BY b.id ASC SEPARATOR ', '),'') AS benefits").
		Pluck("benefits", &resp.BenefitsName).Error; err != nil {
		return resp, err
	}

	// already applied
	var jobApplication model.JobApplication
	if err = db.Where("job_id = ?", resp.JobId).Where("status <> ?", model.JobApplicationStatusWithdraw).Where("user_id = ?", req.ReqUserId).First(&jobApplication).Error; xgorm.IsSqlErr(err) {
		return resp, err
	}
	if xgorm.IsNotFoundErr(err) {
		resp.AlreadyApplied = "N"
	} else {
		resp.AlreadyApplied = "Y"
		resp.JobApplicationId = jobApplication.Id
		resp.ApplicationStatus = jobApplication.Status
	}
	return resp, nil
}

// 專業人員申請工作請求 - 專業人士
type ProfessionalJobApplyReq struct {
	JobId     uint64 `json:"jobId" binding:"required"` // 工作職位Id
	ReqUserId uint64 `json:"-"`                        // 請求者Id
}

// 專業人員申請工作響應 - 專業人士
type ProfessionalJobApplyResp struct {
	JobApplicationId uint64 `json:"jobApplicationId"` // 工作申請Id
}

// 專業人員申請工作 - 專業人士
func (s *jobService) ApplyForProfessional(db *gorm.DB, req ProfessionalJobApplyReq) (ProfessionalJobApplyResp, error) {
	var resp ProfessionalJobApplyResp

	var job model.Job
	if err := db.Where("id = ?", req.JobId).First(&job).Error; err != nil {
		return resp, err
	}

	var professional model.Professional
	if err := db.Where("data_type = ?", model.ProfessionalDataTypeApproved).Where("user_id = ?", req.ReqUserId).First(&professional).Error; err != nil {
		return resp, err
	}

	var facility model.Facility
	if err := db.Where("id = ?", job.FacilityId).First(&facility).Error; err != nil {
		return resp, err
	}

	application := model.JobApplication{
		FacilityId:       job.FacilityId,
		JobId:            req.JobId,
		ProfessionalId:   professional.Id,
		UserId:           req.ReqUserId,
		Accept:           model.JobApplicationAcceptN,
		Status:           model.JobApplicationStatusApply,
		ApplyTime:        time.Now().UTC().Truncate(time.Second),
		InvoiceGenerated: model.JobApplicationInvoiceGeneratedN,
	}

	// 計算得分
	score, err := s.CalculateScore(db, job, professional)
	if err != nil {
		return resp, err
	}
	application.Score = score

	if err = db.Create(&application).Error; err != nil {
		return resp, err
	}
	resp.JobApplicationId = application.Id

	// 將其它的撤銷記錄標記為已刪除
	if err = db.Model(&model.JobApplication{}).
		Where("job_id = ?", application.JobId).
		Where("user_id = ?", application.UserId).
		Where("id <> ?", application.Id).
		Where("status = ?", model.JobApplicationStatusWithdraw).
		Updates(map[string]interface{}{
			"deleted": model.JobApplicationDeletedY,
		}).Error; err != nil {
		return resp, err
	}

	return resp, nil
}

// 計算專業人士得分 - 專業人士
func (s *jobService) CalculateScore(db *gorm.DB, job model.Job, professional model.Professional) (int32, error) {
	var score int32
	traceId := xgorm.GetDBContextTraceId(db)

	if professional.Profession != job.PositionProfession {
		return score, fmt.Errorf("professional profession %s does not match job position profession %s", professional.Profession, job.PositionProfession)
	}

	// 獲取附加證書數量
	var additionalCertCount int32
	// 從數據庫獲取專業人士的附加證書數量
	profile, err := professional.UnmarshalProfile(professional.ProfileJson)
	if err != nil {
		return score, err
	}
	// 計算附加證書數量
	for _, file := range profile.Files {
		if file.FileCode == model.ProfessionalFileCodeAdditionalCertification {
			additionalCertCount += int32(len(file.ProfessionalFileIds))
		}
	}

	// 檢查是否在該機構有過工作記錄
	hasWorkedInFacility := false
	// todo 從檢查是否在該機構有過工作,有確認過的賬單都屬於工作過
	//if XXX {
	//	hasWorkedInFacility = true
	//}

	// 根據不同專業類型計算分數
	switch professional.Profession {
	case model.ProfessionalProfessionMedicalPractitioner:
		// Medical Practitioner
		// 1. Experience Level：總共8個級別，從低到高每個等級+4分
		var experienceLevelScore int32
		var experienceLevels []model.Selection
		if err = db.Where("selection_type = ?", model.SelectionTypeExperienceLevelMedicalPractitioner).Order("seq").Find(&experienceLevels).Error; err != nil {
			return score, err
		}
		for _, level := range experienceLevels {
			if level.Code == professional.ExperienceLevel {
				experienceLevelScore = level.Seq * 4
				break
			}
		}
		// 2. Preferred Grade：總共6個級別，從低到高每個等級+5分
		var gradeScore int32
		var preferredGrades []model.Selection
		if err = db.Where("selection_type = ?", model.SelectionTypePreferredGrade).Order("seq").Find(&preferredGrades).Error; err != nil {
			return score, err
		}
		// TODO PreferredGrade已遷移
		//for _, grade := range preferredGrades {
		//if grade.Code == professional.PreferredGrade {
		//	gradeScore = grade.Seq * 5
		//	break
		//}
		//}

		// 3. Additional Certification：每個證書2分，上限8分
		var certScore int32
		if additionalCertCount > 0 {
			certScore = additionalCertCount * 2
			if certScore > 8 {
				certScore = 8
			}
		}

		// todo 4. Rating：獲取個人歷史評價平均分數,默認5分
		var ratingScore int32 = 5

		// 綜合得分 = (Minimum Classification + Preferred Grade + Additional Certification) + Rating * 6
		score = experienceLevelScore + gradeScore + certScore + ratingScore*6

		// 添加分數明細日誌
		log.WithField("traceId", traceId).Infof("Medical Practitioner 分數明細 - 專業ID: %d, 職位ID: %d - Experience Level: %d分, Preferred Grade: %d分, Additional Certification: %d分, Rating: %d分 (加權後: %d分), 總分: %d分",
			professional.Id, job.Id, experienceLevelScore, gradeScore, certScore, ratingScore, ratingScore*6, score)

	case model.ProfessionalProfessionEnrolledNurse:
		// Enrolled Nurse
		// 1. Specialisation：總共15個選項，每個等級2分，上限30分
		var specialisationScore int32
		// TODO PreferredGrade已遷移
		//if professional.PreferredSpecialities != "" {
		//	specialisations := strings.Split(professional.PreferredSpecialities, ",")
		//	specialisationCount := len(specialisations)
		//	specialisationScore = int32(specialisationCount * 2)
		//	if specialisationScore > 30 {
		//		specialisationScore = 30
		//	}
		//}

		// 2. Additional Certification：每個證書2分，上限20分
		certScore := additionalCertCount * 2
		if certScore > 20 {
			certScore = 20
		}

		// 3. 是否在該facility有過工作記錄：有+10分，無0分
		var workHistoryScore int32
		if hasWorkedInFacility {
			workHistoryScore = 10
		}

		// todo 4. Rating：獲取個人歷史評價平均分數,默認5分
		var ratingScore int32 = 5

		// 綜合得分 = (Specialisation + Additional Certification + 是否在該facility有過工作記錄) + Rating * 8
		score = specialisationScore + certScore + workHistoryScore + ratingScore*8

		// 添加分數明細日誌
		log.WithField("traceId", traceId).Infof("Enrolled Nurse 分數明細 - 專業ID: %d, 職位ID: %d - Specialisation: %d分, Additional Certification: %d分, 工作記錄: %d分, Rating: %d分 (加權後: %d分), 總分: %d分",
			professional.Id, job.Id, specialisationScore, certScore, workHistoryScore, ratingScore, ratingScore*8, score)

	case model.ProfessionalProfessionRegisteredNurse:
		// Registered Nurse
		// 1. Experience Level：總共6個級別，從低到高每個等級+8分，上限48分
		var experienceLevelScore int32
		var experienceLevels []model.Selection
		if err = db.Where("selection_type = ?", model.SelectionTypeExperienceLevelRegisteredNurse).Order("seq").Find(&experienceLevels).Error; err != nil {
			return score, err
		}
		for _, level := range experienceLevels {
			if level.Code == professional.ExperienceLevel {
				experienceLevelScore = level.Seq * 8
				break
			}
		}

		// TODO PreferredGrade已遷移
		var specialisationScore int32
		//if professional.PreferredSpecialities != "" {
		//	specialisations := strings.Split(professional.PreferredSpecialities, ",")
		//	specialisationCount := len(specialisations)
		//	specialisationScore = int32(specialisationCount * 2)
		//	if specialisationScore > 14 {
		//		specialisationScore = 14
		//	}
		//}

		// 3. Additional Certification：每個證書2分，上限8分
		certScore := additionalCertCount * 2
		if certScore > 8 {
			certScore = 8
		}

		// todo 4. Rating：獲取個人歷史評價平均分數,默認5分
		var ratingScore int32 = 5

		// 綜合得分 = (Experience Level + Specialisation + Additional Certification) + Rating * 6
		score = experienceLevelScore + specialisationScore + certScore + ratingScore*6

		// 添加分數明細日誌
		log.WithField("traceId", traceId).Infof("Registered Nurse 分數明細 - 專業ID: %d, 職位ID: %d - Experience Level: %d分, Specialisation: %d分, Additional Certification: %d分, Rating: %d分 (加權後: %d分), 總分: %d分",
			professional.Id, job.Id, experienceLevelScore, specialisationScore, certScore, ratingScore, ratingScore*6, score)

	case model.ProfessionalProfessionPersonalCareWorker:
		// Personal Care Worker
		// 1. Qualifications：總共5個級別，從低到高每個等級+10分，上限50分，如果有多個Qualifications，按最高級別計算
		var qualificationScore int32

		// 檢查文件中的資格證書
		highestLevel := 0
		for _, file := range profile.Files {
			switch file.FileCode {
			// 1級 - 兩年或以上專業護理/殘疾經驗
			case model.ProfessionalFileCodePersonalCareWorkerQualificationExperienceAgedCareDisability:
				if highestLevel < 1 {
					highestLevel = 1
				}
			// 2級 - Certificate III 相關證書
			case model.ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIAGEDCARE,
				model.ProfessionalFileCodePersonalCareWorkerQualificationCertIIIDisabilities,
				model.ProfessionalFileCodePersonalCareWorkerQualificationCertIIIIndividualSupport,
				model.ProfessionalFileCodePersonalCareWorkerQualificationCertIIIIndividualSupportAgedAged,
				model.ProfessionalFileCodePersonalCareWorkerQualificationCertIIIIndividualSupportDisability,
				model.ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIHomeCommunityCare:
				if highestLevel < 2 {
					highestLevel = 2
				}
			// 3級 - Certificate IV 相關證書
			case model.ProfessionalFileCodePersonalCareWorkerQualificationCertificateIVAGEDCARE,
				model.ProfessionalFileCodePersonalCareWorkerQualificationCertificateIVDisabilities,
				model.ProfessionalFileCodePersonalCareWorkerQualificationCertificateIVHomeCommunityCare:
				if highestLevel < 3 {
					highestLevel = 3
				}
			// 4級 - 正在攻讀護理學位
			case model.ProfessionalFileCodePersonalCareWorkerQualificationWorkingTowardNursing:
				if highestLevel < 4 {
					highestLevel = 4
				}
			// 5級 - 護理或相關健康學位
			case model.ProfessionalFileCodePersonalCareWorkerQualificationDegreeAlliedHealth,
				model.ProfessionalFileCodePersonalCareWorkerQualificationDegreeNursing:
				if highestLevel < 5 {
					highestLevel = 5
				}
			}
		}
		qualificationScore = int32(highestLevel * 10)

		// 2. Specialisation：總共8個選項，每個等級2分，上限12分
		var specialisationScore int32
		// TODO PreferredGrade已遷移
		//if professional.PreferredSpecialities != "" {
		//	specialisations := strings.Split(professional.PreferredSpecialities, ",")
		//	specialisationCount := len(specialisations)
		//	specialisationScore = int32(specialisationCount * 2)
		//	if specialisationScore > 12 {
		//		specialisationScore = 12
		//	}
		//}

		// 3. Additional Certification：每個證書2分，上限8分
		certScore := additionalCertCount * 2
		if certScore > 8 {
			certScore = 8
		}

		// todo 4. Rating：獲取個人歷史評價平均分數,默認5分
		var ratingScore int32 = 5

		// 綜合得分 = (Qualifications + Specialisation + Additional Certification) + Rating * 6
		score = qualificationScore + specialisationScore + certScore + ratingScore*6

		// 添加分數明細日誌
		log.WithField("traceId", traceId).Infof("Personal Care Worker 分數明細 - 專業ID: %d, 職位ID: %d - Qualifications(級別%d): %d分, Specialisation: %d分, Additional Certification: %d分, Rating: %d分 (加權後: %d分), 總分: %d分",
			professional.Id, job.Id, highestLevel, qualificationScore, specialisationScore, certScore, ratingScore, ratingScore*6, score)
	}

	return score, nil
}

// 更新工作日曆備註請求 - 機構
type JobUpdateCalendarNoteReq struct {
	FacilityId uint64 `json:"facilityId" binding:"required"` // 機構Id
	JobId      uint64 `json:"jobId" binding:"required"`      // 工作Id
	Remark     string `json:"remark"`                        // 備註
}

// 檢查是否可以更新工作日曆備註 - 機構
func (s *jobService) CheckCanUpdateCalendarNote(db *gorm.DB, jobId uint64, facilityId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job.id.does_not_exist",
		Other: "No such record, please try after reloading.",
	}
	// 查詢工作記錄
	var job model.Job
	var err error
	if err = db.Where("id = ? AND facility_id = ?", jobId, facilityId).First(&job).Error; xgorm.IsSqlErr(err) {
		return false, i18n.Message{}, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	}
	return true, i18n.Message{}, nil
}

// 更新工作日曆備註 - 機構
func (s *jobService) UpdateCalendarNote(db *gorm.DB, req JobUpdateCalendarNoteReq) error {
	// 更新備註
	if err := db.Model(&model.Job{}).
		Where("facility_id = ?", req.FacilityId).
		Where("id = ?", req.JobId).
		Update("calendar_note", req.Remark).Error; err != nil {
		return err
	}
	return nil
}

// 邀請專業人士加入工作 - 機構
type JobInviteProfessionalReq struct {
	FacilityId       uint64 `json:"facilityId" binding:"required"`       // 機構Id
	JobId            uint64 `json:"jobId" binding:"required"`            // 工作Id
	JobApplicationId uint64 `json:"jobApplicationId" binding:"required"` // 工作申請Id
	InviterUserId    uint64 `json:"-"`                                   // 邀請者Id
}

// 邀請專業人士加入工作 - 機構
func (s *jobService) InviteProfessional(db *gorm.DB, req JobInviteProfessionalReq) error {
	// 將工作申請狀態設置為已邀請
	updateMap := map[string]interface{}{
		"status":          model.JobApplicationStatusInvite,
		"invite_time":     time.Now().UTC().Truncate(time.Second),
		"inviter_user_id": req.InviterUserId,
	}
	if err := db.Model(&model.JobApplication{}).
		Where("id = ?", req.JobApplicationId).
		Updates(updateMap).Error; err != nil {
		return err
	}

	// TODO: 創建邀請信息

	// TODO: 發送邀請通知

	return nil
}

// 撤銷邀請 - 機構
type JobWithdrawInviteReq struct {
	FacilityId       uint64 `json:"facilityId" binding:"required"`       // 機構Id
	JobId            uint64 `json:"jobId" binding:"required"`            // 工作Id
	JobApplicationId uint64 `json:"jobApplicationId" binding:"required"` // 工作申請Id
}

// 撤銷邀請 - 機構
func (s *jobService) WithdrawInvite(db *gorm.DB, req JobWithdrawInviteReq) error {
	// 將工作申請狀態設置為聊天中
	updateMap := map[string]interface{}{
		"status": model.JobApplicationStatusChatting,
	}
	if err := db.Model(&model.JobApplication{}).
		Where("id = ?", req.JobApplicationId).
		Where("status = ?", model.JobApplicationStatusInvite).
		Updates(updateMap).Error; err != nil {
		return err
	}

	// TODO: 發送取消邀請通知

	// TODO: 更新工作申請記錄

	return nil
}

// 獲取工作預計收入金額
func (s *jobService) GetJobExpectedRevenueAmount(db *gorm.DB, jobShiftTimes []JobSearchForProfessionalShiftTimeResp) decimal.Decimal {
	var sum decimal.Decimal
	for _, jobShiftTime := range jobShiftTimes {
		amount := jobShiftTime.PayHours.Mul(jobShiftTime.HourlyRate).Round(2)
		sum = sum.Add(amount)
	}
	return sum.Round(2)
}

// TODO: 獲取工作應收金額
func (s *jobService) GetJobArAmount(db *gorm.DB, jobId uint64) (decimal.Decimal, error) {
	var amount decimal.Decimal

	return amount, nil
}

// TODO: 獲取工作未收金額
func (s *jobService) GetJobOutstandingAmount(db *gorm.DB, jobId uint64) (decimal.Decimal, error) {
	var amount decimal.Decimal

	return amount, nil
}

// region ---------------------------------------------------- 獲取可以申請確認通知單的工作 ----------------------------------------------------

type ConfirmationNoteJobListReq struct {
	FacilityId uint64 `form:"facilityId" json:"facilityId"` // 機構Id
	DocumentId uint64 `form:"documentId" json:"documentId"` // 當前單據Id
	ReqUserId  uint64 `form:"-" json:"-"`                   // 專業人士UserId
}

type ConfirmationNoteJobListResp struct {
	JobApplicationId       uint64                                  `json:"jobApplicationId"`                // 工作申請Id
	JobId                  uint64                                  `json:"jobId"`                           // 工作Id
	FacilityId             uint64                                  `json:"facilityId"`                      // 機構Id
	FacilityName           string                                  `json:"facilityName"`                    // 機構名稱
	FacilityAbn            string                                  `json:"facilityAbn"`                     // 機構ABN
	Address                string                                  `json:"address"`                         // 地址
	AddressExtra           string                                  `json:"addressExtra"`                    // 地址附註
	PaymentTerms           string                                  `json:"paymentTerms"`                    // 支付方式 PAY_IN_ARREARS PAY_UPFRONT
	PositionProfession     string                                  `json:"positionProfession"`              // 職位專業
	PositionProfessionName string                                  `json:"positionProfessionName" gorm:"-"` // 職位專業名稱
	ServiceLocationAddress string                                  `json:"serviceLocationAddress"`          // 服務地點地址
	Timezone               string                                  `json:"timezone"`                        // 時區
	MinHourlyRate          decimal.Decimal                         `json:"minHourlyRate"`                   // 最低時薪
	MaxHourlyRate          decimal.Decimal                         `json:"maxHourlyRate"`                   // 最高時薪
	Duration               string                                  `json:"duration"`                        // 總工作時長
	PayHours               decimal.Decimal                         `json:"payHours"`                        // 總工作時長
	TotalAmount            decimal.Decimal                         `json:"totalAmount" gorm:"-"`            // 總金額
	JobShiftItems          []JobSearchForProfessionalShiftTimeResp `json:"jobShiftItems" gorm:"-"`          // 班次時間
}

func (r *ConfirmationNoteJobListResp) CanApplyConfirmationNote(nowTime time.Time) bool {
	if r.JobShiftItems == nil || len(r.JobShiftItems) == 0 {
		return false
	}
	tz, err := time.LoadLocation(r.Timezone)
	if err != nil {
		return false
	}

	dayCount := 0
	lastDays := ""
	lastTime := time.Time{}
	for _, jobShiftItem := range r.JobShiftItems {
		beginTime := jobShiftItem.BeginTime.In(tz)
		beginTimeDayStr := beginTime.Format(xtool.DateDayA)
		endTime := jobShiftItem.EndTime.In(tz)
		endTimeDayStr := endTime.Format(xtool.DateDayA)

		if beginTimeDayStr != lastDays {
			if beginTimeDayStr == lastDays {
				dayCount += 1
			} else {
				dayCount += 2
			}
		} else {
			if endTimeDayStr != lastDays {
				dayCount += 1
			}
		}
		lastDays = endTimeDayStr
		lastTime = endTime
	}

	if dayCount < 10 {
		// 如果班次時間總天數小於10天，並且最後一個班次時間已經過去，則可以申請確認通知單
		return lastTime.Before(nowTime)
	}
	if lastTime.Before(nowTime) {
		// 如果最後一個班次時間已經過去，則可以申請確認通知單
		return true
	}

	// 如果班次時間總天數大於10天, 並且最後一個班次還未結束
	// 當前已經距離開始時間超過14個自然日，即第15天
	timeDiff := JobService.NaturalDayDiff(lastTime, nowTime)
	if timeDiff > 14 {
		return true
	}

	return false
}

// 計算兩個時間之間的自然日差值
func (s *jobService) NaturalDayDiff(a, b time.Time) int {
	y1, m1, d1 := a.Date()
	y2, m2, d2 := b.Date()
	date1 := time.Date(y1, m1, d1, 0, 0, 0, 0, time.UTC)
	date2 := time.Date(y2, m2, d2, 0, 0, 0, 0, time.UTC)

	return int(date2.Sub(date1).Hours() / 24)
}
func (r *ConfirmationNoteJobListResp) GetTotalAmount() decimal.Decimal {
	var sum decimal.Decimal
	for _, jobShiftTime := range r.JobShiftItems {
		amount := jobShiftTime.PayHours.Round(2).Mul(jobShiftTime.HourlyRate).Round(2)
		sum = sum.Add(amount)
	}
	return sum.Round(2)
}

func (s *jobService) ConfirmationNoteJobList(db *gorm.DB, req ConfirmationNoteJobListReq) ([]ConfirmationNoteJobListResp, error) {
	var err error
	var resp []ConfirmationNoteJobListResp
	nowTime := time.Now().UTC().Truncate(time.Second)
	builder := db.Model(&model.JobApplication{}).
		Table("job_application as ja").
		Joins("JOIN job as j ON ja.job_id = j.id").
		Joins("JOIN service_location as sl ON j.service_location_id = sl.id").
		Joins("JOIN facility as f ON j.facility_id = f.id").
		Joins("JOIN facility_profile as fp ON f.id = fp.facility_id").
		Select([]string{
			"ja.id as job_application_id",
			"j.id as job_id",
			"j.facility_id as facility_id",
			"fp.name as facility_name",
			"fp.abn as facility_abn",
			"fp.address as address",
			"fp.address_extra as address_extra",
			"fp.payment_terms as payment_terms",
			"j.position_profession as position_profession",
			"sl.address as service_location_address",
			"sl.timezone as timezone",
			"j.min_hourly_rate as min_hourly_rate",
			"j.max_hourly_rate as max_hourly_rate",
			"j.duration as duration",
			"j.pay_hours as pay_hours",
		}).
		Where("ja.user_id = ?", req.ReqUserId).
		Where("ja.deleted <> ?", model.JobApplicationDeletedY).
		// 已接受邀請且已接受工作 或 已申請工作且已取消時間>=工作開始時間減1小時
		Where("(ja.accept = ? AND ja.status = ?) OR (ja.status = ? AND ja.cancel_time >= DATE_SUB(j.begin_time, INTERVAL 1 HOUR))", model.JobApplicationAcceptY, model.JobApplicationStatusAccept, model.JobApplicationStatusFacilityCancel).
		Where("j.begin_time < ?", nowTime)
	if req.FacilityId != 0 {
		builder = builder.Where("j.facility_id = ?", req.FacilityId)
	}
	if err = builder.
		Group("ja.id").
		Order("j.begin_time").
		Order("j.id").
		Find(&resp).Error; err != nil {
		return nil, err
	}
	if len(resp) == 0 {
		return resp, nil
	}
	var jobIds []uint64
	for _, job := range resp {
		jobIds = append(jobIds, job.JobId)
	}

	// 查詢工作班次時間
	var shiftTimesMap map[uint64][]JobSearchForProfessionalShiftTimeResp
	shiftTimesMap, err = JobShiftTimeService.SetJobShiftTimeList(db, jobIds)
	if err != nil {
		return resp, err
	}
	// 查詢這些工作班次是否已經提交確認通知單
	var jobShiftIds []uint64
	for _, job := range resp {
		for _, shiftTime := range shiftTimesMap[job.JobId] {
			jobShiftIds = append(jobShiftIds, shiftTime.JobShiftId)
		}
	}
	var submittedJobShiftIds []string // application_id-job_shift_id
	jobShiftBuilder := db.Model(&model.DocumentItem{}).
		Table("document_item as di").
		Joins("JOIN document as d ON di.document_id = d.id AND d.category = ? AND d.progress <> ?", model.DocumentCategoryConfirmation, model.DocumentProgressCancel).
		Where("di.job_shift_id IN (?)", jobShiftIds)

	if req.DocumentId != 0 {
		jobShiftBuilder = jobShiftBuilder.Where("di.document_id <> ?", req.DocumentId)
	}
	if err = jobShiftBuilder.
		Pluck("CONCAT(d.job_application_id, '-', di.job_shift_id)", &submittedJobShiftIds).
		Error; xgorm.IsSqlErr(err) {
		return resp, err
	}

	// 查詢職位專業名稱
	var professionSectionMap map[string]string
	professionSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
	if err != nil {
		return resp, err
	}

	for i := len(resp) - 1; i >= 0; i-- {
		resp[i].JobShiftItems = shiftTimesMap[resp[i].JobId]
		resp[i].TotalAmount = resp[i].GetTotalAmount()
		resp[i].PositionProfessionName = professionSectionMap[resp[i].PositionProfession]
		// 刪除已提交確認通知單的工作
		noAllSubmitted := true
		for _, jobShiftItem := range resp[i].JobShiftItems {
			if !lo.Contains(submittedJobShiftIds, fmt.Sprintf("%d-%d", resp[i].JobApplicationId, jobShiftItem.JobShiftId)) {
				noAllSubmitted = false
				break
			}
		}
		if noAllSubmitted {
			resp = append(resp[:i], resp[i+1:]...)
		}
	}

	// 判斷是否可以申請確認通知單
	for i := range resp {
		if !resp[i].CanApplyConfirmationNote(nowTime) {
			resp = append(resp[:i], resp[i+1:]...)
		}
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 獲取可以申請確認通知單的工作 ----------------------------------------------------

// region ---------------------------------------------------- 獲取申請確認通知單工作的班次 ----------------------------------------------------

type JobShiftTimeListReq struct {
	DocumentId uint64 `form:"documentId" json:"documentId"`                    // 單據Id
	FacilityId uint64 `form:"facilityId" json:"facilityId" binding:"required"` // 機構Id
	JobId      uint64 `form:"jobId" json:"jobId" binding:"required"`           // 工作Id
	ReqUserId  uint64 `form:"-" json:"-"`                                      // 專業人士UserId
}

type JobShiftTimeListResp struct {
	JobId            uint64          `json:"jobId"`                // 工作Id
	JobShiftId       uint64          `json:"jobShiftId"`           // 工作Id
	BeginTime        time.Time       `json:"beginTime"`            // 開始時間
	EndTime          time.Time       `json:"endTime"`              // 結束時間
	Duration         decimal.Decimal `json:"duration"`             // 工作時長
	BreakDuration    decimal.Decimal `json:"breakDuration"`        // 休息時長

	PayHours         decimal.Decimal `json:"payHours"`             // 工作時長
	HourlyRate       decimal.Decimal `json:"hourlyRate"`           // 時薪
	Status           string          `json:"status" gorm:"-"`      // 狀態  READY NOT_READY SUBMITTED
	TotalAmount      decimal.Decimal `json:"totalAmount" gorm:"-"` // 總金額
	DocumentId       uint64          `json:"-"`                    // 單據Id
}

func (s *jobService) JobShiftTimeList(db *gorm.DB, req JobShiftTimeListReq) ([]JobShiftTimeListResp, error) {
	var err error
	var resp []JobShiftTimeListResp

	documentBuilder := db.Model(&model.Document{}).
		Table("document as d").
		Joins("JOIN document_item as di ON d.id = di.document_id").
		Select("di.job_shift_id, d.id as document_id").
		Where("d.progress <> ?", model.DocumentProgressCancel).
		Where("d.data_type <> ?", model.DocumentDataTypeSystemToFacility).
		Where("d.user_id = ?", req.ReqUserId).
		Group("di.job_shift_id")
	if req.DocumentId > 0 {
		documentBuilder = documentBuilder.Where("d.id <> ?", req.DocumentId)
	}

	builder := db.Model(&model.JobShift{}).
		Table("job_shift as js").
		Joins("JOIN job as j ON js.job_id = j.id").
		Joins("JOIN job_application AS ja ON js.job_id = ja.job_id AND ja.user_id = ? AND ja.accept = ? AND ja.status = ?", req.ReqUserId, model.JobApplicationAcceptY, model.JobApplicationStatusAccept).
		Joins("LEFT JOIN (?) AS di ON js.id = di.job_shift_id", documentBuilder).
		Select([]string{
			"js.id as job_shift_id",
			"js.job_id",
			"js.begin_time",
			"js.end_time",
			"js.duration",
			"js.break_duration",
			"js.pay_hours",
			"js.hourly_rate",
			"js.break_time_payable",
			"di.document_id",
		}).
		Where("js.job_id = ?", req.JobId).
		Order("js.begin_time ASC")
	if err = builder.Group("js.id").Find(&resp).Error; err != nil {
		return nil, err
	}
	if len(resp) == 0 {
		resp = []JobShiftTimeListResp{}
		return resp, nil
	}
	var job model.Job
	if err = db.Where("id = ?", req.JobId).First(&job).Error; err != nil {
		return nil, err
	}

	nowTime := time.Now().UTC().Truncate(time.Second)
	for i := range resp {
		if resp[i].DocumentId > 0 {
			// 已提交
			resp[i].Status = "SUBMITTED"
		} else if nowTime.After(resp[i].EndTime) {
			// 可以申請
			resp[i].Status = "READY"
		} else {
			// 不能申請
			resp[i].Status = "NOT_READY"
		}
		resp[i].TotalAmount = resp[i].PayHours.Mul(resp[i].HourlyRate).Round(2)
	}
	// 定義狀態優先級
	statusPriority := map[string]int{
		"READY":     1,
		"NOT_READY": 2,
		"SUBMITTED": 3,
	}
	// 按照狀態優先級排序：READY > NOT_READY > SUBMITTED
	sort.Slice(resp, func(i, j int) bool {
		// 如果狀態不同，按照優先級排序
		if resp[i].Status != resp[j].Status {
			return statusPriority[resp[i].Status] < statusPriority[resp[j].Status]
		}

		// 如果狀態相同，則按照開始時間升序排序
		return resp[i].BeginTime.Before(resp[j].BeginTime)
	})
	return resp, nil
}

// endregion ---------------------------------------------------- 獲取申請確認通知單工作的班次 ----------------------------------------------------

// region ---------------------------------------------------- 機構將班次分配方式改為手動 ----------------------------------------------------

type JobUpdateShiftAllocationReq struct {
	FacilityId uint64 `form:"facilityId" json:"facilityId" binding:"required"` // 機構Id
	JobId      uint64 `form:"jobId" json:"jobId" binding:"required"`           // 工作Id
}

func (s *jobService) JobUpdateShiftAllocation(db *gorm.DB, req JobUpdateShiftAllocationReq) error {
	if err := db.Model(&model.Job{}).
		Where("facility_id = ?", req.FacilityId).
		Where("id = ?", req.JobId).
		Where("shift_allocation = ?", model.JobShiftAllocationAutomatic).
		Update("shift_allocation", model.JobShiftAllocationManual).Error; err != nil {
		return err
	}
	return nil
}

// endregion ---------------------------------------------------- 機構將班次分配方式改為手動 ----------------------------------------------------

// region ---------------------------------------------------- JobAllowance 相關方法 ----------------------------------------------------

// 獲取工作的所有津貼
func (s *jobService) GetJobAllowances(db *gorm.DB, jobId uint64) ([]model.JobAllowance, error) {
	var allowances []model.JobAllowance
	if err := db.Where("job_id = ?", jobId).Find(&allowances).Error; err != nil {
		return nil, err
	}
	return allowances, nil
}

// 計算單個JobShift的津貼金額
func (s *jobService) CalculateJobShiftAllowanceAmount(db *gorm.DB, jobShift model.JobShift) (decimal.Decimal, error) {
	// 獲取該Job的所有津貼
	allowances, err := s.GetJobAllowances(db, jobShift.JobId)
	if err != nil {
		return decimal.Zero, err
	}

	totalAllowance := decimal.Zero

	for _, allowance := range allowances {
		switch allowance.AllowanceType {
		case model.JobAllowanceAllowanceTypeHourly:
			// 按小時津貼：津貼金額 × 工作時長
			amount := allowance.Amount.Mul(jobShift.PayHours)
			totalAllowance = totalAllowance.Add(amount)
		case model.JobAllowanceAllowanceTypeShift:
			// 按班次津貼：固定津貼金額
			totalAllowance = totalAllowance.Add(allowance.Amount)
		case model.JobAllowanceAllowanceTypeJob:
			// 按職位津貼：需要根據該Job的所有JobShift平均分配
			// 這裡先獲取該Job的總班次數
			var totalShifts int64
			if err := db.Model(&model.JobShift{}).Where("job_id = ?", jobShift.JobId).Count(&totalShifts).Error; err != nil {
				return decimal.Zero, err
			}
			if totalShifts > 0 {
				amount := allowance.Amount.Div(decimal.NewFromInt(totalShifts))
				totalAllowance = totalAllowance.Add(amount)
			}
		}
	}

	// 保留兩位小數
	return totalAllowance.Round(2), nil
}

// 更新JobShift的AllowanceAmount字段
func (s *jobService) UpdateJobShiftAllowanceAmount(db *gorm.DB, jobShiftId uint64) error {
	// 獲取JobShift
	var jobShift model.JobShift
	if err := db.First(&jobShift, jobShiftId).Error; err != nil {
		return err
	}

	// 計算津貼金額
	allowanceAmount, err := s.CalculateJobShiftAllowanceAmount(db, jobShift)
	if err != nil {
		return err
	}

	// 更新AllowanceAmount字段
	return db.Model(&jobShift).Update("allowance_amount", allowanceAmount).Error
}

// 計算工作總金額
// 公式：JobShiftItems裡面payHours*HourlyRate*Superannuation(12%)+JobShiftItem的Allowances合計*Superannuation(12%)
func (s *jobService) CalculateGrandTotal(db *gorm.DB, jobShiftItems []JobShiftItem) (decimal.Decimal, error) {
	// 超級年金比例 12%
	superannuationRate := decimal.NewFromFloat(0.12)
	grandTotal := decimal.Zero

	// 計算每個班次的金額
	for _, shiftItem := range jobShiftItems {
		// 計算工作時長金額：payhours * HourlyRate * Superannuation(12%)
		payHoursAmount := shiftItem.PayHours.Mul(shiftItem.HourlyRate)
		payHoursWithSuper := payHoursAmount.Add(payHoursAmount.Mul(superannuationRate))
		grandTotal = grandTotal.Add(payHoursWithSuper)

		// 計算津貼總額
		allowanceTotal := decimal.Zero
		for _, allowance := range shiftItem.Allowances {
			allowanceTotal = allowanceTotal.Add(allowance.Amount)
		}

		// 津貼金額加上超級年金：Allowances合計 * Superannuation(12%)
		allowanceWithSuper := allowanceTotal.Add(allowanceTotal.Mul(superannuationRate))
		grandTotal = grandTotal.Add(allowanceWithSuper)
	}

	return grandTotal, nil
}

// 計算 JobShiftItems 中的最低和最高時薪
func (s *jobService) CalculateHourlyRateRange(jobShiftItems []JobShiftItem) (decimal.Decimal, decimal.Decimal) {
	if len(jobShiftItems) == 0 {
		return decimal.Zero, decimal.Zero
	}

	minRate := jobShiftItems[0].HourlyRate
	maxRate := jobShiftItems[0].HourlyRate

	for _, item := range jobShiftItems {
		if item.HourlyRate.LessThan(minRate) {
			minRate = item.HourlyRate
		}
		if item.HourlyRate.GreaterThan(maxRate) {
			maxRate = item.HourlyRate
		}
	}

	return minRate, maxRate
}

// 校驗前端計算的總金額是否與後端一致
func (s *jobService) CheckGrandTotal(db *gorm.DB, jobShiftItems []JobShiftItem, frontendGrandTotal decimal.Decimal) (bool, i18n.Message, error) {
	// 計算後端總金額
	backendGrandTotal, err := s.CalculateGrandTotal(db, jobShiftItems)
	if err != nil {
		return false, i18n.Message{}, err
	}

	// 比較前端和後端計算的金額（使用精確比較）
	if !frontendGrandTotal.Equal(backendGrandTotal) {
		return false, MsgJobGrandTotalMismatch, nil
	}

	return true, i18n.Message{}, nil
}

// 獲取某個Job的總津貼金額（所有JobShift的津貼金額合計）
func (s *jobService) GetJobTotalAllowanceAmount(db *gorm.DB, jobId uint64) (decimal.Decimal, error) {
	var totalAmount decimal.Decimal
	if err := db.Model(&model.JobShift{}).
		Select("COALESCE(SUM(allowance_amount), 0)").
		Where("job_id = ?", jobId).
		Scan(&totalAmount).Error; err != nil {
		return decimal.Zero, err
	}
	return totalAmount, nil
}

// 計算並更新JobShift的津貼金額，統一處理所有津貼類型和精度誤差
// 計算所有津貼金額並處理誤差分配

func (s *jobService) calculateAllowanceAmountsWithErrorHandling(shiftItems []JobShiftItem, allowanceMap map[uint64]string) ([]decimal.Decimal, [][]JobAllowanceReq, error) {
	totalShifts := len(shiftItems)
	if totalShifts == 0 {
		return nil, nil, nil
	}

	// 初始化結果
	shiftAllowanceAmounts := make([]decimal.Decimal, totalShifts)
	jobAllowancesData := make([][]JobAllowanceReq, totalShifts)

	// 收集所有按職位津貼（JOB類型）用於誤差處理
	jobTypeAllowances := make(map[uint64]decimal.Decimal) // allowanceId -> baseAmount
	jobTypeAllowanceNames := make(map[uint64]string)      // allowanceId -> allowanceName

	// 第一遍：計算 HOURLY 和 SHIFT 類型津貼，收集 JOB 類型津貼
	for i, item := range shiftItems {
		allowanceAmount := decimal.Zero
		var allowanceDataList []JobAllowanceReq

		for _, allowanceReq := range item.Allowances {
			attractsSuperannuation := "N"
			if val, exists := allowanceMap[allowanceReq.AllowanceId]; exists {
				attractsSuperannuation = val
			}

			switch allowanceReq.AllowanceType {
			case model.JobAllowanceAllowanceTypeHourly, model.JobAllowanceAllowanceTypeShift:
				// 計算 HOURLY 和 SHIFT 類型津貼
				calculatedAmount := s.CalculateAllowanceAmount(
					allowanceReq.AllowanceType,
					allowanceReq.BaseAmount,
					item.PayHours,
					totalShifts,
				)
				allowanceAmount = allowanceAmount.Add(calculatedAmount)

				// 複製請求並設置內部字段
				allowanceData := allowanceReq
				allowanceData.AttractsSuperannuation = attractsSuperannuation
				allowanceData.Amount = calculatedAmount

				allowanceDataList = append(allowanceDataList, allowanceData)
			case model.JobAllowanceAllowanceTypeJob:
				// 收集 JOB 類型津貼，稍後統一處理
				jobTypeAllowances[allowanceReq.AllowanceId] = allowanceReq.BaseAmount
				jobTypeAllowanceNames[allowanceReq.AllowanceId] = allowanceReq.AllowanceName
			}
		}

		shiftAllowanceAmounts[i] = allowanceAmount
		jobAllowancesData[i] = allowanceDataList
	}

	// 第二遍：處理 JOB 類型津貼的分配和誤差處理
	for allowanceId, baseAmount := range jobTypeAllowances {
		allowanceName := jobTypeAllowanceNames[allowanceId]
		// 計算每個班次應分配的金額（平均分配）
		averageAmount := baseAmount.Div(decimal.NewFromInt(int64(totalShifts))).Round(2)
		totalDistributed := averageAmount.Mul(decimal.NewFromInt(int64(totalShifts)))
		// 計算誤差
		difference := baseAmount.Sub(totalDistributed)

		// 分配給每個班次
		for i := 0; i < totalShifts; i++ {
			amount := averageAmount
			// 將誤差計入最後一個班次
			if i == totalShifts-1 {
				amount = amount.Add(difference)
			}

			// 獲取 AttractsSuperannuation
			attractsSuperannuation := "N"
			if val, exists := allowanceMap[allowanceId]; exists {
				attractsSuperannuation = val
			}

			// 添加到該班次的津貼數據
			jobAllowancesData[i] = append(jobAllowancesData[i], JobAllowanceReq{
				AllowanceId:            allowanceId,
				AllowanceName:          allowanceName,
				AllowanceType:          model.JobAllowanceAllowanceTypeJob,
				Amount:                 amount,
				AttractsSuperannuation: attractsSuperannuation,
				BaseAmount:             baseAmount,
			})

			// 累加到班次總津貼金額
			shiftAllowanceAmounts[i] = shiftAllowanceAmounts[i].Add(amount)
		}
	}

	for i := range shiftAllowanceAmounts {
		shiftAllowanceAmounts[i] = shiftAllowanceAmounts[i].Round(2)
	}

	return shiftAllowanceAmounts, jobAllowancesData, nil
}

type AllowanceAttractsSuperannuation struct {
	AllowanceId            uint64 `json:"allowanceId"`
	AttractsSuperannuation string `json:"attractsSuperannuation"`
}

// 批量獲取津貼的AttractsSuperannuation映射
func (s *jobService) GetAllowanceAttractsSuperannuationMap(db *gorm.DB, allowanceIds []uint64) (map[uint64]string, error) {
	if len(allowanceIds) == 0 {
		return make(map[uint64]string), nil
	}

	allowanceIds = xtool.Uint64ArrayDeduplication(allowanceIds) // 去重

	var allowances []AllowanceAttractsSuperannuation

	if err := db.Model(&model.Allowance{}).
		Select([]string{
			"id AS allowance_id",
			"attracts_superannuation",
		}).
		Where("id IN ?", allowanceIds).Find(&allowances).Error; err != nil {
		return nil, err
	}

	allowanceMap := make(map[uint64]string)
	for _, allowance := range allowances {
		allowanceMap[allowance.AllowanceId] = allowance.AttractsSuperannuation
	}

	return allowanceMap, nil
}

// 根據津貼類型和工作時長計算津貼金額
// allowanceType: HOURLY, SHIFT, JOB
// baseAmount: 津貼基礎金額（從allowance表的對應字段獲取）
// payHours: 工作時長（僅HOURLY類型使用）
// attractsSuperannuation: 是否包含公務員津貼 Y/N
// totalJobShifts: 總班次數（僅JOB類型使用）
func (s *jobService) CalculateAllowanceAmount(allowanceType string, baseAmount decimal.Decimal, payHours decimal.Decimal, totalJobShifts int) decimal.Decimal {
	var amount decimal.Decimal

	switch allowanceType {
	case model.JobAllowanceAllowanceTypeHourly:
		amount = baseAmount.Mul(payHours)
	case model.JobAllowanceAllowanceTypeShift:
		amount = baseAmount
	case model.JobAllowanceAllowanceTypeJob:
		if totalJobShifts > 0 {
			amount = baseAmount.Div(decimal.NewFromInt(int64(totalJobShifts)))
		} else {
			amount = decimal.Zero
		}
	default:
		amount = decimal.Zero
	}

	return amount.Round(2)
}

// endregion ---------------------------------------------------- JobAllowance 相關方法 ----------------------------------------------------
