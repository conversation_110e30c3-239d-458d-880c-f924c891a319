package services

import (
	"strings"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gorm.io/gorm"
)

type selectionService struct{}

var SelectionService = new(selectionService)

func (s *selectionService) CheckSelectionExist(db *gorm.DB, m *model.Selection, selectionType, selectionCode, status string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.selection.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var err error
	builder := db.Where("selection_type = ? AND code = ?", selectionType, selectionCode)
	if status != "" {
		builder = builder.Where("status = ?", status)
	}
	if err = builder.First(m).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

func (s *selectionService) CheckSelectionsExist(db *gorm.DB, selectionType string, selectionCodeStr string, status string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.selection.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var err error
	var qty int64

	if selectionCodeStr == "" {
		return true, msg, nil
	}

	selectionCodes := strings.Split(selectionCodeStr, ",")
	selectionCodes = xtool.StringArrayDeduplication(selectionCodes)
	builder := db.Model(&model.Selection{}).Where("selection_type = ? AND code IN (?)", selectionType, selectionCodes)
	if status != "" {
		builder = builder.Where("status = ?", status)
	}
	if err = builder.Count(&qty).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if qty != int64(len(selectionCodes)) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

// region ---------------------------------------------------- 列表 ----------------------------------------------------

type SelectionListReqByPublic struct {
	SelectionTypes string `form:"selectionTypes" binding:"required,splitin=PROFESSIONAL_PROFESSION"` // 選項類型，多個用逗號分隔 PROFESSIONAL_PROFESSION=專業
}

type SelectionListReqByApp struct {
	SelectionTypes string `form:"selectionTypes" binding:"required,splitin=PROFESSIONAL_PROFESSION LANGUAGE"` // 選項類型，多個用逗號分隔 PROFESSIONAL_PROFESSION=專業、LANGUAGE=語言
}

type SelectionListReqByProfessional struct {
	SelectionTypes string `form:"selectionTypes" binding:"required,splitin=PROFESSIONAL_PROFESSION PROFESSIONAL_PERMISSION_TO_WORK GENDER RELATIONSHIP PREFERRED_GRADE EXPERIENCE_LEVEL_MEDICAL_PRACTITIONER EXPERIENCE_LEVEL_REGISTERED_NURSE SUPERVISION_REQUIREMENT PREFERRED_SPECIALTY_MEDICAL_PRACTITIONER PREFERRED_SPECIALTY_NURSE PREFERRED_SPECIALTY_PERSONAL_CARE_WORKER PROFESSIONAL_PERSONAL_CARE_WORKER_QUALIFICATION PROFESSIONAL_DISCLOSURE_QUESTION PROFESSIONAL_ID_CHECK_FILE_GROUP_PRIMARY PROFESSIONAL_ID_CHECK_FILE_GROUP_SECONDARY PROFESSIONAL_ID_CHECK_FILE_GROUP_OTHERS"` // 選項類型，多個用逗號分隔 PROFESSIONAL_PROFESSION=專業、PROFESSIONAL_PERMISSION_TO_WORK=工作許可、GENDER=性別、RELATIONSHIP=關係、PREFERRED_GRADE=首選級別、EXPERIENCE_LEVEL_MEDICAL_PRACTITIONER=經驗級別-執業醫生、EXPERIENCE_LEVEL_REGISTERED_NURSE=經驗級別-註冊護士、SUPERVISION_REQUIREMENT=監督要求、PREFERRED_SPECIALTY_MEDICAL_PRACTITIONER=專業-執業醫生、PREFERRED_SPECIALTY_NURSE=專業-護士、PREFERRED_SPECIALTY_PERSONAL_CARE_WORKER=專業-護理員、PROFESSIONAL_PERSONAL_CARE_WORKER_QUALIFICATION=護理員資格證書類型、PROFESSIONAL_DISCLOSURE_QUESTION=專業人士披露問題、PROFESSIONAL_ID_CHECK_FILE_GROUP_PRIMARY=專業人士身份檢查文件組-主要、PROFESSIONAL_ID_CHECK_FILE_GROUP_SECONDARY=專業人士身份檢查文件組-次要、PROFESSIONAL_ID_CHECK_FILE_GROUP_OTHERS=專業人士身份檢查文件組-其他
}

type SelectionListReqByFacility struct {
	SelectionTypes string `form:"selectionTypes" binding:"required,splitin=PROFESSIONAL_PROFESSION PREFERRED_GRADE FACILITY_PROFILE_FACILITY_TYPE SUPERVISION_REQUIREMENT EXPERIENCE_LEVEL_MEDICAL_PRACTITIONER EXPERIENCE_LEVEL_REGISTERED_NURSE PREFERRED_SPECIALTY_MEDICAL_PRACTITIONER PREFERRED_SPECIALTY_NURSE PREFERRED_SPECIALTY_PERSONAL_CARE_WORKER PROFESSIONAL_PERSONAL_CARE_WORKER_QUALIFICATION"` // 選項類型，多個用逗號分隔 PROFESSIONAL_PROFESSION=專業、FACILITY_PROFILE_FACILITY_TYPE=機構類型、SUPERVISION_REQUIREMENT=監督要求、EXPERIENCE_LEVEL_MEDICAL_PRACTITIONER=經驗級別-執業醫生、EXPERIENCE_LEVEL_REGISTERED_NURSE=經驗級別-註冊護士、PREFERRED_SPECIALTY_MEDICAL_PRACTITIONER=專業-執業醫生、PREFERRED_SPECIALTY_NURSE=專業-護士、PREFERRED_SPECIALTY_PERSONAL_CARE_WORKER=專業-護理員、PREFERRED_GRADE=首選級別、PROFESSIONAL_PERSONAL_CARE_WORKER_QUALIFICATION=護理員資格證書類型
}

type SelectionListReq struct {
	SelectionTypes string `form:"selectionTypes" binding:"required,splitin=FACILITY_PROFILE_FACILITY_TYPE PROFESSIONAL_PERMISSION_TO_WORK PROFESSIONAL_PROFESSION GENDER RELATIONSHIP PREFERRED_GRADE EXPERIENCE_LEVEL_MEDICAL_PRACTITIONER EXPERIENCE_LEVEL_REGISTERED_NURSE SUPERVISION_REQUIREMENT PREFERRED_SPECIALTY_MEDICAL_PRACTITIONER PREFERRED_SPECIALTY_NURSE PREFERRED_SPECIALTY_PERSONAL_CARE_WORKER PROFESSIONAL_PERSONAL_CARE_WORKER_QUALIFICATION PROFESSIONAL_DISCLOSURE_QUESTION PROFESSIONAL_ID_CHECK_FILE_GROUP_PRIMARY PROFESSIONAL_ID_CHECK_FILE_GROUP_SECONDARY PROFESSIONAL_ID_CHECK_FILE_GROUP_OTHERS"` // 選項類型，多個用逗號分隔 FACILITY_PROFILE_FACILITY_TYPE=機構類型、PROFESSIONAL_PERMISSION_TO_WORK=工作許可、PROFESSIONAL_PROFESSION=專業、GENDER=性別、RELATIONSHIP=關係、PREFERRED_GRADE=首選級別、EXPERIENCE_LEVEL_MEDICAL_PRACTITIONER=經驗級別-執業醫生、EXPERIENCE_LEVEL_REGISTERED_NURSE=經驗級別-註冊護士、SUPERVISION_REQUIREMENT=監督要求、PREFERRED_SPECIALTY_MEDICAL_PRACTITIONER=專業-執業醫生、PREFERRED_SPECIALTY_NURSE=專業-護士、PREFERRED_SPECIALTY_PERSONAL_CARE_WORKER=專業-護理員、PROFESSIONAL_PERSONAL_CARE_WORKER_QUALIFICATION=護理員資格證書類型、PROFESSIONAL_DISCLOSURE_QUESTION=專業人士披露問題、PROFESSIONAL_ID_CHECK_FILE_GROUP_PRIMARY=專業人士身份檢查文件組-主要、PROFESSIONAL_ID_CHECK_FILE_GROUP_SECONDARY=專業人士身份檢查文件組-次要、PROFESSIONAL_ID_CHECK_FILE_GROUP_OTHERS=專業人士身份檢查文件組-其他
}

type SelectionListResp struct {
	SelectionType string               `json:"selection_type"`              // 選項類型
	Code          string               `json:"code"`                        // 編號
	Name          string               `json:"name"`                        // 名稱
	Seq           int32                `json:"seq"`                         // 排序
	Children      *[]SelectionListResp `json:"children,omitempty" gorm:"-"` // 子選項
}

func (s *selectionService) List(db *gorm.DB, req SelectionListReq) (map[string][]SelectionListResp, error) {
	var list []SelectionListResp
	builder := db.Model(&model.Selection{}).
		Select([]string{"selection_type", "code", "name", "seq"}).
		Where("hidden <> ?", model.SelectionHiddenYes).
		Order("selection_type ASC").
		Order("seq ASC")
	selectionTypes := strings.Split(req.SelectionTypes, ",")
	builder = builder.Where("selection_type IN (?)", selectionTypes)
	if err := builder.Find(&list).Error; xgorm.IsSqlErr(err) {
		return nil, err
	}
	var resp = make(map[string][]SelectionListResp)
	if len(list) == 0 {
		return resp, nil
	}
	for _, selection := range list {
		if _, ok := resp[selection.SelectionType]; !ok {
			resp[selection.SelectionType] = []SelectionListResp{}
		}
		resp[selection.SelectionType] = append(resp[selection.SelectionType], selection)
	}

	codeList := make([]string, 0)
	for _, selections := range resp {
		for _, selection := range selections {
			codeList = append(codeList, selection.Code)
		}
	}
	// 獲取children
	var childrenMap = make(map[string][]SelectionListResp)
	childrenMap, err := s.List(db, SelectionListReq{
		SelectionTypes: strings.Join(codeList, ","),
	})
	if err != nil {
		return nil, err
	}

	for selectionType, selections := range resp {
		for index, selection := range selections {
			if children, ok := childrenMap[selection.Code]; ok {
				resp[selectionType][index].Children = &children
			} else {
				resp[selectionType][index].Children = nil
			}
		}
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 列表 ----------------------------------------------------

// region ---------------------------------------------------- GetCodeByType ----------------------------------------------------

func (s *selectionService) GetCodeByType(db *gorm.DB, selectionType string, status ...string) ([]string, error) {
	var list []string
	builder := db.
		Model(&model.Selection{}).
		Select("code").
		Where("selection_type = ?", selectionType)
	if len(status) > 0 {
		builder = builder.Where("status = ?", status[0])
	}
	if err := builder.
		Order("seq ASC").
		Pluck("code", &list).Error; xgorm.IsSqlErr(err) {
		return nil, err
	}
	return list, nil
}

// endregion ---------------------------------------------------- GetCodeByType ----------------------------------------------------

// region ---------------------------------------------------- GetCodeNameMap ----------------------------------------------------

func (s *selectionService) GetCodeNameMap(db *gorm.DB, selectionTypes []string) (map[string]string, error) {
	var selections []model.Selection
	var selectionMap = make(map[string]string)
	if err := db.Where("selection_type IN (?)", selectionTypes).Find(&selections).Error; err != nil {
		return nil, err
	}
	for _, selection := range selections {
		selectionMap[selection.Code] = selection.Name
	}
	return selectionMap, nil
}

// endregion ---------------------------------------------------- GetCodeNameMap ----------------------------------------------------

// region ---------------------------------------------------- GetMapByCodes ----------------------------------------------------

func (s *selectionService) GetMapByCodes(db *gorm.DB, codes []string) (map[string]string, error) {
	var selections []model.Selection
	var selectionMap = make(map[string]string)
	if err := db.Where("code IN (?)", codes).Find(&selections).Error; err != nil {
		return nil, err
	}
	for _, selection := range selections {
		selectionMap[selection.Code] = selection.Name
	}
	return selectionMap, nil
}

// endregion ---------------------------------------------------- GetMapByCodes ----------------------------------------------------

// region ---------------------------------------------------- FindByCode ----------------------------------------------------

func (s *selectionService) FindByCode(db *gorm.DB, code string) (*model.Selection, error) {
	var err error
	var selection model.Selection
	if err = db.Where("code = ?", code).First(&selection).Error; xgorm.IsSqlErr(err) {
		return nil, err
	}
	if xgorm.IsNotFoundErr(err) {
		return nil, nil
	}
	return &selection, nil
}

// endregion ---------------------------------------------------- FindByCode ----------------------------------------------------
