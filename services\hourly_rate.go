package services

import (
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtool"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

var HourlyRateService = new(hourlyRateService)

type hourlyRateService struct{}

func (s *hourlyRateService) CheckIdsExist(db *gorm.DB, facilityId uint64, items []HourlyRateItem) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.hourly_rate.id.does_not_exist",
		Other: "No such record, please try after reloading.",
	}
	ids := make([]uint64, 0)
	for _, item := range items {
		ids = append(ids, item.HourlyRateId)
	}
	var err error
	ids = xtool.Uint64ArrayDeduplication(ids)
	var qty int64
	if err = db.Model(&model.HourlyRate{}).Where("facility_id = ?", facilityId).Where("id IN (?)", ids).Count(&qty).Error; err != nil {
		return false, msg, err
	}
	if qty != int64(len(ids)) {
		return false, msg, nil
	}
	return true, msg, nil
}

type HourlyRateListReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"`
	Name       string `form:"name"`
	Code       string `form:"code"`
}

type HourlyRateListResp struct {
	HourlyRateId    uint64          `json:"hourlyRateId"`
	FacilityId      uint64          `json:"facilityId"`
	Name            string          `json:"name"`
	Code            string          `json:"code"`
	Rate            decimal.Decimal `json:"rate"`
	PayForBreakTime string          `json:"payForBreakTime"`
}

func (s *hourlyRateService) List(db *gorm.DB, req HourlyRateListReq, pageSet *xresp.PageSet) ([]HourlyRateListResp, error) {
	var err error
	var resp []HourlyRateListResp
	builder := db.Table("hourly_rate AS hr").Select([]string{
		"hr.id AS hourly_rate_id",
		"hr.facility_id",
		"hr.name",
		"hr.code",
		"hr.rate",
		"hr.pay_for_break_time",
	}).Where("hr.facility_id = ?", req.FacilityId)

	if req.Name != "" {
		builder = builder.Where("hr.name LIKE ?", "%"+req.Name+"%")
	}
	if req.Code != "" {
		builder = builder.Where("hr.code LIKE ?", "%"+req.Code+"%")
	}

	if err = builder.Scopes(xresp.Paginate(pageSet)).Order("hr.id").Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

type HourlyRateUpdateReq struct {
	FacilityId uint64           `json:"facilityId" binding:"required"`
	Items      []HourlyRateItem `json:"items" binding:"required"`
}

type HourlyRateItem struct {
	HourlyRateId    uint64          `json:"hourlyRateId" binding:"required"`
	Rate            decimal.Decimal `json:"rate"`
	PayForBreakTime string          `json:"payForBreakTime" binding:"required,oneof=Y N"`
}

func (s *hourlyRateService) Update(db *gorm.DB, req HourlyRateUpdateReq) error {
	var err error

	// 更新記錄
	for _, item := range req.Items {
		var hourlyRate model.HourlyRate
		if err = db.Where("id = ?", item.HourlyRateId).Where("facility_id = ?", req.FacilityId).First(&hourlyRate).Error; err != nil {
			return err
		}
		hourlyRate.Rate = item.Rate
		hourlyRate.PayForBreakTime = item.PayForBreakTime
		if err = db.Save(&hourlyRate).Error; err != nil {
			return err
		}
	}

	return nil
}

func (s *hourlyRateService) Init(db *gorm.DB, facilityId uint64) error {
	var err error

	initItem := []model.HourlyRate{
		{
			FacilityId:      facilityId,
			Name:            "Medical Practitioner",
			Code:            model.ProfessionalProfessionMedicalPractitioner,
			Rate:            decimal.NewFromFloat(0),
			PayForBreakTime: model.PayForBreakTimeN,
		},
		{
			FacilityId:      facilityId,
			Name:            "Enrolled Nurse",
			Code:            model.ProfessionalProfessionEnrolledNurse,
			Rate:            decimal.NewFromFloat(0),
			PayForBreakTime: model.PayForBreakTimeN,
		},
		{
			FacilityId:      facilityId,
			Name:            "Registered Nurse",
			Code:            model.ProfessionalProfessionRegisteredNurse,
			Rate:            decimal.NewFromFloat(0),
			PayForBreakTime: model.PayForBreakTimeN,
		},
		{
			FacilityId:      facilityId,
			Name:            "Personal Care Worker",
			Code:            model.ProfessionalProfessionPersonalCareWorker,
			Rate:            decimal.NewFromFloat(0),
			PayForBreakTime: model.PayForBreakTimeN,
		},
	}
	if err = db.CreateInBatches(initItem, 10).Error; err != nil {
		return err
	}
	return nil
}
