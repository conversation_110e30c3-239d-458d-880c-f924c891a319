package model

import (
	"time"

	"github.com/Norray/xrocket/xmodel"
	"github.com/shopspring/decimal"
)

// JobShift 工作班次時間
type JobShift struct {
	Id               uint64          `json:"id" gorm:"primary_key"`                                  // 主鍵
	FacilityId       uint64          `json:"facilityId" gorm:"index:facility_idx;not null"`          // 機構Id
	JobId            uint64          `json:"jobId" gorm:"index:job_idx;not null"`                    // 工作Id
	BeginTime        *time.Time      `swaggertype:"string" json:"beginTime" gorm:"type:datetime(0)"` // 班次開始時間
	EndTime          *time.Time      `swaggertype:"string" json:"endTime" gorm:"type:datetime(0)"`   // 班次結束時間
	Duration         decimal.Decimal `json:"duration" gorm:"type:decimal(10,2);not null"`            // 時長（小時）
	BreakDuration    decimal.Decimal `json:"breakDuration" gorm:"type:decimal(10,2);not null"`       // 休息時長（小時）
	PayHours         decimal.Decimal `json:"payHours" gorm:"type:decimal(10,2);not null"`            // 支付時長（小時）
	ShiftPeriod      string          `json:"shiftPeriod" gorm:"type:varchar(255);not null"`          // 班次時間段
	HourlyRate       decimal.Decimal `json:"hourlyRate" gorm:"type:decimal(10,2);not null"`          // 時薪
	AllowanceAmount  decimal.Decimal `json:"allowanceAmount" gorm:"type:decimal(10,2);not null"`     // 津貼總金額
	BreakTimePayable string          `json:"breakTimePayable" gorm:"type:varchar(1);not null"`       // 休息時間是否支付薪酬 Y N

	xmodel.Model
}

func (JobShift) TableName() string {
	return "job_shift"
}

func (JobShift) SwaggerDescription() string {
	return "工作班次時間"
}
