# 【Medic Crew】後端需求整合文檔 - 按功能分類

## 概述
本文檔按功能模塊整合了【Medic Crew】UAT模板中的所有後端開發需求，提供具體的開發實作指引。

## 總時間統計：71.35小時

---

## 1. 語言功能模塊（3.25小時）

### 功能描述
為系統增加多語言支持功能，包括語言選擇、語言要求設定和語言符合度評估。

### Professional端開發需求

#### 1.1 個人資料語言選擇（2小時）
**位置：** Professional Profile > Personal Information

**具體開發任務：**
- **數據庫修改：**
  - 在 `professional` 表增加 `languages` 字段（JSON類型）
  - 支持多語言選擇存儲

- **API開發：**
  ```go
  // 在 professional_api 中新增
  type UpdateLanguagesReq struct {
      Languages []string `json:"languages" binding:"required"`
  }
  
  // 在 services/professional.go 中實現
  func (s *professionalService) UpdateLanguages(db *gorm.DB, professionalId uint64, languages []string) error
  ```

- **校驗邏輯：**
  - 語言代碼格式校驗（ISO 639-1）
  - 最多選擇5種語言限制
  - 必須包含英語的校驗

- **進度追蹤：**
  - 在 `professional_profile_progress` 表增加語言完成度字段
  - 語言選擇完成後更新進度百分比

### Facility端開發需求

#### 1.2 工作語言要求設定（1小時）
**位置：** Facility Job > Post Job

**具體開發任務：**
- **數據庫修改：**
  - 在 `job` 表增加 `language_requirements` 字段（JSON類型）
  - 增加 `language_mandatory` 字段（Boolean）

- **API開發：**
  ```go
  // 在 job.go 中修改
  type JobCreateReq struct {
      // 現有字段...
      LanguageRequirements []LanguageRequirement `json:"language_requirements"`
  }
  
  type LanguageRequirement struct {
      Language  string `json:"language" binding:"required"`
      Mandatory bool   `json:"mandatory"`
      Optional  bool   `json:"optional"`
  }
  ```

#### 1.3 語言符合度評估（0.25小時）
**位置：** Facility Job > 應聘者列表

**具體開發任務：**
- **評分算法：**
  ```go
  // 在 services/job_application.go 中實現
  func (s *jobApplicationService) CalculateLanguageScore(professionalLanguages []string, jobRequirements []LanguageRequirement) float64
  ```

- **顯示邏輯：**
  - 將「申請時間」欄位改為「語言符合度」
  - 顯示百分比分數（0-100%）

---

## 2. 津貼管理功能模塊（4.75小時）

### 功能描述
建立完整的津貼管理系統，包括津貼主檔案管理、工作津貼設定和津貼顯示功能。

### Facility端開發需求

#### 2.1 津貼主檔案管理（3小時）
**位置：** Facility Master > Allowances

**具體開發任務：**
- **數據庫設計：**
  ```sql
  CREATE TABLE allowances (
      id BIGINT PRIMARY KEY AUTO_INCREMENT,
      facility_id BIGINT NOT NULL,
      name VARCHAR(100) NOT NULL,
      description TEXT,
      amount DECIMAL(10,2),
      amount_type ENUM('fixed', 'percentage', 'hourly') DEFAULT 'fixed',
      is_active BOOLEAN DEFAULT TRUE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_facility_id (facility_id)
  );
  ```

- **Model定義：**
  ```go
  // 在 model/allowance.go 中創建
  type Allowance struct {
      Id          uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
      FacilityId  uint64    `gorm:"not null;index" json:"facility_id"`
      Name        string    `gorm:"size:100;not null" json:"name"`
      Description string    `gorm:"type:text" json:"description"`
      Amount      float64   `gorm:"type:decimal(10,2)" json:"amount"`
      AmountType  string    `gorm:"type:enum('fixed','percentage','hourly');default:'fixed'" json:"amount_type"`
      IsActive    bool      `gorm:"default:true" json:"is_active"`
      CreatedAt   time.Time `json:"created_at"`
      UpdatedAt   time.Time `json:"updated_at"`
  }
  ```

- **API開發：**
  ```go
  // 在 routers/allowance.go 中實現
  func allowanceRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
      r := Router.Group("/v1").Use(handlers...)
      {
          r.POST("/allowances/actions/create", v1.NewAllowanceController().Create)
          r.POST("/allowances/actions/list", v1.NewAllowanceController().List)
          r.POST("/allowances/actions/edit", v1.NewAllowanceController().Edit)
          r.POST("/allowances/actions/delete", v1.NewAllowanceController().Delete)
      }
  }
  ```

#### 2.2 工作津貼設定（1.5小時）
**位置：** Facility Job > Post Job

**具體開發任務：**
- **數據庫修改：**
  ```sql
  CREATE TABLE job_allowances (
      id BIGINT PRIMARY KEY AUTO_INCREMENT,
      job_id BIGINT NOT NULL,
      allowance_id BIGINT NOT NULL,
      amount DECIMAL(10,2),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      INDEX idx_job_id (job_id),
      FOREIGN KEY (job_id) REFERENCES jobs(id),
      FOREIGN KEY (allowance_id) REFERENCES allowances(id)
  );
  ```

- **API修改：**
  ```go
  // 在 job.go 中修改JobCreateReq
  type JobCreateReq struct {
      // 現有字段...
      Allowances []JobAllowanceReq `json:"allowances"`
  }
  
  type JobAllowanceReq struct {
      AllowanceId uint64  `json:"allowance_id" binding:"required"`
      Amount      float64 `json:"amount"`
  }
  ```

### Professional端開發需求

#### 2.3 津貼顯示功能（0.25小時）
**位置：** Professional > My Jobs

**具體開發任務：**
- **API修改：**
  ```go
  // 在 services/my.go 中修改工作列表和詳情查詢
  type MyJobResp struct {
      // 現有字段...
      Allowances []JobAllowanceResp `json:"allowances"`
  }
  
  type JobAllowanceResp struct {
      Name   string  `json:"name"`
      Amount float64 `json:"amount"`
      Type   string  `json:"type"`
  }
  ```

---

## 3. 資格認證功能模塊（4.25小時）

### 功能描述
完善不同職業類別的資格認證系統，包括經驗等級、藥物認證、畢業情況等。

### Professional端開發需求

#### 3.1 經驗等級管理（1.75小時）
**位置：** Professional Profile > Experience Level

**具體開發任務：**
- **數據庫修改：**
  ```sql
  -- 在 professional 表增加字段
  ALTER TABLE professional ADD COLUMN experience_level VARCHAR(50);
  ALTER TABLE professional ADD COLUMN graduation_year INT;
  ```

- **選項配置：**
  ```go
  // 在 services/selection.go 中實現
  func (s *selectionService) GetExperienceLevelOptions(professionalType string) []SelectionOption {
      switch professionalType {
      case "Medical Practitioner":
          // 刪除PGY1，修改PGY8為PGY8+
          return []SelectionOption{
              {Value: "PGY2", Label: "PGY2"},
              {Value: "PGY3", Label: "PGY3"},
              // ...
              {Value: "PGY8+", Label: "PGY8+"},
          }
      case "Enrolled Nurse", "Personal Care Worker":
          return []SelectionOption{
              {Value: "Entry Level", Label: "Entry Level"},
              {Value: "Experienced", Label: "Experienced"},
              {Value: "Senior", Label: "Senior"},
          }
      }
  }
  ```

- **動態顯示邏輯：**
  ```go
  // Medical Practitioner根據畢業年份計算可選的Experience Level
  func (s *professionalService) CalculateAvailableExperienceLevels(graduationYear int) []string
  ```

#### 3.2 藥物認證管理（1小時）
**位置：** Professional Profile > Medication Endorsement

**具體開發任務：**
- **數據庫修改：**
  ```sql
  ALTER TABLE professional ADD COLUMN medication_endorsements JSON;
  ```

- **API開發：**
  ```go
  // 僅針對Enrolled Nurse顯示
  type MedicationEndorsementReq struct {
      Endorsements []string `json:"endorsements"`
  }
  
  func (s *professionalService) UpdateMedicationEndorsements(db *gorm.DB, professionalId uint64, endorsements []string) error
  ```

#### 3.3 畢業情況管理（1小時）
**位置：** Professional Profile > Graduating Institution

**具體開發任務：**
- **數據庫修改：**
  ```sql
  ALTER TABLE professional ADD COLUMN graduation_status VARCHAR(50);
  ALTER TABLE professional ADD COLUMN graduation_institution VARCHAR(200);
  ALTER TABLE professional ADD COLUMN graduation_year INT;
  ```

- **適用範圍：** Medical Practitioner、Registered Nurse、Enrolled Nurse

### Facility端開發需求

#### 3.4 最低資格要求設定（0.5小時）
**位置：** Facility Job > Post Job

**具體開發任務：**
- **Personal Care Worker資格修改：**
  ```go
  // 將"Qualifications"改為"Min. Qualifications"
  type JobCreateReq struct {
      // 現有字段...
      MinQualifications []string `json:"min_qualifications"` // 原qualifications字段
  }
  ```

- **搜索篩選修改：**
  ```go
  // 在找工作功能中修改篩選邏輯
  func (s *jobService) FilterByMinQualifications(db *gorm.DB, minQualifications []string) *gorm.DB
  ```

---

## 4. 專業領域管理功能模塊（2.85小時）

### 功能描述
優化專業領域選擇和管理，包括Preferred Grade合併、新增專業選項等。

### Professional端開發需求

#### 4.1 Areas of Experience優化（2.1小時）
**位置：** Professional Profile > Areas of Experience

**具體開發任務：**
- **Medical Practitioner Preferred Grade合併（1.5小時）：**
  ```sql
  -- 數據遷移腳本
  ALTER TABLE professional ADD COLUMN preferred_grades JSON;
  
  -- 將現有的preferred_grade數據遷移到areas_of_experience中
  UPDATE professional 
  SET areas_of_experience = JSON_MERGE(areas_of_experience, 
      JSON_OBJECT('preferred_grades', JSON_ARRAY(preferred_grade)))
  WHERE preferred_grade IS NOT NULL;
  ```

- **動態顯示邏輯：**
  ```go
  // 根據Experience Level顯示對應的Preferred Grade選項
  func (s *professionalService) GetAvailablePreferredGrades(experienceLevel string) []SelectionOption
  ```

- **新增專業選項（0.25小時）：**
  ```go
  // Enrolled Nurse/Registered Nurse增加"Intensive Care Unit"
  // Personal Care Worker增加新選項
  func (s *selectionService) UpdateAreasOfExperienceOptions(professionalType string) []SelectionOption
  ```

- **選項調整（0.35小時）：**
  - 刪除Medical Practitioner的Intern選項
  - 調整Preferred Grade選項順序
  - 修改Medicine選項內容
  - 刪除Oncology類目

#### 4.2 文件上傳校驗（1小時）
**位置：** Professional Profile > Areas of Experience

**具體開發任務：**
- **文件校驗邏輯：**
  ```go
  // 在 services/document_file.go 中實現
  func (s *documentFileService) ValidatePreferredGradeDocument(file *multipart.FileHeader, professionalType string) error {
      // 文件格式校驗（PDF, JPG, PNG）
      // 文件大小校驗（最大5MB）
      // 文件內容校驗
  }
  ```

- **進度更新：**
  ```go
  // 文件上傳成功後更新profile進度
  func (s *professionalService) UpdatePreferredGradeProgress(db *gorm.DB, professionalId uint64) error
  ```

### 其他優化

#### 4.3 刪除過時功能（0.75小時）
- **刪除Supervision Requirement（0.5小時）：**
  ```sql
  ALTER TABLE professional DROP COLUMN supervision_requirement;
  ```

- **刪除RN Grade 1選項（0.25小時）：**
  ```go
  // 在selection服務中移除相關選項
  ```

---

## 5. 工作排班功能模塊（3.75小時）

### 功能描述
優化工作排班系統，包括時間間隔調整、排班表格重構等。

### Facility端開發需求

#### 5.1 時間間隔優化（0.5小時）
**位置：** Facility Job > Post Job & Professional > My Profile > Availability

**具體開發任務：**
- **時間選擇器修改：**
  ```go
  // 在 services/shift_time.go 中修改
  func (s *shiftTimeService) GenerateTimeSlots() []TimeSlot {
      // 從30分鐘間隔改為15分鐘間隔
      slots := []TimeSlot{}
      for hour := 0; hour < 24; hour++ {
          for minute := 0; minute < 60; minute += 15 {
              slots = append(slots, TimeSlot{
                  Value: fmt.Sprintf("%02d:%02d", hour, minute),
                  Label: fmt.Sprintf("%02d:%02d", hour, minute),
              })
          }
      }
      return slots
  }
  ```

#### 5.2 Shift Date & Time表格重構（3小時）
**位置：** Facility Job > Post Job

**具體開發任務：**
- **數據庫重構：**
  ```sql
  -- 重新設計job_schedule表結構
  CREATE TABLE job_schedules_new (
      id BIGINT PRIMARY KEY AUTO_INCREMENT,
      job_id BIGINT NOT NULL,
      date DATE NOT NULL,
      start_time TIME NOT NULL,
      end_time TIME NOT NULL,
      positions_needed INT DEFAULT 1,
      hourly_rate DECIMAL(10,2),
      break_duration INT DEFAULT 0, -- 分鐘
      is_overnight BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_job_id (job_id),
      INDEX idx_date (date)
  );
  ```

- **API重構：**
  ```go
  // 在 job.go 中重新定義排班結構
  type JobScheduleReq struct {
      Date            string  `json:"date" binding:"required"`
      StartTime       string  `json:"start_time" binding:"required"`
      EndTime         string  `json:"end_time" binding:"required"`
      PositionsNeeded int     `json:"positions_needed" binding:"min=1"`
      HourlyRate      float64 `json:"hourly_rate" binding:"min=0"`
      BreakDuration   int     `json:"break_duration"`
      IsOvernight     bool    `json:"is_overnight"`
  }
  
  type JobCreateReq struct {
      // 現有字段...
      Schedules []JobScheduleReq `json:"schedules" binding:"required,min=1"`
  }
  ```

- **校驗邏輯：**
  ```go
  // 在 services/job_schedule.go 中實現
  func (s *jobScheduleService) ValidateSchedule(schedule JobScheduleReq) error {
      // 時間邏輯校驗
      // 重疊時間檢查
      // 最小工作時長檢查
  }
  ```

#### 5.3 時間限制檢查（0.25小時）
**位置：** Facility Job > Post Job

**具體開發任務：**
- **發佈時間限制：**
  ```go
  // 在 services/job.go 中實現
  func (s *jobService) ValidatePublishTimeLimit(schedules []JobScheduleReq) error {
      // 檢查發佈時間是否符合提前通知要求
      // 例如：至少提前24小時發佈
  }
  ```

---

## 6. 評分機制功能模塊（2小時）

### 功能描述
優化不同職業類別的應聘者評分算法，提高匹配準確度。

### Facility端開發需求

#### 6.1 評分算法重構（2小時）
**位置：** Facility Job > 應聘者列表

**具體開發任務：**
- **評分權重配置：**
  ```go
  // 在 services/job_application.go 中實現
  type ScoringWeights struct {
      ExperienceWeight    float64 `json:"experience_weight"`
      QualificationWeight float64 `json:"qualification_weight"`
      LocationWeight      float64 `json:"location_weight"`
      LanguageWeight      float64 `json:"language_weight"`
      AvailabilityWeight  float64 `json:"availability_weight"`
  }
  
  func (s *jobApplicationService) GetScoringWeights(professionalType string) ScoringWeights {
      switch professionalType {
      case "Medical Practitioner":
          return ScoringWeights{
              ExperienceWeight:    0.4,
              QualificationWeight: 0.3,
              LocationWeight:      0.1,
              LanguageWeight:      0.1,
              AvailabilityWeight:  0.1,
          }
      case "Registered Nurse":
          return ScoringWeights{
              ExperienceWeight:    0.35,
              QualificationWeight: 0.25,
              LocationWeight:      0.15,
              LanguageWeight:      0.15,
              AvailabilityWeight:  0.1,
          }
      // 其他職業類別...
      }
  }
  ```

- **評分計算邏輯：**
  ```go
  func (s *jobApplicationService) CalculateApplicantScore(jobId uint64, professionalId uint64) (float64, error) {
      // 獲取工作要求和應聘者資料
      job, err := s.GetJobDetails(jobId)
      professional, err := s.GetProfessionalDetails(professionalId)
      
      weights := s.GetScoringWeights(job.ProfessionalType)
      
      // 計算各項分數
      experienceScore := s.CalculateExperienceScore(job, professional)
      qualificationScore := s.CalculateQualificationScore(job, professional)
      locationScore := s.CalculateLocationScore(job, professional)
      languageScore := s.CalculateLanguageScore(job, professional)
      availabilityScore := s.CalculateAvailabilityScore(job, professional)
      
      // 加權計算總分
      totalScore := experienceScore*weights.ExperienceWeight +
                   qualificationScore*weights.QualificationWeight +
                   locationScore*weights.LocationWeight +
                   languageScore*weights.LanguageWeight +
                   availabilityScore*weights.AvailabilityWeight
      
      return totalScore, nil
  }
  ```

---

## 7. 文件管理功能模塊（1.5小時）

### 功能描述
完善文件上傳、校驗和管理功能。

### Professional端開發需求

#### 7.1 CV上傳功能（0.5小時）
**位置：** Professional Profile > Proof of Identity & Records

**具體開發任務：**
- **文件類型擴展：**
  ```go
  // 在 services/document_file.go 中增加CV文件類型
  func (s *documentFileService) UploadCV(professionalId uint64, file *multipart.FileHeader) error {
      // 支持PDF, DOC, DOCX格式
      // 文件大小限制10MB
      // 文件名稱規範化
  }
  ```

#### 7.2 工作經驗文件上傳（0.5小時）
**位置：** Professional Profile > Experience

**具體開發任務：**
- **經驗證明文件：**
  ```go
  type ExperienceDocumentReq struct {
      ExperienceId uint64                `json:"experience_id"`
      File         *multipart.FileHeader `json:"file"`
      DocumentType string                `json:"document_type"` // certificate, reference_letter
  }
  ```

### Facility端開發需求

#### 7.3 應聘者文件隱藏（0.5小時）
**位置：** Facility Job > 查看應聘者

**具體開發任務：**
- **文件權限控制：**
  ```go
  // 在 services/job_application.go 中實現
  func (s *jobApplicationService) GetApplicantDocuments(facilityId uint64, applicantId uint64) []DocumentResp {
      // 隱藏敏感文件（如身份證、護照等）
      // 只顯示相關的專業證書和經驗證明
  }
  ```

---

## 8. 註冊流程功能模塊（3.5小時）

### 功能描述
優化Professional和Facility的註冊流程，增加驗證機制。

### Professional端開發需求

#### 8.1 註冊流程優化（2小時）
**位置：** Professional Registration

**具體開發任務：**
- **註冊步驟重構：**
  ```go
  // 在 services/register.go 中重構註冊流程
  type ProfessionalRegisterReq struct {
      // 基本信息
      Email           string `json:"email" binding:"required,email"`
      Phone           string `json:"phone" binding:"required"`
      FirstName       string `json:"first_name" binding:"required"`
      LastName        string `json:"last_name" binding:"required"`
      ProfessionalType string `json:"professional_type" binding:"required"`
      
      // 驗證碼
      EmailVerifyCode string `json:"email_verify_code" binding:"required"`
      PhoneVerifyCode string `json:"phone_verify_code" binding:"required"`
  }
  ```

- **郵件驗證（0.5小時）：**
  ```go
  func (s *registerService) SendEmailVerification(email string) error {
      // 生成6位數驗證碼
      // 發送驗證郵件
      // 存儲驗證碼（5分鐘有效期）
  }
  
  func (s *registerService) VerifyEmailCode(email string, code string) bool {
      // 驗證郵件驗證碼
  }
  ```

- **手機驗證（0.5小時）：**
  ```go
  func (s *registerService) SendSMSVerification(phone string) error {
      // 發送SMS驗證碼
      // 集成第三方SMS服務
  }
  
  func (s *registerService) VerifyPhoneCode(phone string, code string) bool {
      // 驗證手機驗證碼
  }
  ```

### Facility端開發需求

#### 8.2 機構註冊流程（1.5小時）
**位置：** Facility Registration

**具體開發任務：**
- **註冊流程修改：**
  ```go
  type FacilityRegisterReq struct {
      // 機構信息
      FacilityName    string `json:"facility_name" binding:"required"`
      ABN             string `json:"abn" binding:"required"`
      ContactEmail    string `json:"contact_email" binding:"required,email"`
      ContactPhone    string `json:"contact_phone" binding:"required"`
      
      // 聯繫人信息
      ContactPersonName  string `json:"contact_person_name" binding:"required"`
      ContactPersonTitle string `json:"contact_person_title"`
      
      // 驗證
      EmailVerifyCode string `json:"email_verify_code" binding:"required"`
  }
  ```

- **ABN驗證：**
  ```go
  func (s *registerService) ValidateABN(abn string) (bool, error) {
      // 調用澳洲ABN查詢API
      // 驗證ABN格式和有效性
  }
  ```

---

## 9. 銀行資料與GST功能模塊（1.5小時）

### 功能描述
完善Professional的銀行資料管理和GST相關功能。

### Professional端開發需求

#### 9.1 GST資料管理（1.5小時）
**位置：** Professional Profile > Bank Details

**具體開發任務：**
- **數據庫修改：**
  ```sql
  ALTER TABLE professional ADD COLUMN gst_registered BOOLEAN DEFAULT FALSE;
  ALTER TABLE professional ADD COLUMN abn VARCHAR(20);
  ALTER TABLE professional ADD COLUMN gst_rate DECIMAL(5,2) DEFAULT 10.00;
  ```

- **GST輸入功能（1小時）：**
  ```go
  type BankDetailsReq struct {
      // 現有銀行字段...
      GstRegistered bool    `json:"gst_registered"`
      ABN           string  `json:"abn"`
      GstRate       float64 `json:"gst_rate"`
  }
  ```

- **GST校驗（0.5小時）：**
  ```go
  func (s *professionalService) ValidateGSTDetails(req BankDetailsReq) error {
      if req.GstRegistered {
          // ABN格式校驗
          if !s.ValidateABNFormat(req.ABN) {
              return errors.New("Invalid ABN format")
          }
          
          // GST稅率校驗（通常為10%）
          if req.GstRate < 0 || req.GstRate > 15 {
              return errors.New("Invalid GST rate")
          }
      }
      return nil
  }
  ```

### Facility端開發需求

#### 9.2 GST顯示邏輯（0.75小時）
**位置：** Facility Billing > Confirmation Note

**具體開發任務：**
- **GST計算顯示：**
  ```go
  // 在 services/invoice.go 中實現
  func (s *invoiceService) CalculateGST(professionalId uint64, amount float64) (float64, error) {
      professional, err := s.GetProfessionalGSTDetails(professionalId)
      if err != nil {
          return 0, err
      }
      
      if professional.GstRegistered {
          return amount * (professional.GstRate / 100), nil
      }
      return 0, nil
  }
  ```

---

## 10. 其他功能模塊（剩餘時間）

### 10.1 Reference Check優化（3小時）
### 10.2 Job Application流程（2.5小時）
### 10.3 搜索篩選功能（5小時）
### 10.4 性能優化（3.5小時）
### 10.5 安全增強（3小時）

---

## 開發順序建議

### 第一階段（高優先級 - 25小時）
1. 津貼管理功能模塊（4.75小時）
2. 資格認證功能模塊（4.25小時）
3. 工作排班功能模塊（3.75小時）
4. 註冊流程功能模塊（3.5小時）
5. 語言功能模塊（3.25小時）
6. 專業領域管理功能模塊（2.85小時）
7. 評分機制功能模塊（2小時）
8. 銀行資料與GST功能模塊（1.5小時）

### 第二階段（中優先級 - 30小時）
9. 搜索篩選功能（5小時）
10. Reference Check優化（3小時）
11. Job Application流程（2.5小時）
12. 文件管理功能模塊（1.5小時）
13. 其他中優先級功能（18小時）

### 第三階段（低優先級 - 16.35小時）
14. 性能優化（3.5小時）
15. 安全增強（3小時）
16. 系統集成（3小時）
17. 其他低優先級功能（6.85小時）

---

*文檔生成時間：2025年1月21日*
*總預計開發時間：71.35小時*
*按功能分類整合，提供具體開發指引*