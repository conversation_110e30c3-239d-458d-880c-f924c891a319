package services

import (
	"fmt"
	"image"
	"io"
	"math"
	"path/filepath"
	"time"

	"github.com/Norray/xrocket/xtool"
	"golang.org/x/image/webp"

	"github.com/Norray/xrocket/xconfig"
	"github.com/disintegration/imaging"
	"github.com/matcornic/hermes/v2"
)

const (
	MedicCrewName        = "Medic Crew"
	MedicCrewLink        = "#" // TODO: update link
	MedicCrewCopyright   = "Copyright © Medic Crew. All rights reserved."
	MedicCrewButtonColor = "#1FAF96"

	OSSFacilityFilePath     = "facility/%d/%s/%s"     // facility_id, file_code, uuid_file_name
	OSSProfessionalFilePath = "professional/%d/%s/%s" // professional_id, file_code, uuid_file_name
	OSSDocumentFilePath     = "document/%d/%s/%s"     // professional_id, file_code, uuid_file_name

	OSSFaqFilePath = "faq/%s/%s" // file_code, uuid_file_name

	MinPageSize = 1
	MaxPageSize = 100
)

var medicCrewLogoUrl string

func DefaultSetup() {
	medicCrewLogoUrl = fmt.Sprintf("https://%s.%s/assets/medic-crew-mail-logo.png", xconfig.OSSConf.Bucket, xconfig.OSSConf.Endpoint)
}
func HermesDefaultProduct(logoURL string) hermes.Product {
	return hermes.Product{
		Name:      MedicCrewName,
		Link:      MedicCrewLink,
		Logo:      logoURL,
		Copyright: MedicCrewCopyright,
	}
}

func ForgetPasswordProduct(logoURL string, troubleText string) hermes.Product {
	return hermes.Product{
		Name:        MedicCrewName,
		Link:        MedicCrewLink,
		Logo:        logoURL,
		Copyright:   MedicCrewCopyright,
		TroubleText: troubleText,
	}
}

func MakeThumbnail(file io.Reader, savePath string, mimeType string) error {
	if err := xtool.IsNotExistMkDir(filepath.Dir(savePath)); err != nil {
		return err
	}
	var err error
	var img image.Image
	switch mimeType {
	case "image/webp":
		img, err = webp.Decode(file)
	default:
		img, err = imaging.Decode(file)
	}
	if err != nil {
		return err
	}
	b := img.Bounds()
	width := b.Max.X
	height := b.Max.Y
	w, h := thumbnailCalculateRatioFit(width, height)
	// 進行圖片縮放
	m := imaging.Resize(img, w, h, imaging.Lanczos)
	// 需要保存的文件
	err = imaging.Save(m, savePath)
	if err != nil {
		return err
	}
	return nil
}

// 计算缩放后尺寸
const defaultThumbnailMaxWidth float64 = 400
const defaultThumbnailMaxHeight float64 = 400

func thumbnailCalculateRatioFit(srcWidth, srcHeight int) (int, int) {
	ratio := math.Min(defaultThumbnailMaxWidth/float64(srcWidth), defaultThumbnailMaxHeight/float64(srcHeight))
	return int(math.Ceil(float64(srcWidth) * ratio)), int(math.Ceil(float64(srcHeight) * ratio))
}

// 字符串數組去重
func StringArrayDistinct(arr []string) []string {
	if len(arr) == 0 {
		return arr
	}

	// 使用 map 來實現去重
	uniqueMap := make(map[string]bool)
	result := make([]string, 0, len(arr))

	for _, item := range arr {
		if !uniqueMap[item] {
			uniqueMap[item] = true
			result = append(result, item)
		}
	}

	return result
}

func ISOWeekday(wd time.Weekday) int {
	if wd == time.Sunday {
		return 7
	}
	return int(wd)
}
