package services

import (
	"strings"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gorm.io/gorm"
)

var LocationService = new(locationService)

type locationService struct{}

func (s *locationService) CheckIdExist(db *gorm.DB, m *model.Location, id uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.location.id.does_not_exist",
		Other: "No such record, please try after reloading.",
	}
	var err error
	if err = db.First(&m, id).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

func (s *locationService) CheckIdExistInCountry(db *gorm.DB, country string, m *model.Location, id uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.location.id.does_not_exist",
		Other: "No such record, please try after reloading.",
	}
	var err error
	if err = db.Where("country = ?", country).First(&m, id).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

func (s *locationService) CheckIdCanBeParent(db *gorm.DB, country string, id uint64, parentId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.location.id.cannot_be_parent",
		Other: "The parent location is incorrect.",
	}
	if id == parentId {
		return false, msg, nil
	}
	// 遞歸查這個Parent是否合法
	parentId, err := s.getParentId(db, country, parentId)
	if err != nil {
		return false, i18n.Message{}, err
	}
	if parentId == 0 {
		return true, i18n.Message{}, nil
	} else if parentId == id {
		return false, msg, nil
	}
	return s.CheckIdCanBeParent(db, country, id, parentId)
}

func (s *locationService) CheckCanDelete(db *gorm.DB, id uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.location.id.cannot_delete",
		Other: "The location cannot be deleted.",
	}
	var err error
	var m model.Location
	if err = db.Where("parent_id = ?", id).First(&m).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return true, msg, nil
	}
	return false, msg, nil
}

func (s *locationService) getParentId(db *gorm.DB, country string, id uint64) (uint64, error) {
	if id == 0 {
		return 0, nil
	}
	var m model.Location
	if err := db.Where("country = ?", country).Where("id = ?", id).First(&m).Error; err != nil {
		return 0, err
	}
	return m.ParentId, nil
}

type LocationCreateReq struct {
	Country  string `json:"country" binding:"required,oneof=Australia"`
	Level    string `json:"level" binding:"required,oneof=STATE CITY"`
	Location string `json:"location" binding:"required"`
	ParentId uint64 `json:"parentId" binding:"required_if=Level CITY"`
}

type LocationCreateResp struct {
	LocationId uint64 `json:"locationId"`
}

func (s *locationService) Create(db *gorm.DB, req LocationCreateReq) (LocationCreateResp, error) {
	var resp LocationCreateResp
	var err error
	var m model.Location
	_ = copier.Copy(&m, req)
	if req.Level == "STATE" {
		var country model.Location
		if err = db.Where("country = ?", req.Country).Where("level = 'COUNTRY'").First(&country).Error; err != nil {
			return resp, err
		}
		m.ParentId = country.Id
	}
	if err = db.Create(&m).Error; err != nil {
		return resp, err
	}
	resp.LocationId = m.Id
	return resp, nil
}

type LocationListReq struct {
	Country        string `form:"country"`
	Level          string `form:"level"` // 可逗號分隔
	Location       string `form:"location"`
	ParentLocation string `form:"parentLocation"`
	StateLocation  string `form:"stateLocation"`
	CityLocation   string `form:"cityLocation"`
}

type LocationListResp struct {
	LocationId     uint64 `json:"locationId"`
	Country        string `json:"country"`
	Level          string `json:"level"`
	Location       string `json:"location"`
	ParentLocation string `json:"parentLocation"`
	ParentId       uint64 `json:"parentId"`
}

func (s *locationService) List(db *gorm.DB, req LocationListReq, pageSet *xresp.PageSet) ([]LocationListResp, error) {
	var err error
	var resp []LocationListResp
	builder := db.Table("location AS l").Select([]string{
		"l.id AS location_id",
		"l.country",
		"l.level",
		"l.location",
		"pl.location AS parent_location",
		"pl.id AS parent_id",
	}).Joins("LEFT JOIN location pl on l.parent_id = pl.id")

	if req.Country != "" {
		builder = builder.Where("l.country = ?", req.Country)
	}
	if req.Level != "" {
		builder = builder.Where("l.level IN (?)", strings.Split(req.Level, ","))
	}
	if req.Location != "" {
		builder = builder.Where("l.location LIKE ?", "%"+req.Location+"%")
	}
	if req.ParentLocation != "" {
		builder = builder.Where("l.parent_location = ?", req.ParentLocation)
	}
	if req.StateLocation != "" {
		builder = builder.Where("(l.level = ? AND l.location LIKE ? ) OR (l.level = ? AND pl.location LIKE ?)", "STATE", "%"+req.StateLocation+"%", "CITY", "%"+req.StateLocation+"%")
	}
	if req.CityLocation != "" {
		builder = builder.Where("(l.level = ? AND l.location LIKE ?)", "CITY", "%"+req.CityLocation+"%")
	}
	// 按 Level的三個值排，COUNTRY 先排，然後 STATE，然後CITY
	if err = builder.Scopes(xresp.Paginate(pageSet)).
		Order("l.level = 'COUNTRY' DESC").
		Order("l.level = 'STATE' DESC").
		Order("l.level = 'CITY' DESC").
		Order("pl.location").
		Order("l.location").
		Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

type LocationEditReq struct {
	LocationId uint64 `json:"locationId" binding:"required"`
	Country    string `json:"country" binding:"required,oneof=Australia"`
	Level      string `json:"level" binding:"required,oneof=STATE CITY"`
	Location   string `json:"location" binding:"required"`
	ParentId   uint64 `json:"parentId" binding:"required_if=Level CITY"`
}

func (s *locationService) Edit(db *gorm.DB, req LocationEditReq) error {
	var err error
	var m model.Location
	if err = db.First(&m, req.LocationId).Error; err != nil {
		return err
	}
	_ = copier.Copy(&m, req)
	if req.Level == "STATE" {
		var country model.Location
		if err = db.Where("country = ?", req.Country).Where("level = 'COUNTRY'").First(&country).Error; err != nil {
			return err
		}
		m.ParentId = country.Id
	}
	if err = db.Save(&m).Error; err != nil {
		return err
	}
	return nil
}

type LocationInquireReq struct {
	LocationId uint64 `form:"locationId" binding:"required"`
}

type LocationInquireResp struct {
	LocationId     uint64 `json:"locationId"`
	Country        string `json:"country"`
	Level          string `json:"level"`
	Location       string `json:"location"`
	ParentLocation string `json:"parentLocation"`
	ParentId       uint64 `json:"parentId"`
}

func (s *locationService) Inquire(db *gorm.DB, req LocationInquireReq) (LocationInquireResp, error) {
	var err error
	var resp LocationInquireResp
	var m model.Location
	if err = db.First(&m, req.LocationId).Error; err != nil {
		return resp, err
	}
	_ = copier.Copy(&resp, m)
	resp.LocationId = m.Id

	if resp.ParentId > 0 {
		var pm model.Location
		if err = db.Where("id = ?", m.ParentId).First(&pm).Error; err != nil {
			return resp, err
		}
		resp.ParentLocation = pm.Location
	}

	return resp, nil
}

type LocationDeleteReq struct {
	LocationId uint64 `json:"locationId" binding:"required"`
}

func (s *locationService) Delete(db *gorm.DB, req LocationDeleteReq) error {
	var err error
	if err = db.Delete(&model.Location{}, req.LocationId).Error; err != nil {
		return err
	}
	return nil
}

type LocationSearchReq struct {
	Country        string `form:"country" binding:"required"`                // 國家 (精準搜索)
	Level          string `form:"level" binding:"required,oneof=STATE CITY"` // 想要的區域類型 STATE CITY
	Location       string `form:"location"`                                  // 區域名字 (模糊搜索)
	ParentLocation string `form:"parentLocation"`                            // 父級區域名字 (精準篩選)
	SelectedValue  string `form:"selectedValue"`                             // 選中值
	Limit          int    `form:"limit"`                                     // 限制數量
}

type LocationSearchResp struct {
	Location string `json:"location"`
}

func (s *locationService) Search(db *gorm.DB, req LocationSearchReq) ([]LocationSearchResp, error) {
	var err error
	var resp []LocationSearchResp
	var selectLocation LocationSearchResp
	builder := db.Table("location AS l").Select([]string{
		"l.location",
	}).Where("l.country = ?", req.Country).Where("l.level = ?", req.Level)

	if req.Location != "" {
		builder = builder.Where("l.location LIKE ?", "%"+req.Location+"%")
	}
	if req.ParentLocation != "" {
		builder = builder.Joins("JOIN location AS pl on pl.id = l.parent_id").Where("pl.location = ?", req.ParentLocation)
	}
	if req.SelectedValue != "" {
		builder = builder.Where("l.location != ?", req.SelectedValue)
		selectBuilder := db.Table("location AS l").
			Select("l.location").
			Where("l.country = ?", req.Country).
			Where("l.level = ?", req.Level).
			Where("l.location = ?", req.SelectedValue)
		if req.Level == "CITY" {
			selectBuilder = selectBuilder.
				Joins("JOIN location AS pl on pl.id = l.parent_id").Where("pl.location = ?", req.ParentLocation)
		}
		if err = selectBuilder.
			First(&selectLocation).Error; xgorm.IsSqlErr(err) {
			return resp, err
		}
	}
	if req.Limit > 0 {
		builder = builder.Limit(req.Limit)
	}
	if err = builder.Order("l.id").Find(&resp).Error; err != nil {
		return resp, err
	}
	if selectLocation.Location != "" {
		resp = append([]LocationSearchResp{selectLocation}, resp...)
	}
	return resp, nil
}

type LocationSearchCountryReq struct {
	Country       string `form:"country"`       // 國家名字 (模糊搜索)
	SelectedValue string `form:"selectedValue"` // 選中值
	Limit         int    `form:"limit"`         // 限制數量
}

type LocationSearchCountryResp struct {
	Location string `json:"location"`
}

// SearchCountry 專門搜索國家的函數
func (s *locationService) SearchCountry(db *gorm.DB, req LocationSearchCountryReq) ([]LocationSearchCountryResp, error) {
	var err error
	var resp []LocationSearchCountryResp
	var selectCountry LocationSearchCountryResp

	// 構建查詢
	builder := db.Table("location AS l").
		Select("DISTINCT l.location").
		Where("l.level = ?", "COUNTRY")

	// 模糊搜索國家名稱
	if req.Country != "" {
		builder = builder.Where("l.country LIKE ?", "%"+req.Country+"%")
	}

	// 處理選中值（將選中的國家排在前面）
	if req.SelectedValue != "" {
		builder = builder.Where("l.country != ?", req.SelectedValue)

		// 單獨查詢選中的國家
		selectBuilder := db.Table("location AS l").
			Select("l.Location").
			Where("l.level = ?", "COUNTRY").
			Where("l.country = ?", req.SelectedValue)

		if err = selectBuilder.First(&selectCountry).Error; xgorm.IsSqlErr(err) {
			return resp, err
		}
	}

	// 設置限制數量
	if req.Limit > 0 {
		builder = builder.Limit(req.Limit)
	}

	// 執行查詢，按國家名稱排序
	if err = builder.Order("l.location").Find(&resp).Error; err != nil {
		return resp, err
	}

	// 如果有選中的國家，將其放在結果的最前面
	if selectCountry.Location != "" {
		resp = append([]LocationSearchCountryResp{selectCountry}, resp...)
	}

	return resp, nil
}
