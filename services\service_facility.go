package services

import (
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xgorm"
	"gorm.io/gorm"
)

var ServiceFacilityService = new(serviceFacilityService)

type serviceFacilityService struct{}

type ServiceFacilitySearchReq struct {
	Name          string `form:"name" binding:"omitempty"`          // 名稱
	SelectedValue string `form:"selectedValue" binding:"omitempty"` // 選中值
	Limit         int    `form:"limit" binding:"omitempty"`         // 限制
}

type ServiceFacilitySearchResp struct {
	ServiceName string `json:"serviceName,omitempty"` // 服務名稱
}

func (s *serviceFacilityService) Search(db *gorm.DB, req ServiceFacilitySearchReq) ([]ServiceFacilitySearchResp, error) {
	var err error
	var resp []ServiceFacilitySearchResp
	var firstServiceFacility ServiceFacilitySearchResp
	builder := db.Table("service_facility AS sf").Select("DISTINCT sf.service_name AS service_name")

	if req.Name != "" {
		builder = builder.Where("sf.service_name LIKE ?", "%"+req.Name+"%")
	}
	if req.SelectedValue != "" {
		builder = builder.Where("sf.service_name != ?", req.SelectedValue)
		if err = db.Model(&model.ServiceFacility{}).
			Select("id,service_name").
			Where("service_name = ?", req.SelectedValue).
			Take(&firstServiceFacility).Error; xgorm.IsSqlErr(err) {
			return resp, err
		}
	}
	if req.Limit == 0 {
		req.Limit = 30
	}
	builder = builder.Limit(req.Limit)
	if err = builder.
		Order("sf.service_name").
		Find(&resp).Error; err != nil {
		return resp, err
	}

	if firstServiceFacility.ServiceName != "" {
		resp = append([]ServiceFacilitySearchResp{firstServiceFacility}, resp...)
	}
	return resp, nil
}
