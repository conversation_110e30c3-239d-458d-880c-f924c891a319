package model

import (
	"github.com/Norray/xrocket/xmodel"
	"github.com/shopspring/decimal"
)

const (
	JobAllowanceAllowanceTypeHourly = "HOURLY" // 小時津貼
	JobAllowanceAllowanceTypeShift  = "SHIFT"  // 班次津貼
	JobAllowanceAllowanceTypeJob    = "JOB"    // 職位津貼
)

// 職位津貼
type JobAllowance struct {
	Id                     uint64          `json:"id" gorm:"primary_key"`                                  // 主鍵
	FacilityId             uint64          `json:"facilityId" gorm:"index:facility_idx;not null"`          // 機構Id
	JobId                  uint64          `json:"jobId" gorm:"index:job_idx;not null"`                    // 工作Id
	JobShiftId             uint64          `json:"jobShiftId" gorm:"index:job_shift_idx;not null"`         // 工作班次Id
	AllowanceId            uint64          `json:"allowanceId" gorm:"index:allowance_idx;not null"`        // 津貼Id
	AllowanceName          string          `json:"allowanceName" gorm:"type:varchar(100);not null"`        // 津貼名稱
	AllowanceType          string          `json:"allowanceType" gorm:"type:varchar(20);not null"`         // 津貼類型 HOURLY, SHIFT, JOB
	AttractsSuperannuation string          `json:"attractsSuperannuation" gorm:"type:varchar(1);not null"` // 納入退休金 Y N
	BaseAmount             decimal.Decimal `json:"baseAmount" gorm:"type:decimal(10,2);not null"`          // 原始津貼金額（未計算）
	Amount                 decimal.Decimal `json:"amount" gorm:"type:decimal(10,2);not null"`              // 計算後的津貼金額
	xmodel.Model
}

func (JobAllowance) TableName() string {
	return "job_allowance"
}

func (JobAllowance) SwaggerDescription() string {
	return "職位津貼"
}
