package system_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/nicksnyder/go-i18n/v2/i18n"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
)

type ProfessionalProfileController struct {
	v1.CommonController
}

func NewProfessionalProfileController() ProfessionalProfileController {
	return ProfessionalProfileController{}
}

// @Tags Professional Profile
// @Summary 獲取專業人士資料列表
// @Description
// @Router /v1/system/professional-profiles [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.ProfessionalProfileListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Param sortingKey query string false "name:機構名稱,approvedTime:審核時間,applicationTime:申請時間"
// @Param sortingType query string false "a=順序,d=倒序"
// @Success 200 {object} []services.ProfessionalProfileListResp "Success"
func (con ProfessionalProfileController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalProfileListReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	var sortSet xresp.SortingSet
	_ = c.ShouldBindQuery(&sortSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.ProfessionalProfileService.List(db, req, &pageSet, sortSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Profile
// @Summary 查詢專業人士資料
// @Description
// @Router /v1/system/professional-profiles/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalProfileInquireReq true "parameter"
// @Success 200 {object} services.ProfessionalProfileInquireResp
func (con ProfessionalProfileController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalProfileInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		var professional model.Professional
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckIdExist(db, &professional, req.ProfessionalId)
			})
		var alertMsg []string
		alertMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}
		resp, err := services.ProfessionalProfileService.Inquire(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Profile
// @Summary 查詢ABN是否有效
// @Description
// @Router /v1/system/professional-profiles/actions/check-abn [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param abnNumber query string true "ABN號碼"
// @Success 200 {object} services.ProfessionalProfileCheckAbnResp
func (con ProfessionalProfileController) CheckAbn(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalProfileCheckAbnReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.ProfessionalProfileService.ProfessionalProfileCheckAbn(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Profile
// @Summary 審核專業人士資料
// @Description
// @Router /v1/system/professional-profiles/actions/approve [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalProfileApproveReq true "parameter"
// @Success 200 "Success"
func (con ProfessionalProfileController) Approve(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalProfileApproveReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var professional model.Professional
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckIdExist(db, &professional, req.ProfessionalId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckCanApprove(professional)
			})
		var alertMsg []string
		alertMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}

		tx := db.Begin()
		err = services.ProfessionalProfileService.Approve(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()

		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Profile
// @Summary 查詢付款資料
// @Description
// @Router /v1/system/professional-profiles/actions/payment-detail [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalProfilePaymentDetailReqBySystem true "parameter"
// @Success 200 {object} services.ProfessionalProfilePaymentDetailResp
func (con ProfessionalProfileController) PaymentDetail(c *gin.Context) {
	var req services.ProfessionalProfilePaymentDetailReqBySystem
	nc := xapp.NGinCtx{C: c}
	db := xgorm.DB.WithContext(c)
	if err := c.ShouldBind(&req); err == nil {

		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.UserService.CheckUserExists(db, model.UserUserTypeProfessional, req.UserId)
			})
		var msg []string
		msg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(msg) > 0 {
			nc.BadRequestResponseWithCheckMsg(msg)
			return
		}
		r := services.ProfessionalProfilePaymentDetailReq{
			UserId: req.UserId,
		}
		resp, err := services.ProfessionalProfileService.ProfessionalProfilePaymentDetail(db, r)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Profile
// @Summary 停用/啟用專業人士資料
// @Description 停用/啟用專業人士資料
// @Router /v1/system/professional-profiles/actions/deactivate [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalProfileDeactivateReq true "parameter"
// @Success 200 "Success"
func (con ProfessionalProfileController) Deactivate(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalProfileDeactivateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var professional model.Professional
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckIdExist(db, &professional, req.ProfessionalId)
			})
		var alertMsg []string
		alertMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}

		tx := db.Begin()
		err = services.ProfessionalProfileService.Deactivate(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()

		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Profile
// @Summary 專業人士資料對比
// @Description
// @Router /v1/system/professional-profiles/actions/compare [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalProfileCompareReq true "parameter"
// @Success 200 {object} services.ProfessionalProfileCompareResp
func (con ProfessionalProfileController) Compare(c *gin.Context) {
	var req services.ProfessionalProfileCompareReq
	nc := xapp.NGinCtx{C: c}
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		checker := xapp.NewCK(c, true)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckIdExist(db, &model.Professional{}, req.CurrentProfessionalId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckIdExist(db, &model.Professional{}, req.NewProfessionalId)
			})
		msg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(msg) > 0 {
			nc.BadRequestResponseWithCheckMsg(msg)
			return
		}
		resp, err := services.ProfessionalProfileService.Compare(db, req, nc.GetLanguage())
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
