# 【Medic Crew】後端需求整合文檔

## 需求概覽

本文檔整合了UAT模板中的所有後端開發需求，按照功能模組分類，並標註了預計開發時間。

### 總預計時間統計
- **高優先級任務**: 約 12.5 小時
- **中優先級任務**: 約 8.5 小時
- **低優先級任務**: 約 3.5 小時
- **總計**: 約 24.5 小時

---

## 1. Professional Profile 模組

### 1.1 Personal Information (個人信息)

#### 1.1.1 Language 多選輸入 [預計: 2小時]
- **需求**: 增加Language的輸入（多選）
- **後端備註**: 資料加字段、加校驗、加進度
- **優先級**: 中

### 1.2 Work Preferences & Experience (工作偏好與經驗)

#### 1.2.1 Graduating Institution [預計: 1小時]
- **需求**: Medical Practitioner/Registered Nurse/Enrolled Nurse增加畢業情況的輸入
- **後端備註**: 資料加字段、加校驗、加進度
- **優先級**: 中

#### 1.2.2 Experience Level 相關修改 [預計: 1.75小時]
- **需求1**: Enrolled Nurse/Personal Care Worker增加Experience Level選擇 [1小時]
  - **後端備註**: 增加資料、增加校驗
- **需求2**: Medical Practitioner Experience Level根據畢業年份顯示 [0.5小時]
  - **後端備註**: 增加校驗
- **需求3**: 刪除和修改部分選項 [0.25小時]
  - **後端備註**: 刪除、修改

#### 1.2.3 Medication Endorsement [預計: 1小時]
- **需求**: Enrolled Nurse增加Medication Endorsement選擇
- **後端備註**: 資料加字段、加校驗、加進度
- **優先級**: 中

#### 1.2.4 Areas of Experience 重構 [預計: 3.25小時] ⭐ 高優先級
- **需求1**: Medical Practitioner的Preferred Grade合併到Areas of Experience [1.5小時]
  - **後端備註**: 資料加字段、加校驗、加進度
- **需求2**: Preferred Grade文件上傳校驗 [1小時]
  - **後端備註**: 加校驗、加進度
- **需求3**: Preferred Grade根據Experience Level顯示 [0.75小時]
  - **後端備註**: 加校驗

#### 1.2.5 Supervision Requirement [預計: 0.5小時]
- **需求**: 刪除這個輸入
- **後端備註**: 刪除字段
- **優先級**: 低

#### 1.2.6 Experience 相關 [預計: 1.5小時]
- **需求1**: 醫院選項增加數據 [1小時]
  - **後端備註**: 補數據
- **需求2**: 工作經驗增加上傳文件功能並必填 [0.5小時]
  - **後端備註**: 加檢查，加進度

#### 1.2.7 Professional Referees [預計: 0.5小時]
- **需求**: 增加"Dates Worked Together"輸入
- **後端備註**: 加字段、加進度
- **優先級**: 中

---

## 2. Facility Job 模組

### 2.1 Post Job (發佈工作)

#### 2.1.1 基本功能修改 [預計: 1.25小時]
- **需求1**: shift time時間間隔改為15分鐘 [0.25小時]
  - **後端備註**: 時間刻度0.25，檢查API有無影響
- **需求2**: 刪除Gender選擇 [0.5小時]
  - **後端備註**: 刪字段
- **需求3**: 發佈job時檢查shift time時間限制 [0.5小時]
  - **後端備註**: 加檢查

#### 2.1.2 Medical Practitioner 相關 [預計: 0.35小時]
- **需求1**: Minimum Classification刪除PGY1 [0小時]
- **需求2**: Preferred Grade改成"Minimum Grade"並調整選項 [0.25小時]
- **需求3**: Supervision Level改成非必填 [0.1小時]

#### 2.1.3 Enrolled Nurse 相關 [預計: 0.5小時]
- **需求**: 增加是否藥劑師、Minimum Classification輸入
- **後端備註**: 加字段
- **優先級**: 中

#### 2.1.4 Shift Date & Time 重構 [預計: 3小時] ⭐ 高優先級
- **需求**: 表格內容修改
- **後端備註**: 改結構
- **優先級**: 高

#### 2.1.5 Allowances 功能 [預計: 1.5小時] ⭐ 高優先級
- **需求**: 增加Allowances的輸入
- **後端備註**: 加字段
- **備註**: Allowances也是用pay hours計算
- **優先級**: 高

#### 2.1.6 Language 要求 [預計: 1小時]
- **需求**: language輸入增加"Mandatory"或"Optional"選擇
- **後端備註**: 加字段、找工作加篩選
- **優先級**: 中

#### 2.1.7 Personal Care Worker Qualifications [預計: 2小時] ⭐ 高優先級
- **需求**: Qualifications改成Min. Qualifications，選項包括Cert III、Cert IV
- **後端備註**: 改值、改校驗、找工作改篩選
- **詳細規則**:
  - 選擇Cert III：所有Personal Care Worker都符合條件
  - 選擇Cert IV：只有特定資格的PCW符合條件
- **優先級**: 高

#### 2.1.8 Orientation Documents [預計: 1小時]
- **需求**: 檢查facility基本資料是否有對應position的Orientation Documents
- **後端備註**: 加上傳、同步到基本資料
- **優先級**: 中

### 2.2 查看應聘者 [預計: 1.5小時]

#### 2.2.1 工作詳情顯示 [預計: 0.25小時]
- **需求**: 增加Allowance顯示
- **優先級**: 低

#### 2.2.2 應聘者詳情優化 [預計: 1小時]
- **需求1**: 隱藏部分Proof of Identity文件 [0.5小時]
  - **後端備註**: 改返回結果、隱藏部分文件
- **需求2**: 增加顯示Language、Graduating Institution等信息 [0.25小時]
- **需求3**: 增加reference完成狀態顯示 [0.25小時]

#### 2.2.3 評分機制修改 [預計: 2小時]
- **需求1**: Enrolled Nurse評分機制修改 [0.5小時]
- **需求2**: Registered Nurse評分機制修改 [0.5小時]
- **需求3**: Medical Practitioner評分機制修改 [0.5小時]
  - **特別注意**: 需要區分specialities的Prefer grade評分
- **需求4**: Personal Care Worker評分機制修改 [0.5小時]

#### 2.2.4 語言匹配顯示 [預計: 0.25小時]
- **需求**: 申請時間欄位改成Professional對應符合職位的語言
- **規則**:
  - Mandatory語言要求：刪除申請時間欄位
  - Optional語言要求：顯示匹配的語言
- **優先級**: 低

---

## 3. Facility Master 模組

### 3.1 Benefits 初始化 [預計: 1小時]
- **需求**: 機構的津貼主檔案做初始化
- **後端備註**: 機構初始數據
- **默認數據**:
  - Travel Reimbursement
  - Accommodation Provided
  - Meal Allowance
  - Onsite Café
  - Free Onsite Parking
- **優先級**: 中

### 3.2 Allowances 主檔案 [預計: 3小時] ⭐ 高優先級
- **需求**: 增加Allowances的主檔案
- **後端備註**: 主檔案
- **優先級**: 高

---

## 4. Facility Billing 模組

### 4.1 Confirmation Note [預計: 0.75小時]
- **需求**: GST根據Professional有無GST來顯示
- **後端備註**: 生成confirmation note時，需要檢查用戶是否有權限勾選GST，沒有權限時需要返回錯誤
- **優先級**: 中

---

## 5. 其他功能模組

### 5.1 數據維護 [預計: 1小時]
- **需求**: 各種選項的增加、刪除、修改
- **包括**: Experience Level選項、Areas of Experience選項、Qualifications選項等
- **優先級**: 低

### 5.2 校驗規則更新 [預計: 1.5小時]
- **需求**: 根據新的業務規則更新各種校驗邏輯
- **包括**: Experience Level根據畢業年份校驗、Preferred Grade根據Experience Level校驗等
- **優先級**: 中

---

## 開發建議

### 優先級排序
1. **高優先級** (12.5小時):
   - Areas of Experience重構 (3.25小時)
   - Shift Date & Time重構 (3小時)
   - Allowances主檔案 (3小時)
   - Personal Care Worker Qualifications (2小時)
   - Allowances輸入功能 (1.5小時)

2. **中優先級** (8.5小時):
   - 各種字段添加和校驗更新
   - Benefits初始化
   - Billing模組GST處理

3. **低優先級** (3.5小時):
   - UI顯示優化
   - 數據選項維護

### 技術注意事項
1. **數據庫變更**: 多個模組需要添加新字段，建議統一規劃migration
2. **校驗邏輯**: 新增的校驗規則較多，需要確保前後端一致性
3. **進度計算**: 多個功能涉及進度計算的修改，需要統一處理
4. **文件上傳**: 新增多個文件上傳功能，需要統一文件處理邏輯

### 開發順序建議
1. 先完成數據庫結構變更（Allowances主檔案、新字段添加）
2. 再實現核心業務邏輯（Areas of Experience重構、評分機制）
3. 最後處理UI相關的顯示優化

---

*文檔生成時間: 2025年1月21日*
*總預計開發時間: 24.5小時*