package services

import (
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"path/filepath"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xs3"
	"github.com/Norray/xrocket/xtool"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

var ProfessionalFileService = new(professionalFileService)

type professionalFileService struct{}

// region ---------------------------------------------------- Checker ----------------------------------------------------

// 檢查文件是否存在
func (s *professionalFileService) CheckFileExist(db *gorm.DB, userId uint64, fileCodes []string, ids []uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.professional_file.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	if len(ids) == 0 {
		return true, i18n.Message{}, nil
	}
	ids = xtool.Uint64ArrayDeduplication(ids)

	for _, id := range ids {
		if id == 0 {
			return false, msg, nil
		}
	}

	var count int64
	if err := db.Model(&model.ProfessionalFile{}).Where("user_id = ?", userId).
		Where("file_code IN (?)", fileCodes).
		Where("id IN (?)", ids).Count(&count).Error; err != nil {
		return false, msg, err
	}
	if int64(len(ids)) == count {
		return true, i18n.Message{}, nil
	}
	return false, msg, nil
}

// endregion ---------------------------------------------------- Checker ----------------------------------------------------

// region ---------------------------------------------------- 上傳文件 ----------------------------------------------------

type ProfessionalFileUploadFileReq struct {
	FileCode       string                `form:"fileCode" binding:"required,oneof=PROFESSIONAL_PHOTO QUALIFICATION_CERTIFICATE AHPRA_CERT PROFESSIONAL_ABN IND_INSURANCE_CERT AUSTRALIAN_PASSPORT FOREIGN_PASSPORT AUSTRALIAN_BIRTH_CERT AUSTRALIAN_CITIZENSHIP_CERT CURRENT_AUSTRALIA_DRIVER_LICENCE AUSTRALIAN_PUBLIC_SERVICE_EMPLOYEE_ID_CARD OTHER_AUSTRALIAN_GOVERNMENT_ISSUE_ID_CARD TERTIARY_STUDENT_ID_CARD CREDIT_DEBIT_ATM_CARD MEDICARE_CARD UTILITY_BILL_OR_RATE_NOTICE STATEMENT_FROM_FINANCIAL_INSTITUTION CENTRELINK_OR_PENSION_CARD VISA NATIONAL_CRIMINAL_CHECK WORKING_WITH_CHILDREN_OR_VULNERABLE_PEOPLE CURRENT_IMMUNISATION_RECORDS COMMONWEALTH_STATUTORY_DECLARATION ADDITIONAL_CERTIFICATION DISCLOSURE SIGNED_AGREEMENT PPCWQ_EXPERIENCE_AGED_CARE_DISABILITY	PPCWQ_CERT_III_AGED_CARE PPCWQ_CERT_III_DISABILITIES PPCWQ_CERT_III_INDIVIDUAL_SUPPORT PPCWQ_CERT_III_INDIVIDUAL_SUPPORT_AGED PPCWQ_CERT_III_INDIVIDUAL_SUPPORT_DISABILITY PPCWQ_CERT_III_HOME_COMMUNITY_CARE PPCWQ_CERT_IV_AGED_CARE PPCWQ_CERT_IV_DISABILITIES PPCWQ_CERT_IV_HOME_COMMUNITY_CARE PPCWQ_DEGREE_ALLIED_HEALTH PPCWQ_DEGREE_NURSING PPCWQ_OTHER_RELEVANT PPCWQ_WORKING_TOWARD_NURSING UPDATE_PROMPT"` // 文件類型：PHOTO=照片、AHPRA_CERT=AHPRA證書、ABN=ABN、IND_INSURANCE_CERT=專業人士責任保險證明、AUSTRALIAN_PASSPORT=澳洲護照、FOREIGN_PASSPORT=外國護照、AUSTRALIAN_BIRTH_CERT=澳洲出生證明、AUSTRALIAN_CITIZENSHIP_CERT=澳洲公民證、CURRENT_AUSTRALIA_DRIVER_LICENCE=澳洲駕照、AUSTRALIAN_PUBLIC_SERVICE_EMPLOYEE_ID_CARD=澳洲公務員ID卡、OTHER_AUSTRALIAN_GOVERNMENT_ISSUE_ID_CARD=其他澳洲政府發出的ID卡、TERTIARY_STUDENT_ID_CARD=大學生ID卡、CREDIT_DEBIT_ATM_CARD=信用卡/扣帳卡/ATM卡、MEDICARE_CARD=醫療卡、UTILITY_BILL_OR_RATE_NOTICE=水電費單或收費通知、STATEMENT_FROM_FINANCIAL_INSTITUTION=金融機構的結單、CENTRELINK_OR_PENSION_CARD=澳洲国民福利署或養老金卡、VISA=簽證、NATIONAL_CRIMINAL_CHECK=國家犯罪檢查、WORKING_WITH_CHILDREN_OR_VULNERABLE_PEOPLE=兒童/脆弱人群工作檢查、CURRENT_IMMUNISATION_RECORDS=現在的免疫記錄、COMMONWEALTH_STATUTORY_DECLARATION=聯邦法定聲明、ADDITIONAL_CERTIFICATION=附加證明、DISCLOSURE=披露、SIGNED_AGREEMENT=已簽署的協議 UPDATE_PROMPT=更新提示
	ProfessionalId uint64                `form:"-" swaggerignore:"true"`
	File           *multipart.FileHeader `form:"-" swaggerignore:"true"`
	AiExpireDate   AiExpireDate          `form:"-" swaggerignore:"true"`
}
type ProfessionalFileUploadFileResp struct {
	ProfessionalFileId uint64 `json:"professionalFileId"` // 專業人士文件ID
	ExpireDate         string `json:"expireDate"`         // 到期日
}

var AiFileMap = map[string]bool{
	model.ProfessionalFileCodeAhpraCertificate:              true,
	model.ProfessionalFileCodeIndemnityInsuranceCertificate: true,

	model.ProfessionalFileCodeAustralianPassport:                    true,
	model.ProfessionalFileCodeForeignPassport:                       true,
	model.ProfessionalFileCodeAustralianBirthCertificate:            true,
	model.ProfessionalFileCodeAustralianCitizenshipCertificate:      true,
	model.ProfessionalFileCodeCurrentAustraliaDriverLicence:         true,
	model.ProfessionalFileCodeAustralianPublicServiceEmployeeIDCard: true,
	model.ProfessionalFileCodeOtherAustralianGovernmentIssueIDCard:  true,
	model.ProfessionalFileCodeTertiaryStudentIDCard:                 true,
	model.ProfessionalFileCodeCreditDebitAtmCard:                    true,
	model.ProfessionalFileCodeMedicareCard:                          true,
	model.ProfessionalFileCodeUtilityBillOrRateNotice:               true,
	model.ProfessionalFileCodeStatementFromFinancialInstitution:     true,
	model.ProfessionalFileCodeCentrelinkOrPensionCard:               true,

	model.ProfessionalFileCodeVisa:                                  true,
	model.ProfessionalFileCodeNationalCriminalCheck:                 true,
	model.ProfessionalFileCodeWorkingWithChildrenOrVulnerablePeople: true,

	model.ProfessionalFileCodeAdditionalCertification: true,
}

type AiExpireDate struct {
	AiResp
	ExpireDate string `json:"expireDate"` // 到期日
}

// AI識別圖片的到期日
func (s *professionalFileService) AiExpireDate(db *gorm.DB, fileCode string, file *multipart.FileHeader) (AiExpireDate, error) {
	var err error
	var resp AiExpireDate
	// 讀取文件
	reader, err := file.Open()
	if err != nil {
		return resp, err
	}
	defer reader.Close()

	// 讀取文件
	fileData, err := io.ReadAll(reader)
	if err != nil {
		return resp, err
	}
	var aiResp AiResp
	if AiFileMap[fileCode] {
		aiResp, err = AiService.AiExpireDate(db, file.Filename, fileData)
		if err != nil {
			return resp, err
		}
		_ = copier.Copy(&resp.AiResp, aiResp)
		if aiResp.Output != "" {
			var data map[string]string
			err = json.Unmarshal([]byte(aiResp.Output), &data)
			if err != nil {
				return resp, err
			}
			resp.ExpireDate = data["expireDate"]
		}
	}
	return resp, nil
}
func (s *professionalFileService) UploadFile(db *gorm.DB, req ProfessionalFileUploadFileReq, userId uint64) (ProfessionalFileUploadFileResp, error) {
	var err error
	var resp ProfessionalFileUploadFileResp
	var professional model.Professional
	if err = db.Where("id = ?", req.ProfessionalId).First(&professional).Error; err != nil {
		return resp, err
	}

	filename := req.File.Filename
	uuidStr := uuid.NewV4().String()
	ext := filepath.Ext(filename) // 獲取文件擴展名
	uuidFileName := uuidStr + ext

	// 讀取文件
	reader, err := req.File.Open()
	if err != nil {
		return resp, err
	}
	defer reader.Close()

	// 構建縮略圖
	thumbnailResp, err := ImageService.ConvertToThumbnail(reader, filename, uuidStr)
	if err != nil {
		return resp, err
	}

	// 上傳原文件到OSS
	professionalFile := model.ProfessionalFile{
		UserId:             userId,
		FileCode:           req.FileCode,
		Mode:               xs3.PrivateMode,
		Bucket:             xconfig.OSSConf.Bucket,
		Path:               fmt.Sprintf(OSSProfessionalFilePath, professional.Id, req.FileCode, uuidFileName),
		Uuid:               uuidStr,
		OriginFileName:     filename,
		FileName:           uuidFileName,
		FileType:           ext,
		FileSize:           uint32(req.File.Size),
		ThumbnailPath:      fmt.Sprintf(OSSProfessionalFilePath, professional.Id, req.FileCode, thumbnailResp.ThumbnailUuidName),
		ThumbnailFileSize:  thumbnailResp.Size,
		AiResultJson:       req.AiExpireDate.Output,
		AiModel:            req.AiExpireDate.Model,
		AiInputTokenUsage:  req.AiExpireDate.InputToken,
		AiOutputTokenUsage: req.AiExpireDate.OutputToken,
	}
	// 先上傳縮略圖
	err = xs3.UploadObjectFromReader(professionalFile.Bucket, professionalFile.ThumbnailPath, professionalFile.OriginFileName, thumbnailResp.ThumbnailBody)
	if err != nil {
		return resp, err
	}
	// 重置 reader 指針
	_, err = reader.Seek(0, io.SeekStart)
	if err != nil {
		return resp, err
	}

	// 上傳原文件到OSS
	err = xs3.UploadObjectFromReader(professionalFile.Bucket, professionalFile.Path, professionalFile.OriginFileName, reader)
	if err != nil {
		return resp, err
	}
	if err = db.Create(&professionalFile).Error; err != nil {
		return resp, err
	}
	resp.ProfessionalFileId = professionalFile.Id
	resp.ExpireDate = req.AiExpireDate.ExpireDate
	return resp, nil
}

// endregion ---------------------------------------------------- 上傳文件 ----------------------------------------------------

// region ---------------------------------------------------- 預覽文件 ----------------------------------------------------

type ProfessionalFileGetPreviewReq struct {
	ProfessionalId     uint64 `form:"-" swaggerignore:"true"`
	ProfessionalFileId uint64 `form:"professionalFileId" binding:"required"`
	Thumb              string `form:"thumb" binding:"required,oneof=Y N"`
}

type ProfessionalFileGetPreviewResp struct {
	FileBytes []byte
	Filename  string
	UuidName  string
}

func (s *professionalFileService) Preview(db *gorm.DB, req ProfessionalFileGetPreviewReq) (ProfessionalFileGetPreviewResp, error) {
	var err error
	var resp ProfessionalFileGetPreviewResp
	var m model.ProfessionalFile
	if err = db.First(&m, req.ProfessionalFileId).Error; err != nil {
		return resp, err
	}
	var object []byte
	if req.Thumb == "Y" {
		object, err = xs3.GetObject(m.Bucket, m.ThumbnailPath)
	} else {
		object, err = xs3.GetObject(m.Bucket, m.Path)
	}
	if err != nil {
		return ProfessionalFileGetPreviewResp{}, err
	}
	resp.FileBytes = object
	resp.Filename = m.OriginFileName
	resp.UuidName = m.FileName
	return resp, nil
}

// endregion ---------------------------------------------------- 預覽文件 ----------------------------------------------------

// region ---------------------------------------------------- 機構預覽專業人士文件 ----------------------------------------------------

type ProfessionalFileGetPreviewByFacilityReq struct {
	FacilityId         uint64 `form:"facilityId" binding:"required"`
	ProfessionalId     uint64 `form:"professionalId" binding:"required"`
	ProfessionalFileId uint64 `form:"professionalFileId" binding:"required"`
	Thumb              string `form:"thumb" binding:"required,oneof=Y N"`
}

// endregion ---------------------------------------------------- 機構預覽專業人士文件 ----------------------------------------------------
