package routers

import (
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtest"
)

func TestShiftTimeList(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/shift-times",
		UserId:           11,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ShiftTimeListReq{
					FacilityId: 3,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestShiftTimeUpdate(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/shift-times/actions/update",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "批量更新",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ShiftTimeUpdateReq{
					FacilityId: 1,
					Items:      []services.ShiftTimeItem{},
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFixInitShiftTime(t *testing.T) {
	var err error
	var facilityArr []model.Facility
	if err = xgorm.DB.Find(&facilityArr).Error; err != nil {
		t.Fatal(err)
	}
	// 循環先查詢是否已存在shift time，如果不存在則 init
	for _, facility := range facilityArr {
		var shiftTimeArr []model.ShiftTime
		if err = xgorm.DB.Where("facility_id = ?", facility.Id).Find(&shiftTimeArr).Error; err != nil {
			t.Fatal(err)
		}
		if len(shiftTimeArr) == 0 {
			err = services.ShiftTimeService.Init(xgorm.DB, facility.Id)
			if err != nil {
				t.Fatal(err)
			}
		}
	}
}
