package professional_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/shopspring/decimal"
)

type ProfessionalConfirmationNoteController struct {
	v1.CommonController
}

func NewProfessionalConfirmationNoteController() ProfessionalConfirmationNoteController {
	return ProfessionalConfirmationNoteController{}
}

// @Tags Confirmation Note
// @Summary 獲取確認通知單列表
// @Description
// @Router /v1/professional/confirmation-notes [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.ProfessionalConfirmationNoteListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Param sortingKey query string false "createTime 申請时间, amount 金額"
// @Param sortingType query string false "a=順序,d=倒序"
// @Success 200 {object} []services.ProfessionalConfirmationNoteListResp "Success"
func (con ProfessionalConfirmationNoteController) ProfessionalList(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalConfirmationNoteListReq
	var pageSet xresp.PageSet
	var sortSet xresp.SortingSet
	_ = c.ShouldBindQuery(&pageSet)
	_ = c.ShouldBindQuery(&sortSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.ConfirmationNoteService.ProfessionalList(db, req, nc.GetJWTUserId(), &pageSet, sortSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Confirmation Note
// @Summary 查詢確認通知單詳情
// @Description
// @Router /v1/professional/confirmation-notes/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.ConfirmationNoteInquireReq true "parameter"
// @Success 200 {object} services.ConfirmationNoteInquireResp "Success"
func (con ProfessionalConfirmationNoteController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ConfirmationNoteInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		// 檢查申請是否可以取消
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.ConfirmationNoteService.CheckIdExist(db, &model.Document{}, model.DocumentCategoryConfirmation, req.DocumentId, nc.GetJWTUserId())
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		resp, err := services.ConfirmationNoteService.ProfessionalInquire(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Confirmation Note
// @Summary 創建確認通知單
// @Description
// @Router /v1/professional/confirmation-notes/actions/create [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ConfirmationNoteCreateReq true "parameter"
// @Success 200 {object} services.ConfirmationNoteCreateResp "Success"
func (con ProfessionalConfirmationNoteController) Create(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ConfirmationNoteCreateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		superRate := decimal.NewFromInt(12).Div(decimal.NewFromInt(100))
		taxRate := decimal.NewFromInt(10).Div(decimal.NewFromInt(100))

		var jobApplication model.JobApplication
		var professional model.Professional
		var jobAllowanceMap map[string]decimal.Decimal
		jobShiftIds := make([]uint64, 0)

		// 檢查申請是否可以取消
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.JobApplicationService.CheckIdExist(db, &jobApplication, req.JobApplicationId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckIdExist(db, &professional, jobApplication.ProfessionalId, nc.GetJWTUserId())
			}).
			Run(func() (bool, i18n.Message, error) {
				if len(req.WagesDocumentFileIds) > 0 {
					return services.DocumentFileService.CheckIdsExist(db, req.WagesDocumentFileIds, nc.GetJWTUserId())
				}
				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				if len(req.OtherDocumentFileIds) > 0 {
					return services.DocumentFileService.CheckIdsExist(db, req.OtherDocumentFileIds, nc.GetJWTUserId())
				}
				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				if len(req.WagesItem.Items) > 0 {
					for _, item := range req.WagesItem.Items {
						if item.JobShiftId > 0 {
							jobShiftIds = append(jobShiftIds, item.JobShiftId)
						}
					}
					return services.JobShiftTimeService.CheckJobShiftTimeCanSelect(db, nc.GetJWTUserId(), jobApplication.JobId, jobShiftIds, 0)
				}
				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				if len(req.WagesItem.Items) > 0 {
					for _, item := range req.WagesItem.Items {
						if item.JobShiftId > 0 {
							jobShiftIds = append(jobShiftIds, item.JobShiftId)
						}
					}
					return services.JobShiftTimeService.CheckJobShiftTimeCanSelect(db, nc.GetJWTUserId(), jobApplication.JobId, jobShiftIds, 0)
				}
				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				jobAllowances, err := services.JobAllowanceService.GetAllowance(db, jobApplication.JobId, jobShiftIds)
				if err != nil {
					return false, i18n.Message{}, err
				}
				checkMap := make(map[string]bool)
				if len(jobAllowances) > 0 {
					jobAllowanceMap = make(map[string]decimal.Decimal)
					for _, jobAllowance := range jobAllowances {
						if jobAllowance.AllowanceType == model.JobAllowanceAllowanceTypeHourly {
							// HOURLY 的津貼金額是根據時數計算，所以需要設定為0，檢查時只需要存在即可
							jobAllowanceMap[jobAllowance.AllowanceName] = decimal.Zero
						} else {
							// SHIFT JOB 的津貼金額是固定，需要對比上傳的津貼金額是否正確
							jobAllowanceMap[jobAllowance.AllowanceName] = jobAllowanceMap[jobAllowance.AllowanceName].Add(jobAllowance.Amount)
						}

						checkMap[jobAllowance.AllowanceName] = false
					}
				}

				// 檢查各個津貼金額是否存在並正確
				for _, item := range req.WagesItem.Items {
					if item.ItemType == model.DocumentItemTypeAllowance {
						if checkMap[item.ItemName] {
							return false, i18n.Message{
								ID:    "checker.confirmation_note.item.allowance_duplicate",
								Other: "Allowance duplicate.",
							}, nil
						}
						if amount, ok := jobAllowanceMap[item.ItemName]; !ok {
							return false, i18n.Message{
								ID:    "checker.confirmation_note.item.allowance_not_found",
								Other: "Allowance not found.",
							}, nil
						} else {
							if amount.GreaterThan(decimal.Zero) {
								if !amount.Equal(item.TotalAmount) {
									return false, i18n.Message{
										ID:    "checker.confirmation_note.item.allowance_amount_invalid",
										Other: "Invalid allowance amount submitted.",
									}, nil
								} else {
									checkMap[item.ItemName] = true
								}
							} else {
								checkMap[item.ItemName] = true
							}
						}
					}
				}

				// 檢查是否所有津貼都已通過檢查
				for _, ok := range checkMap {
					if !ok {
						return false, i18n.Message{
							// 提交的津貼數據有誤
							ID:    "checker.confirmation_note.item.allowance_invalid",
							Other: "Invalid allowance data submitted.",
						}, nil
					}
				}

				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				checkItemValidReq := services.CheckItemValidReq{
					WagesItem:   req.WagesItem,
					OtherItem:   req.OtherItem,
					TotalAmount: req.TotalAmount,
					TaxAmount:   req.TaxAmount,
					SuperAmount: req.SuperAmount,
					GrandTotal:  req.GrandTotal,
				}
				return services.ConfirmationNoteService.CheckItemValid(checkItemValidReq, superRate, taxRate, professional)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		resp, err := services.ConfirmationNoteService.Create(tx, req, superRate, taxRate, jobApplication, professional)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Confirmation Note
// @Summary 更新確認通知單
// @Description
// @Router /v1/professional/confirmation-notes/actions/update [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ConfirmationNoteUpdateReq true "parameter"
// @Success 200 "Success"
func (con ProfessionalConfirmationNoteController) Update(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ConfirmationNoteUpdateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var document model.Document
		var jobApplication model.JobApplication
		var professional model.Professional
		var jobAllowanceMap map[string]decimal.Decimal
		jobShiftIds := make([]uint64, 0)

		// 檢查申請是否可以取消
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.ConfirmationNoteService.CheckIdExist(db, &document, model.DocumentCategoryConfirmation, req.DocumentId, nc.GetJWTUserId())
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.JobApplicationService.CheckIdExist(db, &jobApplication, document.JobApplicationId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckIdExist(db, &professional, jobApplication.ProfessionalId, nc.GetJWTUserId())
			}).
			Run(func() (bool, i18n.Message, error) {
				if len(req.WagesDocumentFileIds) > 0 {
					return services.DocumentFileService.CheckIdsExist(db, req.WagesDocumentFileIds, nc.GetJWTUserId())
				}
				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				if len(req.OtherDocumentFileIds) > 0 {
					return services.DocumentFileService.CheckIdsExist(db, req.OtherDocumentFileIds, nc.GetJWTUserId())
				}
				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				if len(req.WagesItem.Items) > 0 {
					for _, item := range req.WagesItem.Items {
						if item.JobShiftId > 0 {
							jobShiftIds = append(jobShiftIds, item.JobShiftId)
						}
					}
					return services.JobShiftTimeService.CheckJobShiftTimeCanSelect(db, nc.GetJWTUserId(), jobApplication.JobId, jobShiftIds, document.Id)
				}
				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				jobAllowances, err := services.JobAllowanceService.GetAllowance(db, jobApplication.JobId, jobShiftIds)
				if err != nil {
					return false, i18n.Message{}, err
				}
				checkMap := make(map[string]bool)
				if len(jobAllowances) > 0 {
					jobAllowanceMap = make(map[string]decimal.Decimal)
					for _, jobAllowance := range jobAllowances {
						if jobAllowance.AllowanceType == model.JobAllowanceAllowanceTypeHourly {
							// HOURLY 的津貼金額是根據時數計算，所以需要設定為0，檢查時只需要存在即可
							jobAllowanceMap[jobAllowance.AllowanceName] = decimal.Zero
						} else {
							// SHIFT JOB 的津貼金額是固定，需要對比上傳的津貼金額是否正確
							jobAllowanceMap[jobAllowance.AllowanceName] = jobAllowanceMap[jobAllowance.AllowanceName].Add(jobAllowance.Amount)
						}

						checkMap[jobAllowance.AllowanceName] = false
					}
				}

				// 檢查各個津貼金額是否存在並正確
				for _, item := range req.WagesItem.Items {
					if item.ItemType == model.DocumentItemTypeAllowance {
						if checkMap[item.ItemName] {
							return false, i18n.Message{
								ID:    "checker.confirmation_note.item.allowance_duplicate",
								Other: "Allowance duplicate.",
							}, nil
						}
						if amount, ok := jobAllowanceMap[item.ItemName]; !ok {
							return false, i18n.Message{
								ID:    "checker.confirmation_note.item.allowance_not_found",
								Other: "Allowance not found.",
							}, nil
						} else {
							if amount.GreaterThan(decimal.Zero) {
								if !amount.Equal(item.TotalAmount) {
									return false, i18n.Message{
										ID:    "checker.confirmation_note.item.allowance_amount_invalid",
										Other: "Invalid allowance amount submitted.",
									}, nil
								} else {
									checkMap[item.ItemName] = true
								}
							} else {
								checkMap[item.ItemName] = true
							}
						}
					}
				}

				// 檢查是否所有津貼都已通過檢查
				for _, ok := range checkMap {
					if !ok {
						return false, i18n.Message{
							// 提交的津貼數據有誤
							ID:    "checker.confirmation_note.item.allowance_invalid",
							Other: "Invalid allowance data submitted.",
						}, nil
					}
				}

				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				checkItemValidReq := services.CheckItemValidReq{
					WagesItem:   req.WagesItem,
					OtherItem:   req.OtherItem,
					TotalAmount: req.TotalAmount,
					TaxAmount:   req.TaxAmount,
					SuperAmount: req.SuperAmount,
					GrandTotal:  req.GrandTotal,
				}
				return services.ConfirmationNoteService.CheckItemValid(checkItemValidReq, document.SuperRate, document.TaxRate, professional)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()

		err = services.ConfirmationNoteService.Update(tx, document, req, document.SuperRate, document.TaxRate, jobApplication, professional)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Confirmation Note
// @Summary 提交確認通知單
// @Description
// @Router /v1/professional/confirmation-notes/actions/submit [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ConfirmationNoteSubmitReq true "parameter"
// @Success 200 "Success"
func (con ProfessionalConfirmationNoteController) Submit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ConfirmationNoteSubmitReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		superRate := decimal.NewFromInt(12).Div(decimal.NewFromInt(100))
		taxRate := decimal.NewFromInt(10).Div(decimal.NewFromInt(100))

		var document model.Document
		var jobApplication model.JobApplication
		var professional model.Professional
		var jobAllowanceMap map[string]decimal.Decimal
		jobShiftIds := make([]uint64, 0)

		// 檢查申請是否可以取消
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				if req.DocumentId > 0 {
					return services.ConfirmationNoteService.CheckIdExist(db, &document, model.DocumentCategoryConfirmation, req.DocumentId, nc.GetJWTUserId())
				}
				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				if document.Id > 0 {
					return services.ConfirmationNoteService.CheckCanSubmit(db, model.DocumentCategoryConfirmation, document.Id, nc.GetJWTUserId())
				}
				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				if document.Id > 0 {
					return services.JobApplicationService.CheckIdExist(db, &jobApplication, document.JobApplicationId)
				} else {
					return services.JobApplicationService.CheckIdExist(db, &jobApplication, req.JobApplicationId)
				}
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckIdExist(db, &professional, jobApplication.ProfessionalId, nc.GetJWTUserId())
			}).
			Run(func() (bool, i18n.Message, error) {
				if len(req.WagesDocumentFileIds) > 0 {
					return services.DocumentFileService.CheckIdsExist(db, req.WagesDocumentFileIds, nc.GetJWTUserId())
				}
				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				if len(req.OtherDocumentFileIds) > 0 {
					return services.DocumentFileService.CheckIdsExist(db, req.OtherDocumentFileIds, nc.GetJWTUserId())
				}
				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				msg := i18n.Message{
					ID:    "checker.confirmation_note.item.invalid",
					Other: "Invalid item data submitted.",
				}
				if len(req.WagesItem.Items)+len(req.OtherItem.Items) == 0 {
					return false, msg, nil
				}

				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				if len(req.WagesItem.Items) > 0 {
					for _, item := range req.WagesItem.Items {
						if item.JobShiftId > 0 {
							jobShiftIds = append(jobShiftIds, item.JobShiftId)
						}
					}
					return services.JobShiftTimeService.CheckJobShiftTimeCanSelect(db, nc.GetJWTUserId(), jobApplication.JobId, jobShiftIds, document.Id)
				}
				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				jobAllowances, err := services.JobAllowanceService.GetAllowance(db, jobApplication.JobId, jobShiftIds)
				if err != nil {
					return false, i18n.Message{}, err
				}
				checkMap := make(map[string]bool)
				if len(jobAllowances) > 0 {
					jobAllowanceMap = make(map[string]decimal.Decimal)
					for _, jobAllowance := range jobAllowances {
						if jobAllowance.AllowanceType == model.JobAllowanceAllowanceTypeHourly {
							// HOURLY 的津貼金額是根據時數計算，所以需要設定為0，檢查時只需要存在即可
							jobAllowanceMap[jobAllowance.AllowanceName] = decimal.Zero
						} else {
							// SHIFT JOB 的津貼金額是固定，需要對比上傳的津貼金額是否正確
							jobAllowanceMap[jobAllowance.AllowanceName] = jobAllowanceMap[jobAllowance.AllowanceName].Add(jobAllowance.Amount)
						}

						checkMap[jobAllowance.AllowanceName] = false
					}
				}

				// 檢查各個津貼金額是否存在並正確
				for _, item := range req.WagesItem.Items {
					if item.ItemType == model.DocumentItemTypeAllowance {
						if checkMap[item.ItemName] {
							return false, i18n.Message{
								ID:    "checker.confirmation_note.item.allowance_duplicate",
								Other: "Allowance duplicate.",
							}, nil
						}
						if amount, ok := jobAllowanceMap[item.ItemName]; !ok {
							return false, i18n.Message{
								ID:    "checker.confirmation_note.item.allowance_not_found",
								Other: "Allowance not found.",
							}, nil
						} else {
							if amount.GreaterThan(decimal.Zero) {
								if !amount.Equal(item.TotalAmount) {
									return false, i18n.Message{
										ID:    "checker.confirmation_note.item.allowance_amount_invalid",
										Other: "Invalid allowance amount submitted.",
									}, nil
								} else {
									checkMap[item.ItemName] = true
								}
							} else {
								checkMap[item.ItemName] = true
							}
						}
					}
				}

				// 檢查是否所有津貼都已通過檢查
				for _, ok := range checkMap {
					if !ok {
						return false, i18n.Message{
							// 提交的津貼數據有誤
							ID:    "checker.confirmation_note.item.allowance_invalid",
							Other: "Invalid allowance data submitted.",
						}, nil
					}
				}

				return true, i18n.Message{}, nil
			}).
			Run(func() (bool, i18n.Message, error) {
				checkItemValidReq := services.CheckItemValidReq{
					WagesItem:   req.WagesItem,
					OtherItem:   req.OtherItem,
					TotalAmount: req.TotalAmount,
					TaxAmount:   req.TaxAmount,
					SuperAmount: req.SuperAmount,
					GrandTotal:  req.GrandTotal,
				}
				if document.Id > 0 {
					superRate = document.SuperRate
					taxRate = document.TaxRate
				}
				return services.ConfirmationNoteService.CheckItemValid(checkItemValidReq, superRate, taxRate, professional)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.ConfirmationNoteService.Submit(tx, document, req, superRate, taxRate, jobApplication, professional)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Confirmation Note
// @Summary 取消確認通知單
// @Description
// @Router /v1/professional/confirmation-notes/actions/cancel [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ConfirmationNoteCancelReq true "parameter"
// @Success 200 "Success"
func (con ProfessionalConfirmationNoteController) Cancel(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ConfirmationNoteCancelReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		// 檢查申請是否可以取消
		var alertMsg []string
		alerter := xapp.NewCK(c)
		alerter.
			Run(func() (bool, i18n.Message, error) {
				return services.ConfirmationNoteService.CheckIdExist(db, &model.Document{}, model.DocumentCategoryConfirmation, req.DocumentId, nc.GetJWTUserId())
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.ConfirmationNoteService.CheckCanCancel(db, model.DocumentCategoryConfirmation, req.DocumentId, nc.GetJWTUserId())
			})
		alertMsg, err = alerter.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}
		tx := db.Begin()
		err = services.ConfirmationNoteService.Cancel(tx, req, model.DocumentCategoryConfirmation, nc.GetJWTUserId())
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Confirmation Note
// @Summary 獲取可以申請確認通知單的工作列表
// @Description
// @Router /v1/professional/confirmation-notes/actions/job-list [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.ConfirmationNoteJobListReq true "parameter"
// @Success 200 {object} []services.ConfirmationNoteJobListResp "Success"
func (con ProfessionalConfirmationNoteController) JobList(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ConfirmationNoteJobListReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.ReqUserId = nc.GetJWTUserId()
		resp, err := services.JobService.ConfirmationNoteJobList(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Confirmation Note
// @Summary 獲取申請確認通知單工作的班次
// @Description
// @Router /v1/professional/confirmation-notes/actions/job-shift-time-list [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.JobShiftTimeListReq true "parameter"
// @Success 200 {object} []services.JobShiftTimeListResp "Success"
func (con ProfessionalConfirmationNoteController) JobShiftTimeList(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobShiftTimeListReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.ReqUserId = nc.GetJWTUserId()
		resp, err := services.JobService.JobShiftTimeList(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
