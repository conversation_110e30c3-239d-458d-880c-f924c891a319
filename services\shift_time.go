package services

import (
	"regexp"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtool"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

var ShiftTimeService = new(shiftTimeService)

type shiftTimeService struct{}

func (s *shiftTimeService) CheckIdsExist(db *gorm.DB, facilityId uint64, items []ShiftTimeItem) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.shift_time.id.does_not_exist",
		Other: "No such record, please try after reloading.",
	}
	ids := make([]uint64, 0)
	for _, item := range items {
		ids = append(ids, item.ShiftTimeId)
	}
	var err error
	ids = xtool.Uint64ArrayDeduplication(ids)
	var qty int64
	if err = db.Model(&model.ShiftTime{}).Where("facility_id = ?", facilityId).Where("id IN (?)", ids).Count(&qty).Error; err != nil {
		return false, msg, err
	}
	if qty != int64(len(ids)) {
		return false, msg, nil
	}
	return true, msg, nil
}

func (s *shiftTimeService) CheckTimeFormat(items []ShiftTimeItem) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.shift_time.time_format.invalid",
		Other: "Time format must be mm:ss",
	}

	for _, item := range items {
		if item.BeginTime != "" && !s.validateTimeFormat(item.BeginTime) {
			return false, msg, nil
		}
		if item.EndTime1 != "" && !s.validateTimeFormat(item.EndTime1) {
			return false, msg, nil
		}
		if item.EndTime2 != "" && !s.validateTimeFormat(item.EndTime2) {
			return false, msg, nil
		}
	}
	return true, msg, nil
}

func (s *shiftTimeService) validateTimeFormat(timeStr string) bool {
	if timeStr == "" {
		return true
	}
	pattern := `^([0-5]?[0-9]):([0-5]?[0-9])$`
	matched, _ := regexp.MatchString(pattern, timeStr)
	return matched
}

type ShiftTimeListReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"`
	Name       string `form:"name"`
}

type ShiftTimeListResp struct {
	ShiftTimeId uint64          `json:"shiftTimeId"`
	FacilityId  uint64          `json:"facilityId"`
	Name        string          `json:"name"`
	BeginTime   string          `json:"beginTime"`
	EndTime1    string          `json:"endTime1"`
	NextDay1    string          `json:"nextDay1"`
	EndTime2    string          `json:"endTime2"`
	NextDay2    string          `json:"nextDay2"`
	HourlyRate  decimal.Decimal `json:"hourlyRate"`
}

func (s *shiftTimeService) List(db *gorm.DB, req ShiftTimeListReq, pageSet *xresp.PageSet) ([]ShiftTimeListResp, error) {
	var err error
	var resp []ShiftTimeListResp
	builder := db.Table("shift_time AS st").Select([]string{
		"st.id AS shift_time_id",
		"st.facility_id",
		"st.name",
		"st.begin_time",
		"st.end_time1",
		"st.next_day1",
		"st.end_time2",
		"st.next_day2",
		"st.hourly_rate",
	}).Where("st.facility_id = ?", req.FacilityId)

	if req.Name != "" {
		builder = builder.Where("st.name LIKE ?", "%"+req.Name+"%")
	}

	if err = builder.Scopes(xresp.Paginate(pageSet)).Order("st.id").Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

type ShiftTimeUpdateReq struct {
	FacilityId uint64          `json:"facilityId" binding:"required"`
	Items      []ShiftTimeItem `json:"items" binding:"required"`
}

type ShiftTimeItem struct {
	ShiftTimeId uint64          `json:"shiftTimeId" binding:"required"`
	BeginTime   string          `json:"beginTime"`
	EndTime1    string          `json:"endTime1"`
	NextDay1    string          `json:"nextDay1" binding:"omitempty,oneof=Y,N"` // 如果沒有則不用傳
	EndTime2    string          `json:"endTime2"`
	NextDay2    string          `json:"nextDay2" binding:"required,oneof=Y,N"`
	HourlyRate  decimal.Decimal `json:"hourlyRate"`
}

func (s *shiftTimeService) Update(db *gorm.DB, req ShiftTimeUpdateReq) error {
	var err error

	// 插入新記錄
	for _, item := range req.Items {
		var shiftTime model.ShiftTime
		if err = db.Where("id = ?", item.ShiftTimeId).Where("facility_id = ?", req.FacilityId).First(&shiftTime).Error; err != nil {
			return err
		}
		shiftTime.BeginTime = item.BeginTime
		shiftTime.EndTime1 = item.EndTime1
		shiftTime.NextDay1 = item.NextDay1
		shiftTime.EndTime2 = item.EndTime2
		shiftTime.NextDay2 = item.NextDay2
		shiftTime.HourlyRate = item.HourlyRate
		if err = db.Save(&shiftTime).Error; err != nil {
			return err
		}
	}

	return nil
}

func (s *shiftTimeService) Init(db *gorm.DB, facilityId uint64) error {
	var err error
	initItem := []model.ShiftTime{
		{
			FacilityId: facilityId,
			Name:       "AM",
			HourlyRate: decimal.NewFromInt(1),
		},
		{
			FacilityId: facilityId,
			Name:       "PM",
			HourlyRate: decimal.NewFromInt(1),
		},
		{
			FacilityId: facilityId,
			Name:       "Night",
			HourlyRate: decimal.NewFromInt(1),
		},
	}
	if err = db.CreateInBatches(initItem, 10).Error; err != nil {
		return err
	}
	return nil
}
