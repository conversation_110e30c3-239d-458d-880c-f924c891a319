package routers

import (
	"fmt"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xmodel"
	"os"

	"github.com/Norray/xrocket/xcasbin"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgeoip2"
	"github.com/Norray/xrocket/xgoogle"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xi18n"
	"github.com/Norray/xrocket/xjwt"
	"github.com/Norray/xrocket/xredis"
	"github.com/Norray/xrocket/xs3"
	"github.com/Norray/xrocket/xtest"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
)

func init() {
	xconfig.Setup("../config/app.ini")
	if xconfig.ServerConf.HttpProxy != "" {
		if err := os.Setenv("HTTP_PROXY", xconfig.ServerConf.HttpProxy); err != nil {
			return
		}
		log.Println("HTTP_PROXY:", os.Getenv("HTTP_PROXY"))
	}
	if xconfig.ServerConf.HttpsProxy != "" {
		if err := os.Setenv("HTTPS_PROXY", xconfig.ServerConf.HttpsProxy); err != nil {
			return
		}
		log.Println("HTTPS_PROXY:", os.Getenv("HTTPS_PROXY"))
	}
	xredis.DefaultSetup()
	xgoogle.DefaultSetUp()
	xs3.DefaultSetup()
	xgorm.DefaultSetup()
	xjwt.DefaultSetup()
	gin.SetMode("release") // 去除路由註冊的打印
	xgeoip2.Setup("../resource/geoip/GeoLite2-City.mmdb")
	xtest.DefaultSetup(InitRouter())
	fmt.Println(xconfig.ServerConf.DisableLogColor)
	xcasbin.DefaultSetup("console_casbin", "../config/rbac_model.conf", "console_casbin")
	xi18n.Setup([]string{
		"../resource/i18n/active.en.toml",
		"../resource/i18n/active.zh-CN.toml",
		"../resource/i18n/active.zh-HK.toml",
	})
}

type testUserInfo struct {
	UserId                    uint64 `json:"userId"`
	UserType                  string `json:"userType"`
	Username                  string `json:"username"`
	Email                     string `json:"email"`
	FacilityId                uint64 `json:"facilityId"`
	DraftFacilityProfileId    uint64 `json:"draftFacilityProfileId"`
	ApprovedFacilityProfileId uint64 `json:"approvedFacilityProfileId"`
	DraftProfessionalId       uint64 `json:"draftProfessionalId"`
	ApprovedProfessionalId    uint64 `json:"approvedProfessionalId"`
}

func getTestUser(userId uint64) testUserInfo {
	db := xgorm.DB
	var userInfo testUserInfo
	if err := db.Model(&xmodel.User{}).
		Select([]string{
			"id AS user_id",
			"user_type",
			"username",
			"email",
		}).
		Where("id = ?", userId).First(&userInfo).Error; err != nil {
		panic(err)
	}
	if userInfo.UserType == model.UserUserTypeFacilityUser {
		if err := db.Table("facility_user").
			Select([]string{
				"facility_id",
			}).
			Where("user_id = ?", userId).
			Pluck("facility_id", &userInfo.FacilityId).Error; err != nil {
			panic(err)
		}
		if err := db.Model(&model.FacilityProfile{}).
			Select([]string{
				"id",
			}).
			Where("facility_id = ?", userInfo.FacilityId).
			Where("data_type = ?", model.FacilityProfileDataTypeDraft).
			Pluck("id", &userInfo.DraftProfessionalId).Error; err != nil {
			panic(err)
		}
		if err := db.Model(&model.FacilityProfile{}).
			Select([]string{
				"id",
			}).
			Where("facility_id = ?", userInfo.FacilityId).
			Where("data_type = ?", model.FacilityProfileDataTypeApproved).
			Pluck("id", &userInfo.ApprovedProfessionalId).Error; xgorm.IsSqlErr(err) {
			panic(err)
		}
	} else if userInfo.UserType == model.UserUserTypeProfessional {
		if err := db.Model(&model.Professional{}).
			Select([]string{
				"id",
			}).
			Where("user_id = ?", userId).
			Where("data_type = ?", model.ProfessionalDataTypeDraft).
			Pluck("id", &userInfo.DraftProfessionalId).Error; err != nil {
			panic(err)
		}
		if err := db.Model(&model.Professional{}).
			Select([]string{
				"id",
			}).
			Where("user_id = ?", userId).
			Where("data_type = ?", model.ProfessionalDataTypeApproved).
			Pluck("id", &userInfo.ApprovedProfessionalId).Error; xgorm.IsSqlErr(err) {
			panic(err)
		}
	}
	return userInfo
}
