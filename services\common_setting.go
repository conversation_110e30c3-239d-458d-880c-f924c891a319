package services

import (
	"fmt"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xmodel"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

var CommonSettingService = new(commonSettingService)

type commonSettingService struct{}

func (s *commonSettingService) CommonSettingInquire(db *gorm.DB, code string) (xmodel.CommonSetting, error) {
	var commonSetting xmodel.CommonSetting
	if err := db.Where("code = ?", code).First(&commonSetting).Error; err != nil {
		return commonSetting, err
	}
	return commonSetting, nil
}

func (s *commonSettingService) GetSettingValueByCode(db *gorm.DB, code string) (string, error) {
	commonSetting, err := s.CommonSettingInquire(db, code)
	if xgorm.IsNotFoundErr(err) {
		return "", nil
	} else if err != nil {
		return "", err
	}
	return commonSetting.SettingValue, nil
}

func (s *commonSettingService) GetSettingValueByCodeAsDecimal(db *gorm.DB, code string) (decimal.Decimal, error) {
	settingValue, err := s.GetSettingValueByCode(db, code)
	if err != nil {
		return decimal.Zero, err
	}
	// 检查空字符串情况，避免解析错误
	if settingValue == "" {
		return decimal.Zero, fmt.Errorf("setting value for code %s is empty", code)
	}
	decimalValue, err := decimal.NewFromString(settingValue)
	if err != nil {
		return decimal.Zero, err
	}
	return decimalValue, nil
}

type PlatformProfile struct {
	PlatformName              string `json:"platformName"`              // 平台名稱
	PlatformEmail             string `json:"platformEmail"`             // 平台Email
	PlatformAddress           string `json:"platformAddress"`           // 平台地址
	PlatformBankStateBranch   string `json:"platformBankStateBranch"`   // 平台銀行分行
	PlatformBankAccountNumber string `json:"platformBankAccountNumber"` // 平台銀行帳號
	PlatformBankAccountName   string `json:"platformBankAccountName"`   // 平台銀行戶名
}

func (s *commonSettingService) GetPlatformProfile(db *gorm.DB) (PlatformProfile, error) {
	var platformProfile PlatformProfile
	var commonSettings []xmodel.CommonSetting
	if err := db.Where("code IN (?)", []string{
		model.CommonSettingCodePlatformName,
		model.CommonSettingCodePlatformEmail,
		model.CommonSettingCodePlatformAddress,
		model.CommonSettingCodePlatformBankStateBranch,
		model.CommonSettingCodePlatformBankAccountNumber,
		model.CommonSettingCodePlatformBankAccountName,
	}).Find(&commonSettings).Error; err != nil {
		return platformProfile, err
	}
	for _, setting := range commonSettings {
		switch setting.Code {
		case model.CommonSettingCodePlatformName:
			platformProfile.PlatformName = setting.SettingValue
		case model.CommonSettingCodePlatformEmail:
			platformProfile.PlatformEmail = setting.SettingValue
		case model.CommonSettingCodePlatformAddress:
			platformProfile.PlatformAddress = setting.SettingValue
		case model.CommonSettingCodePlatformBankStateBranch:
			platformProfile.PlatformBankStateBranch = setting.SettingValue
		case model.CommonSettingCodePlatformBankAccountNumber:
			platformProfile.PlatformBankAccountNumber = setting.SettingValue
		case model.CommonSettingCodePlatformBankAccountName:
			platformProfile.PlatformBankAccountName = setting.SettingValue
		}
	}
	return platformProfile, nil
}
