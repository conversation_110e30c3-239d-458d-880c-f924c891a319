package task

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm/clause"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	"github.com/jinzhu/copier"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

const JobSchedulePublishTask = "job_schedule_publish_task" // rabbitmq

type JobSchedulePublishReq struct {
	JobScheduleId uint64 `json:"jobScheduleId"` // 工作發佈計劃Id
}

// 執行隊列
func JobSchedulePublish(taskJson string) (context.Context, error) {
	var err error
	ctx := context.Background()
	traceId := uuid.NewV4().String()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.WithField("traceId", traceId).WithField("task", JobSchedulePublishTask)

	taskData := JobSchedulePublishReq{}
	err = json.Unmarshal([]byte(taskJson), &taskData)
	if err != nil {
		logger.Errorf("fail to unmarshal task: %v, taskJson: %s", err, taskJson)
		return ctx, err
	}
	logger = logger.WithField("JobScheduleId", taskData.JobScheduleId)
	logger.Info("Start to publish job schedule")

	// 獲取數據庫連接
	db := xgorm.DB.WithContext(ctx)

	var jobSchedule model.JobSchedule
	if err = db.Where("id = ?", taskData.JobScheduleId).First(&jobSchedule).Error; err != nil {
		logger.Errorf("fail to get job schedule: %v", err)
		return ctx, err
	}
	var job model.Job
	if err = db.Where("job_schedule_id = ?", jobSchedule.Id).First(&job).Error; err != nil {
		logger.Errorf("fail to get job: %v", err)
		return ctx, err
	}

	nowDays := time.Now().UTC().Format(xtool.DateDayA)

	var jobScheduleDates []model.JobScheduleDate
	if err = db.Table("job_schedule_date AS jsd").
		Select("jsd.*").
		Joins("JOIN job_schedule AS js ON js.id = jsd.job_schedule_id AND js.status = ?", model.JobScheduleStatusEnable).
		Where("jsd.job_schedule_id = ?", jobSchedule.Id).
		Where("jsd.status = ?", model.JobScheduleDateStatusPending).
		Where("jsd.date >= ?", nowDays).
		Where("DATE_SUB(jsd.date, INTERVAL js.advance_days DAY) <= ?", nowDays).
		Order("jsd.date ASC").
		Find(&jobScheduleDates).Error; err != nil {
		logger.Errorf("fail to get job schedule date: %v", err)
		return ctx, err
	}

	if len(jobScheduleDates) == 0 {
		logger.Info("no job schedule date to publish")
		return ctx, nil
	}

	// 開啟事務
	tx := db.Begin()
	// 鎖定JobSchedule
	err = services.JobService.LockFacilityJobRecord(tx, job.FacilityId)
	if err != nil {
		tx.Rollback()
		logger.Errorf("fail to lock job schedule: %v", err)
		return ctx, err
	}

	for _, jobScheduleDate := range jobScheduleDates {
		if err = db.
			Clauses(clause.Locking{Strength: "UPDATE"}).
			Where("id = ?", jobScheduleDate.Id).
			Find(&model.JobScheduleDate{}).Error; xgorm.IsSqlErr(err) {
			tx.Rollback()
			logger.Errorf("fail to lock job schedule date: %v", err)
			return ctx, err
		}

		if jobScheduleDate.Status != model.JobScheduleDateStatusPending {
			logger.Infof("job schedule date status is not pending: %d", jobScheduleDate.Id)
			continue
		}

		// 發佈工作
		var newJobId uint64
		newJobId, err = publishJobByTemplate(tx, jobScheduleDate, logger)
		if err != nil {
			tx.Rollback()
			logger.Errorf("fail to publish job: %v", err)
			return ctx, err
		}
		jobScheduleDate.Status = model.JobScheduleDateStatusPublished
		jobScheduleDate.JobId = newJobId
		if err = tx.Save(&jobScheduleDate).Error; err != nil {
			tx.Rollback()
			logger.Errorf("fail to save job schedule date: %v", err)
			return ctx, err
		}
	}

	// 提交事務
	if err = tx.Commit().Error; err != nil {
		logger.Errorf("fail to commit transaction: %v", err)
		return ctx, err
	}

	logger.Info("job schedule publish task completed")
	return ctx, nil
}

// 發佈工作
func publishJobByTemplate(tx *gorm.DB, jobScheduleDate model.JobScheduleDate, logger *log.Entry) (uint64, error) {
	var err error
	// 獲取排程信息
	var schedule model.JobSchedule
	if err = tx.Where("id = ?", jobScheduleDate.JobScheduleId).First(&schedule).Error; err != nil {
		return 0, fmt.Errorf("can not find job schedule: %v", err)
	}

	// 獲取Job模板
	var job model.Job
	if err = tx.Where("job_schedule_id = ?", schedule.Id).
		Where("schedule_template = ?", model.JobScheduleTemplateY).
		First(&job).Error; err != nil {
		return 0, fmt.Errorf("can not find job: %v", err)
	}
	var jobShiftItems []model.JobShift
	if err = tx.
		Where("facility_id = ?", job.FacilityId).
		Where("job_id = ?", job.Id).
		Find(&jobShiftItems).Error; err != nil {
		return 0, fmt.Errorf("can not find job shift items: %v", err)
	}
	var serviceLocation model.ServiceLocation
	if err = tx.
		Where("facility_id = ?", job.FacilityId).
		Where("id = ?", job.ServiceLocationId).
		First(&serviceLocation).Error; err != nil {
		return 0, fmt.Errorf("can not find service location: %v", err)
	}
	tz, err := time.LoadLocation(serviceLocation.Timezone)
	if err != nil {
		return 0, fmt.Errorf("can not load timezone: %v", err)
	}

	var req services.JobCreateReq

	_ = copier.Copy(&req, job)
	req.JobScheduleId = jobScheduleDate.JobScheduleId
	req.ScheduleTemplate = model.JobScheduleTemplateN
	req.SplitType = model.JobSplitTypeNo
	req.Draft = "N"
	req.CreatedUserId = 0
	req.UpdatedUserId = 0
	req.JobShiftItems = make([]services.JobShiftItem, len(jobShiftItems))
	req.PublishNow = "Y"
	for i, item := range jobShiftItems {
		var beginTime time.Time
		var endTime time.Time

		beginTimeStr := fmt.Sprintf("%s %s:00", jobScheduleDate.Date, item.BeginTime.In(tz).Format(xtool.TimeHourG1))
		endTimeStr := fmt.Sprintf("%s %s:00", jobScheduleDate.Date, item.EndTime.In(tz).Format(xtool.TimeHourG1))

		beginTime, err = time.ParseInLocation(xtool.DateTimeSecA1, beginTimeStr, tz)
		if err != nil {
			return 0, fmt.Errorf("can not parse begin time: %v", err)
		}
		endTime, err = time.ParseInLocation(xtool.DateTimeSecA1, endTimeStr, tz)
		if err != nil {
			return 0, fmt.Errorf("can not parse end time: %v", err)
		}

		if beginTime.After(endTime) {
			endTime = time.Date(beginTime.Year(), beginTime.Month(), beginTime.Day()+1, endTime.Hour(), endTime.Minute(), endTime.Second(), 0, tz)
		}

		req.JobShiftItems[i] = services.JobShiftItem{
			BeginTime:     &beginTime,
			EndTime:       &endTime,
			Duration:      item.Duration,
			BreakDuration: item.BreakDuration,
			PayHours:      item.PayHours,
			ShiftPeriod:   item.ShiftPeriod,
			HourlyRate:    item.HourlyRate,
		}
	}
	beginTimeNullStr, endTimeNullStr := services.JobService.GetShiftTimeRange(req.JobShiftItems)
	if beginTimeNullStr == nil || endTimeNullStr == nil {
		return 0, fmt.Errorf("can not get shift time range")
	}
	jobBeginTimeStr := beginTimeNullStr.In(tz).Format(xtool.DateDayA)
	jobEndTimeStr := endTimeNullStr.In(tz).Format(xtool.DateDayA)
	// 檢查工作時間是否在協議時間範圍內
	canPublish, msg, err := services.FacilityAgreementService.CheckJobTimeInRange(tx, job.FacilityId, jobBeginTimeStr, jobEndTimeStr)
	if err != nil {
		return 0, fmt.Errorf("can not check job time in range: %v", err)
	}
	if !canPublish {
		return 0, fmt.Errorf("can not publish job: %v", msg)
	}

	// 開始時間-當前時間大於1小時才發佈
	nowTime := time.Now().In(tz)
	beginTime := beginTimeNullStr.In(tz)
	diffHours := beginTime.Sub(nowTime).Hours()
	if diffHours <= 0 {
		return 0, fmt.Errorf("can not publish job: %v", err)
	}

	resp, err := services.JobService.Create(tx, req)
	if err != nil {
		return 0, fmt.Errorf("can not create job: %v", err)
	}
	if len(resp.JobIds) == 0 {
		return 0, fmt.Errorf("can not create job")
	}
	jobId := resp.JobIds[0]

	logger.Infof("success to update job status to publish: jobId = %d", jobId)
	return jobId, nil
}
