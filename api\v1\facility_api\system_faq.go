package facility_api

import (
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type FacilityFaqController struct{}

func NewFacilityFaqController() FacilityFaqController {
	return FacilityFaqController{}
}

// @Tags Faq
// @Summary 搜索常見問題
// @Description
// @Router /v1/facility/faqs/actions/search [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FaqSearchReqByUser true "parameter"
// @Success 200 {object} []services.FaqSearchResp "Success"
func (con FacilityFaqController) Search(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FaqSearchReqByUser
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.Category = model.FaqCategoryFacility
		sReq := services.FaqSearchReq{}
		_ = copier.Copy(&sReq, req)
		resp, err := services.FaqService.Search(db, sReq, model.UserUserTypeFacilityUser)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Faq
// @Summary 查询常見問題
// @Description
// @Router /v1/facility/faqs/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FaqInquireReq true "parameter"
// @Success 200 {object} services.FaqInquireResp "Success"
func (con FacilityFaqController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FaqInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.Category = model.FaqCategoryFacility

		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FaqService.CheckIdExist(db, &model.Faq{}, req.FaqId, req.Category)
			})
		if nc.BadRequestResponseIfCheckerFailed(checker, req) {
			return
		}
		resp, err := services.FaqService.Inquire(db, req)
		if xgorm.IsNotFoundErr(err) {
			nc.BadRequestResponse(err)
			return
		}
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
