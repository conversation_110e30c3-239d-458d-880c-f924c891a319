package services

import (
	"bytes"
	"fmt"
	"os"
	"strings"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xreport"
	"github.com/Norray/xrocket/xtool"
	"github.com/jinzhu/now"
	"github.com/jung-kurt/gofpdf"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

var documentCommonRowConfig = xreport.PdfCellConfig{
	Type:          xreport.PdfCellTypeText,
	FontStyle:     xreport.FontRegular,
	FontSize:      8,
	TextRowHeight: 4,
	FontColor:     &xreport.PdfColorBlack,
	Align:         gofpdf.AlignLeft,
}

var documentCommonRowGrayConfig = xreport.PdfCellConfig{
	Type:          xreport.PdfCellTypeText,
	FontStyle:     xreport.FontRegular,
	FontSize:      8,
	TextRowHeight: 4,
	FontColor:     &xreport.PdfColor{R: 76, G: 83, B: 98},
	Align:         gofpdf.AlignLeft,
}

var documentCommonRowTipsConfig = xreport.PdfCellConfig{
	Type:          xreport.PdfCellTypeText,
	FontStyle:     xreport.FontRegular,
	FontSize:      8,
	TextRowHeight: 4,
	FontColor:     &xreport.PdfColor{R: 187, G: 187, B: 187},
	Align:         gofpdf.AlignLeft,
}

var documentCommonBoldRowConfig = xreport.PdfCellConfig{
	Type:          xreport.PdfCellTypeText,
	FontStyle:     xreport.FontBold,
	FontSize:      8,
	TextRowHeight: 5,
	FontColor:     &xreport.PdfColorBlack,
	Align:         gofpdf.AlignLeft,
}

type DocumentCommonRow struct {
	Content string `p_xreport:"width_percent=1"`
	xreport.PdfReportBaseModel
}

func (v DocumentCommonRow) PRowMargin(*gofpdf.Fpdf) xreport.PdfRowMargin {
	return xreport.PdfRowMargin{ColumnSpace: 2, Top: 1, Left: 1, Right: 1}
}

type DocumentCommonRowGray struct {
	Content string `p_xreport:"width_percent=1"`
	xreport.PdfReportBaseModel
}

func (v DocumentCommonRowGray) PRowMargin(*gofpdf.Fpdf) xreport.PdfRowMargin {
	return xreport.PdfRowMargin{ColumnSpace: 2, Top: 1, Left: 1, Right: 1}
}

type DocumentCommonRowTips struct {
	Content string `p_xreport:"width_percent=1"`
	xreport.PdfReportBaseModel
}

func (v DocumentCommonRowTips) PRowMargin(*gofpdf.Fpdf) xreport.PdfRowMargin {
	return xreport.PdfRowMargin{ColumnSpace: 2, Top: 10, Left: 1, Right: 1}
}

type DocumentCommonBoldRow struct {
	Content string `p_xreport:"width_percent=1"`
	xreport.PdfReportBaseModel
}

func (v DocumentCommonBoldRow) PRowMargin(*gofpdf.Fpdf) xreport.PdfRowMargin {
	return xreport.PdfRowMargin{ColumnSpace: 2, Top: 2, Left: 1, Right: 1}
}

// 新建pdf報表
func NewPdfReport(orientation string) (*xreport.PdfReport, error) {
	report := xreport.NewPdfReport(orientation, xreport.DefaultUnitStr, xreport.DefaultSizeStrA4, "resource/font/")
	report.AddUTF8Font(xreport.FontBold, "Montserrat-Bold.ttf")
	report.AddUTF8Font(xreport.FontRegular, "Montserrat-Regular.ttf")
	return report, nil
}

type DocumentPDFReq struct {
	DocumentId  uint64 `form:"documentId" binding:"required"`
	Disposition string `form:"disposition" binding:"omitempty,oneof=attachment inline"` // 附件顯示方式 attachment:附件 inline:內聯
}

type DocumentInvoicePDFTitle struct {
	No          string `p_xreport:"width_percent=0.05"`
	ShiftDate   string `p_xreport:"width_percent=0.11"`
	Particulars string `p_xreport:"width_percent=0.35"`
	Amount      string `p_xreport:"width_percent=0.18"`
	TaxAmount   string `p_xreport:"width_percent=0.13"`
	TotalAmount string `p_xreport:"width_percent=0.18"`
	xreport.PdfReportBaseModel
}

func (v DocumentInvoicePDFTitle) PRowMargin(*gofpdf.Fpdf) xreport.PdfRowMargin {
	return xreport.PdfRowMargin{ColumnSpace: 2, BackgroundColor: &xreport.PdfColorGray, Left: 1, Right: 1}
}

type DocumentInvoicePDFItem struct {
	No          string          `p_xreport:"width_percent=0.05"`
	ShiftDate   string          `p_xreport:"width_percent=0.11"`
	Particulars string          `p_xreport:"width_percent=0.35"`
	Amount      decimal.Decimal `p_xreport:"width_percent=0.18"`
	TaxAmount   decimal.Decimal `p_xreport:"width_percent=0.13"`
	TotalAmount decimal.Decimal `p_xreport:"width_percent=0.18"`
	xreport.PdfReportBaseModel
}

func (v DocumentInvoicePDFItem) PRowMargin(*gofpdf.Fpdf) xreport.PdfRowMargin {
	return xreport.PdfRowMargin{ColumnSpace: 2, Top: 2, Bottom: 2, Left: 1, Right: 1}
}

type DocumentInvoicePDFSummary struct {
	Title       string          `p_xreport:"width_percent=0.51"`
	TotalAmount decimal.Decimal `p_xreport:"width_percent=0.18"`
	TaxAmount   decimal.Decimal `p_xreport:"width_percent=0.13"`
	GrandTotal  decimal.Decimal `p_xreport:"width_percent=0.18"`
	xreport.PdfReportBaseModel
}

func (v DocumentInvoicePDFSummary) PRowMargin(*gofpdf.Fpdf) xreport.PdfRowMargin {
	return xreport.PdfRowMargin{ColumnSpace: 2, BackgroundColor: &xreport.PdfColorGray, Left: 1, Right: 1}
}

// 總賬賬項分析 - 生成PDF
func (s *documentService) DocumentPDF(db *gorm.DB, req DocumentPDFReq) (*bytes.Reader, error) {
	var content *bytes.Reader
	var err error

	// 獲取數據
	var document model.Document
	if err = db.Where("id = ?", req.DocumentId).First(&document).Error; err != nil {
		return content, err
	}
	var documentItems []model.DocumentItem
	if err = db.Where("document_id = ?", req.DocumentId).Order("seq").Find(&documentItems).Error; err != nil {
		return content, err
	}

	var report *xreport.PdfReport
	report, err = NewPdfReport(xreport.POrientationStr)
	if err != nil {
		return content, err
	}
	report.File.SetTitle(document.DocumentNo.String, true)
	report.PageIndexName = "Page:"
	if document.DataType == model.DocumentDataTypeProfessionalToFacility || document.DataType == model.DocumentDataTypeProfessionalToSystem {
		// 專業人士發出的發票不需要 medic crew的logo
		// 添加 文件頭部
		AddDocumentPdfSecondHeader(report, "Invoice", 4)
	} else {
		// 讀取本地文件到 []byte
		logoData, err := os.ReadFile("./resource/pdf/logo.jpg")
		if err != nil {
			return content, err
		}
		// 添加 固定頭部
		AddDocumentPdfHeader(report, "Medic Crew", logoData, 12)
		// 添加 文件頭部
		AddDocumentPdfSecondHeader(report, "Invoice", 4)
	}
	// 添加發票專屬頭部
	AddInvoiceHeader(report, document)

	report.AddCellConfig(DocumentInvoicePDFTitle{}, xreport.ReportPdfConfig.TableTitleLeftConfig, "ShiftDate", "Particulars")
	report.AddCellConfig(DocumentInvoicePDFTitle{}, xreport.ReportPdfConfig.TableTitleCenterConfig, "No")
	report.AddCellConfig(DocumentInvoicePDFTitle{}, xreport.ReportPdfConfig.TableTitleRightConfig, "Amount", "TaxAmount", "TotalAmount")

	report.AddCellConfig(DocumentInvoicePDFItem{}, xreport.ReportPdfConfig.TableContentLeftConfig, "ShiftDate", "Particulars")
	report.AddCellConfig(DocumentInvoicePDFItem{}, xreport.ReportPdfConfig.TableContentCenterConfig, "No")
	amountConfig := xreport.ReportPdfConfig.TableContentRightDecimal11Config
	amountConfig.CurrencySymbol = "$"
	report.AddCellConfig(DocumentInvoicePDFItem{}, amountConfig, "Amount", "TaxAmount", "TotalAmount")

	report.AddCellConfig(DocumentInvoicePDFSummary{}, xreport.ReportPdfConfig.TableSummaryRightConfig, "Title")
	summaryAmountConfig := xreport.ReportPdfConfig.TableSummaryRightDecimal11Config
	summaryAmountConfig.CurrencySymbol = "$"
	report.AddCellConfig(DocumentInvoicePDFSummary{}, summaryAmountConfig, "TotalAmount", "GrandTotal", "TaxAmount")

	report.AddCellConfig(DocumentCommonRow{}, documentCommonRowConfig, "Content")
	report.AddCellConfig(DocumentCommonRowGray{}, documentCommonRowGrayConfig, "Content")
	report.AddCellConfig(DocumentCommonRowTips{}, documentCommonRowTipsConfig, "Content")
	report.AddCellConfig(DocumentCommonBoldRow{}, documentCommonBoldRowConfig, "Content")

	tableHeader, err := report.ParseCellByModel(DocumentInvoicePDFTitle{
		No:          "No.",
		ShiftDate:   "Shift Date",
		Particulars: "Item (Particulars)",
		Amount:      "Amount (excl. GST)",
		TaxAmount:   "GST (10%)",
		TotalAmount: "Amount (incl. GST)",
	})
	if err != nil {
		return content, err
	}
	report.AddHeaderRow(tableHeader)

	rows := s.genItemsRows(documentItems)
	for _, rowData := range rows {
		report.AddRow(rowData.(xreport.PdfRowData))
	}

	report.AddRow(DocumentInvoicePDFSummary{Title: "Total", TotalAmount: document.TotalAmount, TaxAmount: document.TaxAmount, GrandTotal: document.GrandTotal})

	if document.Remark != "" {
		report.AddRow(DocumentCommonBoldRow{Content: "Salary Notes:"})
		report.AddRow(DocumentCommonRowGray{Content: document.Remark})
	}
	if document.OtherRemark != "" {
		report.AddRow(DocumentCommonBoldRow{Content: "Expense Reimbursement Notes:"})
		report.AddRow(DocumentCommonRowGray{Content: document.OtherRemark})
	}
	report.AddRow(DocumentCommonBoldRow{Content: "Electronic Funds Transfer to"})
	accountInfoArr := []string{
		"Account Name: " + document.FromBankAccountName,
		"BSB: " + document.FromBankStateBranch,
		"Account Number: " + document.FromBankAccountNumber,
	}
	report.AddRow(DocumentCommonRow{Content: strings.Join(accountInfoArr, "\n")})
	report.AddRow(DocumentCommonRowGray{Content: "The total amount is inclusive of any applicable GST. Payment is due 7 days from receipt of this invoice."})
	if document.DataType == model.DocumentDataTypeProfessionalToSystem {
		report.AddRow(DocumentCommonRowGray{Content: "Special case: If the invoice includes overtime pay or expense reimbursement, the relevant amounts will be collected from the organisation by Medic Crew before being paid out."})
	}
	if document.DataType == model.DocumentDataTypeProfessionalToFacility || document.DataType == model.DocumentDataTypeProfessionalToSystem {
		report.AddRow(DocumentCommonRowTips{Content: "This invoice has been generated and issued by Medic Crew on behalf of the healthcare professional named above. Medic Crew is not the supplier of the services and does not verify or guarantee the accuracy of the invoice details, which are provided solely by the professional."})
	}
	// 生成
	err = report.Generate()
	if err != nil {
		return content, err
	}
	content, err = report.GetFileContent()
	if err != nil {
		return content, err
	}
	return content, nil
}

func (s *documentService) genItemsRows(items []model.DocumentItem) []interface{} {
	var rows []interface{}
	for i, item := range items {
		var shiftDate string
		if item.ItemDate.String() != "" {
			shiftDate = now.MustParse(item.ItemDate.String()).Format(xtool.DateDayC)
		} else {
			shiftDate = "-"
		}
		particular := item.Particular
		if item.ItemType == model.DocumentItemTypeCustom && item.ItemName != "" {
			particular = fmt.Sprintf("%s (%s)", item.ItemName, item.Particular)
		}
		rows = append(rows, DocumentInvoicePDFItem{
			No:          fmt.Sprintf("%d", i+1),
			ShiftDate:   shiftDate,
			Particulars: particular,
			Amount:      item.TotalAmount,
			TaxAmount:   item.TaxAmount,
			TotalAmount: item.TotalAmount.Add(item.TaxAmount),
		})
	}
	return rows
}

func AddDocumentPdfHeader(report *xreport.PdfReport, companyName string, logoData []byte, lastLnOffset float64) {
	report.AddHeader(xreport.PdfCell{
		Type:         xreport.PdfCellTypeText,
		WidthPercent: 0.30,
		StrValue:     " ",
	}, false)
	report.AddHeader(xreport.PdfCell{
		Type:         xreport.PdfCellTypeImage,
		WidthPercent: 0.1,
		ImageData:    logoData,
		ImagePadding: xreport.PdfCellPadding{
			//Left: 10,
		},
	}, false)
	report.AddHeader(xreport.PdfCell{
		Type:          xreport.PdfCellTypeText,
		WidthPercent:  0.24,
		TextMaxRow:    1,
		TextRowHeight: 19,
		StrValue:      companyName,
		FontStyle:     xreport.FontRegular,
		FontSize:      20,
		FontColor:     &xreport.PdfColorBlack,
		Align:         gofpdf.AlignCenter,
		LnOffset:      lastLnOffset,
	}, false)
}

func AddDocumentPdfSecondHeader(report *xreport.PdfReport, documentName string, lastLnOffset float64) {
	report.AddHeader(xreport.PdfCell{
		Type:         xreport.PdfCellTypeText,
		WidthPercent: 0.7,
		TextMaxRow:   1,
		StrValue:     documentName,
		FontStyle:    xreport.FontRegular,
		FontSize:     16,
		FontColor:    &xreport.PdfColorBlack,
		Align:        gofpdf.AlignLeft,
		LnOffset:     lastLnOffset,
	}, true)
}

func AddInvoiceHeader(report *xreport.PdfReport, document model.Document) {
	AddThirdHeader(report, thirdHeaderTextLeft, fmt.Sprintf("To   %s", document.ToName), 1, true, 0)
	AddThirdHeader(report, thirdHeaderTextRight, fmt.Sprintf("Invoice No.: %s", document.DocumentNo.String), 1, false, 3)
	AddThirdHeader(report, thirdHeaderTextLeft, fmt.Sprintf("ABN: %s", document.ToAbn), 1, true, 0)
	if document.DocumentDate.String() != "" {
		documentDate := now.MustParse(document.DocumentDate.String()).Format(xtool.DateDayC)
		AddThirdHeader(report, thirdHeaderTextRight, fmt.Sprintf("Invoice Date: %s", documentDate), 1, false, 3)
	}
	AddThirdHeader(report, thirdHeaderTextLeft, document.ToAddress, 3, true, 0)
	if document.DueDate.Valid {
		dueDate := now.MustParse(document.DueDate.String()).Format(xtool.DateDayC)
		AddThirdHeader(report, thirdHeaderTextRight, fmt.Sprintf("Due Date: %s", dueDate), 1, false, 6)
	}
}

const (
	thirdHeaderTextLeft  = "LEFT"
	thirdHeaderTextRight = "RIGHT"
)

func AddThirdHeader(report *xreport.PdfReport, leftOrRight, strValue string, textMaxRow int, newRow bool, lastLnOffset float64) {
	if leftOrRight == thirdHeaderTextLeft {
		report.AddHeader(xreport.PdfCell{
			Type:          xreport.PdfCellTypeText,
			WidthPercent:  0.6,
			TextMaxRow:    textMaxRow,
			StrValue:      strValue,
			FontStyle:     xreport.FontRegular,
			FontSize:      10,
			TextRowHeight: report.GetFontHeight(12),
			FontColor:     &xreport.PdfColorBlack,
			Align:         gofpdf.AlignLeft,
			LnOffset:      lastLnOffset,
		}, newRow)
	} else {
		report.AddHeader(xreport.PdfCell{
			Type:          xreport.PdfCellTypeText,
			WidthPercent:  0.4,
			TextMaxRow:    textMaxRow,
			StrValue:      strValue,
			FontStyle:     xreport.FontRegular,
			FontSize:      10,
			TextRowHeight: report.GetFontHeight(12),
			FontColor:     &xreport.PdfColorBlack,
			Align:         gofpdf.AlignRight,
			LnOffset:      lastLnOffset,
		}, newRow)
	}
}
