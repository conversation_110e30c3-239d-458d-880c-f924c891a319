package facility_api

import (
	"strings"

	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type FacilityProfileController struct {
	v1.CommonController
}

func NewFacilityProfileController() FacilityProfileController {
	return FacilityProfileController{}
}

// @Tags Facility Profile
// @Summary 初始化機構資料
// @Description
// @Router /v1/facility/facility-profiles/actions/init [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FacilityProfileInitReq true "parameter"
func (con FacilityProfileController) Init(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityProfileInitReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		// 檢查權限 主賬號才能 init
		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityProfileId: req.FacilityProfileId, FacilityPrimaryUser: model.FacilityUserPrimaryUserY}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityProfileService.CheckAlreadyInit(db, &model.FacilityProfile{}, req.FacilityProfileId)
			}).
			Run(func() (bool, i18n.Message, error) {
				// Check Facility Type
				return services.SelectionService.CheckSelectionExist(db, &model.Selection{}, model.SelectionTypeFacilityProfileFacilityType, req.FacilityType, model.SelectionStatusEnable)
			}).
			Run(func() (bool, i18n.Message, error) {
				// Check Expertise Required
				expertiseRequiredArr := strings.Split(req.ExpertiseRequired, ",")
				expertiseRequiredArr = xtool.StringArrayDeduplication(expertiseRequiredArr)
				req.ExpertiseRequired = strings.Join(expertiseRequiredArr, ",")
				return services.SelectionService.CheckSelectionsExist(db, model.SelectionTypeProfessionalProfession, req.ExpertiseRequired, model.SelectionStatusEnable)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.FacilityProfileService.Init(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)

	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility Profile
// @Summary 查询機構資料
// @Description
// @Router /v1/facility/facility-profiles/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilityProfileInquireReq true "parameter"
// @Success 200 {object} services.FacilityProfileInquireResp "Success"
func (con FacilityProfileController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityProfileInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityProfileId: req.FacilityProfileId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.FacilityProfileService.Inquire(db, req)
		if xgorm.IsNotFoundErr(err) {
			nc.BadRequestResponse(err)
			return
		}
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility Profile
// @Summary 修改機構資料
// @Description
// @Router /v1/facility/facility-profiles/actions/edit [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FacilityProfileEditReq true "parameter"
// @Success 200 "Success"
func (con FacilityProfileController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityProfileEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityProfileId: req.FacilityProfileId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var checkMsg []string
		var facilityProfile model.FacilityProfile
		var files []model.FacilityFile
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityProfileService.CheckIdExist(db, &facilityProfile, req.FacilityProfileId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityProfileService.CheckCanEdit(facilityProfile)
			}).
			Run(func() (bool, i18n.Message, error) {
				// Check Facility Type
				return services.SelectionService.CheckSelectionExist(db, &model.Selection{}, model.SelectionTypeFacilityProfileFacilityType, req.FacilityType, "") // 編輯不限制 status
			}).
			Run(func() (bool, i18n.Message, error) {
				// Check Expertise Required
				expertiseRequiredArr := strings.Split(req.ExpertiseRequired, ",")
				expertiseRequiredArr = xtool.StringArrayDeduplication(expertiseRequiredArr)
				req.ExpertiseRequired = strings.Join(expertiseRequiredArr, ",")
				return services.SelectionService.CheckSelectionsExist(db, model.SelectionTypeProfessionalProfession, req.ExpertiseRequired, "") // 編輯不限制 status
			}).
			Run(func() (bool, i18n.Message, error) {
				// Check Facility Type
				req.Files = xtool.Uint64ArrayDeduplication(req.Files)
				return services.FacilityFileService.CheckFileExist(db, &files, facilityProfile.FacilityId, model.FacilityFileCodes, req.Files)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		err = services.FacilityProfileService.Edit(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility Profile
// @Summary 提交機構資料
// @Description
// @Router /v1/facility/facility-profiles/actions/submit [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FacilityProfileSubmitReq true "parameter"
// @Success 200 "Success"
func (con FacilityProfileController) Submit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityProfileSubmitReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityProfileId: req.FacilityProfileId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var checkMsg []string
		var facilityProfile model.FacilityProfile
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityProfileService.CheckIdExist(db, &facilityProfile, req.FacilityProfileId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityProfileService.CheckCanSubmitOrUnSubmit(facilityProfile, req.Submit)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		// 提示訊息
		var alertMsg []string
		alerter := xapp.NewCK(c)
		alerter.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityProfileService.AlertInformationCompleteBeforeSubmit(db, facilityProfile, req.Submit)
			})
		alertMsg, err = alerter.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}

		tx := db.Begin()
		err = services.FacilityProfileService.Submit(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()

		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}
