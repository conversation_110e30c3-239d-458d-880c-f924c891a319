package services

import (
	"fmt"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

var AgreementService = new(agreementService)

type agreementService struct{}

// region ---------------------------------------------------- Checker ----------------------------------------------------

// 檢查協議ID是否存在
func (s *agreementService) CheckIdExist(db *gorm.DB, m *model.Agreement, id uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.agreement.id.does_not_exist",
		Other: "No such record, please try after reloading.",
	}
	var err error
	if err = db.First(&m, id).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

// endregion ---------------------------------------------------- Checker ----------------------------------------------------

// region ---------------------------------------------------- List ----------------------------------------------------

type AgreementListReq struct {
	Title   string `form:"title"`                                           // 標題
	Content string `form:"content"`                                         // 內容
	Status  string `form:"status" binding:"omitempty,oneof=ENABLE DISABLE"` // 狀態 ENABLE=啟用, DISABLE=禁用
}

type AgreementListResp struct {
	AgreementId uint64 `json:"agreementId"` // 協議ID
	Title       string `json:"title"`       // 標題
	Content     string `json:"content"`     // 內容
	Status      string `json:"status"`      // 狀態 ENABLE=啟用, DISABLE=禁用
}

func (s *agreementService) List(db *gorm.DB, req AgreementListReq, pageSet *xresp.PageSet) ([]AgreementListResp, error) {
	var err error
	var resp []AgreementListResp
	builder := db.Table("agreement AS a").Select([]string{
		"a.id AS agreement_id",
		"a.title",
		"a.content_text AS content",
		"a.status",
	})
	if req.Title != "" {
		builder = builder.Where("a.title LIKE ?", "%"+req.Title+"%")
	}
	if req.Content != "" {
		builder = builder.Where("a.content LIKE ?", "%"+req.Content+"%")
	}
	if req.Status != "" {
		builder = builder.Where("a.status = ?", req.Status)
	}
	if err = builder.
		Group("a.id DESC").
		Scopes(xresp.Paginate(pageSet)).Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

// endregion ---------------------------------------------------- List ----------------------------------------------------

// region ---------------------------------------------------- Search ----------------------------------------------------

type AgreementSearchReq struct {
	Title      string `form:"title" binding:"omitempty,max=255"`               // 標題
	Content    string `form:"content" binding:"omitempty,max=255"`             // 內容
	Status     string `form:"status" binding:"omitempty,oneof=ENABLE DISABLE"` // 狀態 ENABLE=啟用 DISABLE=禁用
	SelectedId uint64 `form:"selectedId"`                                      // 選中ID
	Limit      int    `form:"limit"`                                           // 限制
}

type AgreementSearchResp struct {
	AgreementId uint64 `json:"agreementId"` // 協議ID
	Title       string `json:"title"`       // 標題
	Content     string `json:"content"`     // 內容
	Status      string `json:"status"`      // 狀態 ENABLE=啟用 DISABLE=禁用
}

func (s *agreementService) Search(db *gorm.DB, req AgreementSearchReq) ([]AgreementSearchResp, error) {
	var err error
	var resp []AgreementSearchResp
	builder := db.Table("agreement AS a").
		Select([]string{
			"a.id AS agreement_id",
			"a.title",
			"a.content_text AS content",
			"a.status",
		})
	if req.Title != "" {
		builder = builder.Where("a.title LIKE ?", "%"+req.Title+"%")
	}
	if req.Content != "" {
		builder = builder.Where("a.content LIKE ?", "%"+req.Content+"%")
	}
	if req.Status != "" {
		builder = builder.Where("a.status = ?", req.Status)
	}
	if req.SelectedId != 0 {
		builder = builder.Order(fmt.Sprintf("IF(a.id = %d,0,1)", req.SelectedId))
	}
	if req.Limit > 0 {
		builder = builder.Limit(req.Limit)
	}
	if err = builder.
		Group("a.id").
		Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

// endregion ---------------------------------------------------- Search ----------------------------------------------------

// region ---------------------------------------------------- Create ----------------------------------------------------

type AgreementCreateReq struct {
	Title       string `json:"title" binding:"required,max=255"`        // 標題
	Content     string `json:"content" binding:"required"`              // 內容
	ContentText string `json:"contentText" binding:"omitempty,max=255"` // 內容
}

type AgreementCreateResp struct {
	AgreementId uint64 `json:"agreementId"`
}

func (s *agreementService) Create(db *gorm.DB, req AgreementCreateReq) (AgreementCreateResp, error) {
	var resp AgreementCreateResp
	var err error
	m := model.Agreement{
		Title:       req.Title,
		Content:     req.Content,
		ContentText: req.ContentText,
		Status:      model.AgreementStatusEnable,
		UpdateTime:  time.Now().UTC(),
	}
	if err = db.Create(&m).Error; err != nil {
		return resp, err
	}
	resp.AgreementId = m.Id
	return resp, nil
}

// endregion ---------------------------------------------------- Create ----------------------------------------------------

// region ---------------------------------------------------- Edit ----------------------------------------------------

type AgreementEditReq struct {
	AgreementId uint64 `json:"agreementId"  binding:"required"`         // 協議ID
	Title       string `json:"title" binding:"required,max=255"`        // 標題
	Content     string `json:"content" binding:"required"`              // 內容
	ContentText string `json:"contentText" binding:"omitempty,max=255"` // 內容
}

func (s *agreementService) Edit(db *gorm.DB, req AgreementEditReq) error {
	var err error
	var m model.Agreement
	if err = db.First(&m, req.AgreementId).Error; err != nil {
		return err
	}
	m.UpdateTime = time.Now().UTC()
	_ = copier.Copy(&m, req)
	if err = db.Save(&m).Error; err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- Edit ----------------------------------------------------

// region ---------------------------------------------------- Inquire ----------------------------------------------------

type AgreementInquireReq struct {
	AgreementId uint64 `form:"agreementId" binding:"required"` // 協議ID
}

type AgreementInquireResp struct {
	AgreementId uint64 `json:"agreementId"` // 協議ID
	Title       string `json:"title"`       // 標題
	Content     string `json:"content"`     // 內容
	ContentText string `json:"contentText"` // 內容純文字
	Status      string `json:"status"`      // 狀態 ENABLE=啟用, DISABLE=禁用
}

func (s *agreementService) Inquire(db *gorm.DB, req AgreementInquireReq) (AgreementInquireResp, error) {
	var err error
	var resp AgreementInquireResp
	var m model.Agreement
	if err = db.First(&m, req.AgreementId).Error; err != nil {
		return resp, err
	}
	_ = copier.Copy(&resp, m)
	resp.AgreementId = m.Id
	return resp, nil
}

// endregion ---------------------------------------------------- Inquire ----------------------------------------------------

// region ---------------------------------------------------- Delete ----------------------------------------------------

type AgreementDeleteReq struct {
	AgreementId uint64 `json:"agreementId"  binding:"required"` // 協議ID
}

func (s *agreementService) Delete(db *gorm.DB, req AgreementDeleteReq, agreement model.Agreement) error {
	var err error
	if err = db.Delete(&model.Agreement{}, req.AgreementId).Error; err != nil {
		return err
	}
	return nil
}

// endregion ---------------------------------------------------- Delete ----------------------------------------------------

// region ---------------------------------------------------- 用戶 ----------------------------------------------------

// 無可用協議時，返回空協議
func (s *agreementService) GetCurrentAgreement(db *gorm.DB) (model.Agreement, error) {
	var err error
	var agreement model.Agreement
	if err = db.Model(&model.Agreement{}).
		Where("status = ?", model.AgreementStatusEnable).
		First(&agreement).Error; err != nil {
		return agreement, err
	}
	return agreement, nil
}

// 機構獲取協議
type AgreementInquireByFacilityProfileReq struct {
	FacilityId          uint64 `form:"facilityId" binding:"required"`          // 機構 ID
	FacilityAgreementId uint64 `form:"facilityAgreementId" binding:"required"` // 機構協議 ID
}

type AgreementInquireByFacilityProfileResp struct {
	FacilityAgreementId        uint64          `json:"facilityAgreementId"`        // 機構協議ID
	FacilityFileId             uint64          `json:"facilityFileId"`             // 機構協議ID
	Content                    string          `json:"content"`                    // 內容
	PayUpfrontCommissionRate   decimal.Decimal `json:"payUpfrontCommissionRate"`   // 預付款佣金率
	PayInArrearsCommissionRate decimal.Decimal `json:"payInArrearsCommissionRate"` // 事後支付佣金率
	BeginTime                  string          `json:"beginTime"`                  // 生效時間
	EndTime                    string          `json:"endTime"`                    // 失效時間
	Status                     string          `json:"status"`                     // 狀態
}

// 機構獲取協議
func (s *agreementService) GetFacilityAgreement(db *gorm.DB, req AgreementInquireByFacilityProfileReq) (AgreementInquireByFacilityProfileResp, error) {
	var err error
	var resp AgreementInquireByFacilityProfileResp
	var facilityAgreement model.FacilityAgreement
	if err = db.Model(&model.FacilityAgreement{}).
		Where("facility_id = ?", req.FacilityId).
		First(&facilityAgreement, req.FacilityAgreementId).Error; err != nil {
		return resp, err
	}
	resp.FacilityAgreementId = facilityAgreement.Id
	resp.FacilityFileId = facilityAgreement.FacilityFileId
	resp.Content = facilityAgreement.Content
	resp.BeginTime = facilityAgreement.BeginTime.String()
	resp.EndTime = facilityAgreement.EndTime.String()
	resp.Status = facilityAgreement.Status

	var payUpfrontCommission model.Commission
	if err = db.First(&payUpfrontCommission, facilityAgreement.PayUpfrontCommissionId).Error; err != nil {
		return resp, err
	}
	resp.PayUpfrontCommissionRate = payUpfrontCommission.CommissionRate

	var payInArrearsCommission model.Commission
	if err = db.First(&payInArrearsCommission, facilityAgreement.PayInArrearsCommissionId).Error; err != nil {
		return resp, err
	}
	resp.PayInArrearsCommissionRate = payInArrearsCommission.CommissionRate
	return resp, nil
}

// endregion ---------------------------------------------------- Professional ----------------------------------------------------
