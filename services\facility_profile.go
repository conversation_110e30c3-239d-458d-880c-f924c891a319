package services

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"errors"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xmodel/xtype"
	"github.com/Norray/xrocket/xredis"
	"github.com/Norray/xrocket/xtool"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

var FacilityProfileService = new(facilityProfileService)

type facilityProfileService struct{}

func (s *facilityProfileService) CheckIdExist(db *gorm.DB, m *model.FacilityProfile, id uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_profile.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var err error
	if err = db.First(&m, id).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

func (s *facilityProfileService) CheckCanEdit(facilityProfile model.FacilityProfile) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_profile.can_not_edit",
		Other: "Some thing went wrong, please try again later.",
	}
	if facilityProfile.Id == 0 || facilityProfile.DataType != model.FacilityProfileDataTypeDraft || facilityProfile.Status != model.FacilityProfileStatusPending {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

func (s *facilityProfileService) CheckAgreementCorrect(db *gorm.DB, facilityAgreementId uint64, files []model.FacilityFile) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_profile.agreement_id.correct",
		Other: "Some thing went wrong, please try again later.",
	}
	// 檢查是否有簽署協議文件
	foundSignedAgreement := false
	for _, file := range files {
		if file.FileCode == model.FacilityFileCodeSignedAgreement {
			foundSignedAgreement = true
			break
		}
	}
	// 沒有簽名文件但是有協議Id
	if !foundSignedAgreement && facilityAgreementId > 0 {
		return false, msg, nil
	}
	// 有簽名文件但是沒有協議Id
	if foundSignedAgreement && facilityAgreementId == 0 {
		return false, msg, nil
	}
	if facilityAgreementId > 0 {
		// TODO 檢查機構協議是否存在
		//agreement, err := AgreementService.GetCurrentAgreement(db)
		//if err != nil {
		//	return false, msg, err
		//}
		//if agreement.Id != facilityAgreementId {
		//	return false, msg, nil
		//} else {
		return true, msg, nil
		//}
	} else {
		return true, msg, nil
	}
}

func (s *facilityProfileService) CheckCanSubmitOrUnSubmit(facilityProfile model.FacilityProfile, submit string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_profile.can_not_submit_or_un_submit",
		Other: "Some thing went wrong, please try again later.",
	}
	if facilityProfile.Id == 0 || facilityProfile.DataType != model.FacilityProfileDataTypeDraft {
		return false, msg, nil
	} else if submit == "Y" {
		if facilityProfile.Status != model.FacilityProfileStatusPending {
			return false, msg, nil
		}
	} else {
		if facilityProfile.Status != model.FacilityProfileStatusReviewing {
			return false, msg, nil
		}
	}
	return true, msg, nil
}

func (s *facilityProfileService) CheckCanApprove(facilityProfile model.FacilityProfile) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_profile.can_not_approve",
		Other: "Some thing went wrong, please try again later.",
	}
	if facilityProfile.Id == 0 || facilityProfile.DataType != model.FacilityProfileDataTypeDraft || facilityProfile.Status != model.FacilityProfileStatusReviewing {
		return false, msg, nil
	}
	return true, msg, nil
}

func (s *facilityProfileService) AlertInformationCompleteBeforeSubmit(db *gorm.DB, facilityProfile model.FacilityProfile, submit string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_profile.information_incomplete",
		Other: "Please check if all the required fields on the page have been completed.",
	}
	if submit == "Y" {
		var filesCount int64
		// 是否有簽署的機構協議
		if err := db.Table("facility_agreement AS fa").
			Where("fa.facility_id = ?", facilityProfile.FacilityId).
			Where("fa.facility_file_id > ?", 0).
			Where("fa.status in (?)", []string{model.FacilityAgreementStatusSigned, model.FacilityAgreementStatusReviewing}).
			Count(&filesCount).Error; err != nil {
			return false, msg, err
		}
		if filesCount == 0 ||
			facilityProfile.Email == "" ||
			facilityProfile.Name == "" ||
			facilityProfile.FacilityType == "" ||
			facilityProfile.Address == "" ||
			facilityProfile.Abn == "" ||
			facilityProfile.ContactFirstName == "" ||
			facilityProfile.ContactLastName == "" ||
			facilityProfile.ContactRole == "" ||
			facilityProfile.ContactPhone == "" ||
			facilityProfile.ContactEmail == "" ||
			facilityProfile.PaymentTerms == "" {
			return false, msg, nil
		}
	}
	return true, i18n.Message{}, nil
}

func (s *facilityProfileService) CheckAlreadyInit(db *gorm.DB, m *model.FacilityProfile, id uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_profile.already_init",
		Other: "Some thing went wrong, please try again later.",
	}
	var err error
	if err = db.Where("facility_type = ?", "").Where("data_type = ?", model.FacilityProfileDataTypeDraft).First(m, id).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

// 生成機構編號
func (s *facilityProfileService) GenerateFacilityNo(db *gorm.DB, facilityId uint64, facilityName string) (string, error) {
	// 獲取名稱前兩個單詞的首字母取大寫，例如：Brisbane Childcare Centre -> BC
	nameWords := strings.Fields(facilityName)
	nameNo := ""

	if len(nameWords) >= 2 {
		// 取前兩個單詞的首字母
		for i := 0; i < 2; i++ {
			if len(nameWords[i]) > 0 {
				nameNo += strings.ToUpper(string(nameWords[i][0]))
			}
		}
	} else if len(facilityName) >= 2 {
		// 如果只有一個單詞，則取前兩個字母
		nameNo = strings.ToUpper(facilityName[:2])
	} else {
		// 如果名稱太短，則返回錯誤
		return "", errors.New("facility name is too short: " + facilityName)
	}

	// 查詢相同前綴的最大編號
	var maxNo string
	if err := db.Model(&model.FacilityProfile{}).
		Select("no").
		Where("no LIKE ?", nameNo+"%").
		Where("data_type = ?", model.FacilityProfileDataTypeApproved).
		Where("facility_id <> ?", facilityId).
		Order("no DESC").
		First(&maxNo).Error; err != nil && !xgorm.IsNotFoundErr(err) {
		return "", err
	}

	num := 1
	if maxNo != "" {
		maxNumStr := strings.ReplaceAll(maxNo, nameNo, "")
		maxNum, err := strconv.Atoi(maxNumStr)
		if err != nil {
			return "", err
		}
		num = maxNum + 1
	}

	return fmt.Sprintf("%s%03d", nameNo, num), nil
}

type FacilityProfileInitReq struct {
	FacilityProfileId uint64 `json:"facilityProfileId" binding:"required"`
	// Step 1
	Name             string          `json:"name" binding:"required,max=128"`
	FacilityType     string          `json:"facilityType" binding:"required"`
	FacilityTypeName string          `json:"facilityTypeName" binding:"max=128"`
	Address          string          `json:"address" binding:"required,max=255"`
	AddressExtra     string          `json:"addressExtra" binding:"max=255"`
	LocationState    string          `json:"locationState" binding:"required,max=128"`
	LocationCity     string          `json:"locationCity" binding:"required,max=128"`
	LocationRoute    string          `json:"locationRoute" binding:"max=128"`
	LocationLat      decimal.Decimal `json:"locationLat"`
	LocationLng      decimal.Decimal `json:"locationLng"`
	// Step 2
	Abn              string `json:"abn" binding:"required,len=11,numeric"`
	ContactFirstName string `json:"contactFirstName" binding:"required,max=255"`
	ContactLastName  string `json:"contactLastName" binding:"required,max=255"`
	ContactRole      string `json:"contactRole" binding:"required,max=128"`
	ContactPhone     string `json:"contactPhone" binding:"required,max=128"`
	ContactEmail     string `json:"contactEmail" binding:"required,email,max=128"`
	// Step 3
	ExpertiseRequired string `json:"expertiseRequired" binding:"required,max=1024"`
	PaymentTerms      string `json:"paymentTerms" binding:"required,oneof=PAY_IN_ARREARS PAY_UPFRONT"`
}

func (s *facilityProfileService) Init(db *gorm.DB, req FacilityProfileInitReq) error {
	if err := db.Model(&model.FacilityProfile{}).Where("data_type = ?", model.FacilityProfileDataTypeDraft).Where("id = ?", req.FacilityProfileId).Updates(map[string]interface{}{
		"name":               req.Name,
		"facility_type":      req.FacilityType,
		"facility_type_name": req.FacilityTypeName,
		"address":            req.Address,
		"address_extra":      req.AddressExtra,
		"location_state":     req.LocationState,
		"location_city":      req.LocationCity,
		"location_route":     req.LocationRoute,
		"location_lat":       req.LocationLat,
		"location_lng":       req.LocationLng,
		"abn":                req.Abn,
		"contact_first_name": req.ContactFirstName,
		"contact_last_name":  req.ContactLastName,
		"contact_role":       req.ContactRole,
		"contact_phone":      req.ContactPhone,
		"contact_email":      req.ContactEmail,
		"expertise_required": req.ExpertiseRequired,
		"payment_terms":      req.PaymentTerms,
	}).Error; err != nil {
		return err
	}
	return nil
}

type FacilityProfileInquireReq struct {
	FacilityProfileId uint64 `form:"facilityProfileId" binding:"required"`
}

type FacilityProfileInquireResp struct {
	FacilityProfileId                  uint64                `json:"facilityProfileId"`
	FacilityId                         uint64                `json:"facilityId"`
	DataType                           string                `json:"dataType"`
	FacilityType                       string                `json:"facilityType"`
	FacilityTypeName                   string                `json:"facilityTypeName"`
	Name                               string                `json:"name"`
	Email                              string                `json:"email"`
	Address                            string                `json:"address"`
	AddressExtra                       string                `json:"addressExtra"`
	LocationState                      string                `json:"locationState"`
	LocationCity                       string                `json:"locationCity"`
	LocationRoute                      string                `json:"locationRoute"`
	LocationLat                        decimal.Decimal       `json:"locationLat"`
	LocationLng                        decimal.Decimal       `json:"locationLng"`
	ApplicationTime                    *time.Time            `json:"applicationTime"`
	Status                             string                `json:"status"`
	ApprovedTime                       *time.Time            `json:"approvedTime"`
	RejectReason                       string                `json:"rejectReason"`
	Abn                                string                `json:"abn"`
	ContactFirstName                   string                `json:"contactFirstName"`
	ContactLastName                    string                `json:"contactLastName"`
	ContactEmail                       string                `json:"contactEmail"`
	ContactPhone                       string                `json:"contactPhone"`
	ContactRole                        string                `json:"contactRole"`
	ExpertiseRequired                  string                `json:"expertiseRequired"`
	PaymentTerms                       string                `json:"paymentTerms"`
	PublicLiabilityInsuranceExpiryDate string                `json:"publicLiabilityInsuranceExpiryDate"`
	OrientationConfirmation            string                `json:"orientationConfirmation"`
	FacilityAgreementId                uint64                `json:"facilityAgreementId"`
	Deactivated                        string                `json:"deactivated"`
	Diff                               string                `json:"diff"`
	Files                              []FacilityProfileFile `json:"files"`
}

// FacilityProfileInquireCompareResp 用於對比
type FacilityProfileInquireCompareResp struct {
	FacilityType                       string                `json:"facilityType"`
	FacilityTypeName                   string                `json:"facilityTypeName"`
	Name                               string                `json:"name"`
	Email                              string                `json:"email"`
	Address                            string                `json:"address"`
	AddressExtra                       string                `json:"addressExtra"`
	LocationState                      string                `json:"locationState"`
	LocationCity                       string                `json:"locationCity"`
	LocationRoute                      string                `json:"locationRoute"`
	LocationLat                        decimal.Decimal       `json:"locationLat"`
	LocationLng                        decimal.Decimal       `json:"locationLng"`
	RejectReason                       string                `json:"rejectReason"`
	Abn                                string                `json:"abn"`
	ContactFirstName                   string                `json:"contactFirstName"`
	ContactLastName                    string                `json:"contactLastName"`
	ContactEmail                       string                `json:"contactEmail"`
	ContactPhone                       string                `json:"contactPhone"`
	ContactRole                        string                `json:"contactRole"`
	ExpertiseRequired                  string                `json:"expertiseRequired"`
	PaymentTerms                       string                `json:"paymentTerms"`
	PublicLiabilityInsuranceExpiryDate string                `json:"publicLiabilityInsuranceExpiryDate"`
	OrientationConfirmation            string                `json:"orientationConfirmation"`
	FacilityAgreementId                uint64                `json:"facilityAgreementId"`
	Files                              []FacilityProfileFile `json:"files"`
}

func (m FacilityProfileInquireResp) GetCompareData() FacilityProfileInquireCompareResp {
	var compareData FacilityProfileInquireCompareResp
	_ = copier.Copy(&compareData, m)
	compareData.Files = make([]FacilityProfileFile, 0, len(m.Files))
	for _, f := range m.Files {
		compareData.Files = append(compareData.Files, f)
	}
	return compareData
}

type FacilityProfileFile struct {
	FacilityFileId uint64 `json:"facilityFileId"`                                     // 機構文件Id
	FileCode       string `json:"fileCode"`                                           // 機構文件類型
	OriginFileName string `json:"originFileName"`                                     // 原文件名
	ExpiryDate     string `json:"expiryDate" binding:"omitempty,datetime=2006-01-02"` // 到期日(YYYY-MM-DD)
	Number         string `json:"number"`                                             // 號碼
	Description    string `json:"description"`                                        // 描述
}

func (FacilityProfileFile) NewByFacilityFile(f model.FacilityFile) FacilityProfileFile {
	var m FacilityProfileFile
	m.FacilityFileId = f.Id
	m.FileCode = f.FileCode
	m.OriginFileName = f.OriginFileName
	var aiResult model.FacilityFileAiResultCache
	_ = json.Unmarshal([]byte(f.AiResultJson), &aiResult)
	m.ExpiryDate = aiResult.ExpiryDate
	m.Number = aiResult.Number
	m.Description = aiResult.Description
	return m
}

func (s *facilityProfileService) Inquire(db *gorm.DB, req FacilityProfileInquireReq) (FacilityProfileInquireResp, error) {
	var err error
	var resp FacilityProfileInquireResp
	var m model.FacilityProfile
	if err = db.First(&m, req.FacilityProfileId).Error; err != nil {
		return resp, err
	}
	_ = copier.Copy(&resp, m)
	resp.FacilityProfileId = m.Id

	var f model.Facility
	if err = db.Where("id = ?", m.FacilityId).First(&f).Error; err != nil {
		return resp, err
	}
	resp.Deactivated = f.Deactivated

	var files []model.FacilityFile
	if err = db.Select("ff.*").Table("facility_file_relation ffr").
		Joins("left join facility_file ff on ffr.facility_file_id = ff.id").
		Where("ffr.facility_profile_id = ?", m.Id).Order("ffr.id").Find(&files).Error; err != nil {
		return resp, err
	}

	resp.Files = make([]FacilityProfileFile, 0, len(files))
	for _, f := range files {
		resp.Files = append(resp.Files, FacilityProfileFile{}.NewByFacilityFile(f))
	}

	if m.DataType == model.FacilityProfileDataTypeDraft {
		// 找出目前的 Approve 版本
		var approveProfile model.FacilityProfile
		if err = db.Where("facility_id = ?", m.FacilityId).Where("data_type = ?", model.FacilityProfileDataTypeApproved).First(&approveProfile).Error; xgorm.IsSqlErr(err) {
			return resp, err
		}
		if xgorm.IsNotFoundErr(err) {
			// 沒有 Approved 的版本
			resp.Diff = "N"
		} else {
			approvedVersion, err := s.Inquire(db, FacilityProfileInquireReq{
				FacilityProfileId: approveProfile.Id,
			})
			if err != nil {
				return FacilityProfileInquireResp{}, err
			}
			daftJsonString, _ := json.Marshal(resp.GetCompareData())
			approvedJsonString, _ := json.Marshal(approvedVersion.GetCompareData())
			if string(daftJsonString) != string(approvedJsonString) {
				resp.Diff = "Y"
			} else {
				resp.Diff = "N"
			}
		}
	}

	return resp, nil
}

type FacilityProfileEditReq struct {
	FacilityProfileId                  uint64          `json:"facilityProfileId"  binding:"required"`
	FacilityType                       string          `json:"facilityType"`
	FacilityTypeName                   string          `json:"facilityTypeName" binding:"max=128"`
	Name                               string          `json:"name"`
	Email                              string          `json:"email" binding:"required,email,max=128"`
	Address                            string          `json:"address" binding:"max=255"`
	AddressExtra                       string          `json:"addressExtra" binding:"max=255"`
	LocationState                      string          `json:"locationState" binding:"max=128"`
	LocationCity                       string          `json:"locationCity" binding:"max=128"`
	LocationRoute                      string          `json:"locationRoute" binding:"max=128"`
	LocationLat                        decimal.Decimal `json:"locationLat"`
	LocationLng                        decimal.Decimal `json:"locationLng"`
	Abn                                string          `json:"abn" binding:"omitempty,len=11,numeric"`
	ContactFirstName                   string          `json:"contactFirstName" binding:"max=255"`
	ContactLastName                    string          `json:"contactLastName" binding:"max=255"`
	ContactRole                        string          `json:"contactRole" binding:"max=128"`
	ContactPhone                       string          `json:"contactPhone" binding:"max=128"`
	ContactEmail                       string          `json:"contactEmail" binding:"omitempty,email,max=128"`
	ExpertiseRequired                  string          `json:"expertiseRequired" binding:"max=1024"`
	PaymentTerms                       string          `json:"paymentTerms" binding:"omitempty,oneof=PAY_IN_ARREARS PAY_UPFRONT"`
	PublicLiabilityInsuranceExpiryDate string          `json:"publicLiabilityInsuranceExpiryDate"`
	OrientationConfirmation            string          `json:"orientationConfirmation" binding:"omitempty,oneof=Y N"`
	FacilityAgreementId                uint64          `json:"facilityAgreementId"`
	Files                              []uint64        `json:"files"`
}

func (s *facilityProfileService) Edit(db *gorm.DB, req FacilityProfileEditReq) error {
	var err error
	var m model.FacilityProfile
	if err = db.Where("data_type = ?", model.FacilityProfileDataTypeDraft).First(&m, req.FacilityProfileId).Error; err != nil {
		return err
	}
	var approveProfile model.FacilityProfile
	if err = db.Where("facility_id = ?", m.FacilityId).Where("data_type = ?", model.FacilityProfileDataTypeApproved).First(&approveProfile).Error; xgorm.IsSqlErr(err) {
		return err
	}
	if xgorm.IsNotFoundErr(err) {
		var _ = copier.Copy(&m, req)
	} else {
		// 已審核時，Name, FacilityType, FacilityTypeName, Abn 不可修改
		_ = xtool.CopyWithIgnoreFieldNames(&m, &req, "Name", "FacilityType", "FacilityTypeName", "Abn")
	}

	m.PublicLiabilityInsuranceExpiryDate = xtype.NewNullDate(req.PublicLiabilityInsuranceExpiryDate)

	var files []model.FacilityFile
	if err = db.Where("facility_id = ?", m.FacilityId).
		Where("file_code IN (?)", model.FacilityFileCodes).
		Where("id IN (?)", req.Files).Find(&files).Error; err != nil {
		return err
	}

	// 保存文件關聯
	fileIds := make([]uint64, 0, len(files))
	for _, file := range files {
		// 查是否已存在
		var ffr model.FacilityFileRelation
		if err = db.
			Where("facility_id = ?", m.FacilityId).
			Where("facility_profile_id = ?", m.Id).
			Where("facility_file_id = ?", file.Id).First(&ffr).Error; xgorm.IsSqlErr(err) {
			return err
		}
		if xgorm.IsNotFoundErr(err) {
			newRelation := model.FacilityFileRelation{
				FacilityId:        m.FacilityId,
				FacilityProfileId: m.Id,
				FacilityFileId:    file.Id,
			}
			if err = db.Create(&newRelation).Error; err != nil {
				return err
			}
			fileIds = append(fileIds, file.Id)
		} else {
			fileIds = append(fileIds, ffr.FacilityFileId)
		}
	}

	// 刪除除此以外的文件關聯
	if len(fileIds) > 0 {
		if err = db.Where("facility_id = ?", m.FacilityId).
			Where("facility_profile_id = ?", m.Id).
			Not("facility_file_id IN (?)", fileIds).Delete(model.FacilityFileRelation{}).Error; err != nil {
			return err
		}
	} else {
		if err = db.Where("facility_id = ?", m.FacilityId).
			Where("facility_profile_id = ?", m.Id).Delete(model.FacilityFileRelation{}).Error; err != nil {
			return err
		}
	}
	// 保存信息
	if err = db.Save(&m).Error; err != nil {
		return err
	}

	return nil
}

type FacilityProfileSubmitReq struct {
	FacilityProfileId uint64 `json:"facilityProfileId"  binding:"required"`
	Submit            string `json:"submit"  binding:"required,oneof=Y N"` // Y=提交,N=取消提交
}

func (s *facilityProfileService) Submit(db *gorm.DB, req FacilityProfileSubmitReq) error {
	var err error
	nowTime := time.Now().UTC()
	if req.Submit == "Y" {
		if err = db.Model(&model.FacilityProfile{}).
			Where("data_type = ?", model.FacilityProfileDataTypeDraft).
			Where("id = ?", req.FacilityProfileId).
			Where("status = ?", model.FacilityProfileStatusPending).
			Updates(map[string]interface{}{
				"status":           model.FacilityProfileStatusReviewing,
				"application_time": nowTime,
				"reject_reason":    "",
			}).Error; err != nil {
			return err
		}
	} else {
		if err = db.Model(&model.FacilityProfile{}).
			Where("data_type = ?", model.FacilityProfileDataTypeDraft).
			Where("id = ?", req.FacilityProfileId).
			Where("status = ?", model.FacilityProfileStatusReviewing).
			Updates(map[string]interface{}{
				"status":           model.FacilityProfileStatusPending,
				"application_time": nil,
			}).Error; err != nil {
			return err
		}
	}

	return nil
}

type FacilityProfileListReq struct {
	DataType             string `form:"dataType" binding:"required,oneof=DRAFT APPROVED"`
	Status               string `form:"status" binding:"required,oneof=PENDING REVIEWING APPROVED"`
	FacilityType         string `form:"facilityType"`
	Name                 string `form:"name"`
	Address              string `form:"address" binding:"max=255"`
	LocationState        string `form:"locationState" binding:"max=128"`
	LocationCity         string `form:"locationCity" binding:"max=128"`
	LocationRoute        string `form:"locationRoute" binding:"max=128"`
	ContactEmail         string `form:"contactEmail"`
	BeginApplicationDate string `form:"beginApplicationDate" binging:"omitempty,datetime=2006-01-02"`
	EndApplicationDate   string `form:"endApplicationDate" binging:"omitempty,datetime=2006-01-02"`
	Deactivated          string `form:"deactivated" binding:"omitempty,oneof=Y N"` // N=Available,Y=Unavailable
}

type FacilityProfileListResp struct {
	FacilityProfileId                  uint64          `json:"facilityProfileId"`
	FacilityId                         uint64          `json:"facilityId"`
	DataType                           string          `json:"dataType"`
	FacilityType                       string          `json:"facilityType"`
	FacilityTypeName                   string          `json:"facilityTypeName"`
	Name                               string          `json:"name"`
	Email                              string          `json:"email"`
	Address                            string          `json:"address"`
	AddressExtra                       string          `json:"addressExtra"`
	LocationState                      string          `json:"locationState"`
	LocationCity                       string          `json:"locationCity"`
	LocationRoute                      string          `json:"locationRoute"`
	LocationLat                        decimal.Decimal `json:"locationLat"`
	LocationLng                        decimal.Decimal `json:"locationLng"`
	ApplicationTime                    *time.Time      `json:"applicationTime"`
	Status                             string          `json:"status"`
	ApprovedTime                       *time.Time      `json:"approvedTime"`
	RejectReason                       string          `json:"rejectReason"`
	Abn                                string          `json:"abn"`
	ContactFirstName                   string          `json:"contactFirstName"`
	ContactLastName                    string          `json:"contactLastName"`
	ContactEmail                       string          `json:"contactEmail"`
	ContactPhone                       string          `json:"contactPhone"`
	ContactRole                        string          `json:"contactRole"`
	ExpertiseRequired                  string          `json:"expertiseRequired"`
	PaymentTerms                       string          `json:"paymentTerms"`
	PublicLiabilityInsuranceExpiryDate string          `json:"publicLiabilityInsuranceExpiryDate"`
	OrientationConfirmation            string          `json:"orientationConfirmation"`
	Deactivated                        string          `json:"deactivated"`
}

func (s *facilityProfileService) List(db *gorm.DB, req FacilityProfileListReq, pageSet *xresp.PageSet, sortSet xresp.SortingSet) ([]FacilityProfileListResp, error) {
	var err error
	var resp []FacilityProfileListResp
	builder := db.Table("facility_profile AS fp").
		Select([]string{
			"fp.id AS facility_profile_id",
			"fp.facility_id",
			"fp.data_type",
			"fp.facility_type",
			"fp.facility_type_name",
			"fp.name",
			"fp.email",
			"fp.address",
			"fp.address_extra",
			"fp.location_state",
			"fp.location_city",
			"fp.location_route",
			"fp.location_lat",
			"fp.location_lng",
			"fp.application_time",
			"fp.status",
			"fp.approved_time",
			"fp.reject_reason",
			"fp.abn",
			"fp.contact_first_name",
			"fp.contact_last_name",
			"fp.contact_email",
			"fp.contact_phone",
			"fp.contact_role",
			"fp.expertise_required",
			"fp.payment_terms",
			"fp.public_liability_insurance_expiry_date",
			"f.deactivated",
		}).
		Joins("JOIN facility f ON f.id = fp.facility_id")
	if req.DataType != "" {
		builder = builder.Where("fp.data_type = ?", req.DataType)
	}
	if req.FacilityType != "" {
		builder = builder.Where("fp.facility_type = ?", req.FacilityType)
	}
	if req.Name != "" {
		builder = builder.Where("fp.name LIKE ?", "%"+req.Name+"%")
	}
	if req.Address != "" {
		builder = builder.Where("CONCAT (fp.address_extra, fp.address) LIKE ?", "%"+req.Address+"%")
	}
	if req.LocationState != "" {
		builder = builder.Where("fp.location_state = ?", req.LocationState)
	}
	if req.LocationCity != "" {
		builder = builder.Where("fp.location_city LIKE ?", "%"+req.LocationCity+"%")
	}
	if req.LocationRoute != "" {
		builder = builder.Where("fp.location_route LIKE ?", "%"+req.LocationRoute+"%")
	}
	if req.Status != "" {
		builder = builder.Where("fp.status = ?", req.Status)
	}
	if req.ContactEmail != "" {
		builder = builder.Where("fp.contact_email LIKE ?", "%"+req.ContactEmail+"%")
	}
	if req.BeginApplicationDate != "" {
		builder = builder.Where("DATE(fp.application_time) >= ?", req.BeginApplicationDate)
	}
	if req.EndApplicationDate != "" {
		builder = builder.Where("DATE(fp.application_time) <= ?", req.EndApplicationDate)
	}
	if req.Deactivated != "" {
		builder = builder.Where("f.deactivated = ?", req.Deactivated)
	}
	sortKeyList := map[string]string{
		"name":            "fp.name",
		"applicationTime": "fp.application_time",
		"approvedTime":    "fp.approved_time",
	}
	if err = builder.Scopes(xresp.Paginate(pageSet)).Scopes(xresp.AddOrder(sortSet, sortKeyList)).Order("fp.id").Find(&resp).Error; err != nil {
		return resp, err
	}
	selectionNameMap, err := SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeFacilityProfileFacilityType})
	if err != nil {
		return nil, err
	}
	for i, r := range resp {
		if r.FacilityTypeName == "" {
			resp[i].FacilityTypeName = selectionNameMap[r.FacilityType]
		}
	}
	return resp, nil
}

type FacilityProfileApproveReq struct {
	FacilityProfileId uint64 `json:"facilityProfileId" binding:"required"`
	Approve           string `json:"approve" binding:"required,oneof=Y N"`                  // Y=通過,N=拒絕
	RejectReason      string `json:"rejectReason" binding:"required_if=Approve N,max=1024"` // 拒絕原因
	ReqUserId         uint64 `json:"-"`
}

func (s *facilityProfileService) Approve(db *gorm.DB, req FacilityProfileApproveReq) error {
	var err error
	var facilityProfile model.FacilityProfile
	if err = db.Where("data_type = ?", model.FacilityProfileDataTypeDraft).
		Where("id = ?", req.FacilityProfileId).
		Where("status = ?", model.FacilityProfileStatusReviewing).
		First(&facilityProfile).Error; err != nil {
		return err
	}

	nowTime := time.Now().UTC()
	if req.Approve == "Y" {
		facilityProfile.Status = model.FacilityProfileStatusPending
		facilityProfile.ApprovedTime = &nowTime
		facilityProfile.ApprovedUserId = req.ReqUserId
		facilityProfile.RejectReason = ""

		// 生成機構編號
		if facilityProfile.No == "" {
			facilityProfile.No, err = s.GenerateFacilityNo(db, facilityProfile.FacilityId, facilityProfile.Name)
			if err != nil {
				return err
			}
		}
	} else {
		facilityProfile.Status = model.FacilityProfileStatusPending
		facilityProfile.ApprovedTime = &nowTime
		facilityProfile.ApprovedUserId = req.ReqUserId
		facilityProfile.RejectReason = req.RejectReason
	}
	if err = db.Save(&facilityProfile).Error; err != nil {
		return err
	}
	// 如果通過，創建已審核版本
	if req.Approve == "Y" {
		// 如果審核通過，則將機構協議設為已簽署
		var facilityAgreements []model.FacilityAgreement
		if err = db.Where("facility_id = ?", facilityProfile.FacilityId).
			Where("status = ?", model.FacilityAgreementStatusReviewing).
			Find(&facilityAgreements).Error; err != nil {
			return err
		}
		if len(facilityAgreements) > 0 {
			updateMap := map[string]interface{}{
				"status":      model.FacilityAgreementStatusSigned,
				"update_time": time.Now().UTC().Truncate(time.Second),
			}
			if err = db.Model(&model.FacilityAgreement{}).
				Where("facility_id = ?", facilityProfile.FacilityId).
				Where("status = ?", model.FacilityAgreementStatusReviewing).
				Updates(updateMap).Error; err != nil {
				return err
			}
		}

		// 將之前的 Approved 設為 History
		if err = db.Model(&model.FacilityProfile{}).
			Where("facility_id = ?", facilityProfile.FacilityId).
			Where("data_type = ?", model.FacilityProfileDataTypeApproved).
			Updates(map[string]interface{}{
				"data_type": model.FacilityProfileDataTypeHistory,
			}).Error; err != nil {
			return err
		}
		var newProfile model.FacilityProfile
		_ = copier.Copy(&newProfile, facilityProfile)
		newProfile.Id = 0
		newProfile.DataType = model.FacilityProfileDataTypeApproved
		newProfile.Status = model.FacilityProfileStatusApproved
		if err = db.Create(&newProfile).Error; err != nil {
			return err
		}
		// 複製文件關係
		var files []model.FacilityFileRelation
		if err = db.Where("facility_profile_id = ?", facilityProfile.Id).Find(&files).Error; err != nil {
			return err
		}
		for _, file := range files {
			newFileRelation := model.FacilityFileRelation{
				FacilityId:        newProfile.FacilityId,
				FacilityProfileId: newProfile.Id,
				FacilityFileId:    file.FacilityFileId,
			}
			if err = db.Create(&newFileRelation).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

type FacilityProfileDeactivateReq struct {
	FacilityProfileId uint64 `json:"facilityProfileId" binding:"required"`
	Deactivate        string `json:"deactivate" binding:"required,oneof=Y N"` // Y=停用, N=啟用
}

// 停用/啟用機構資料
func (s *facilityProfileService) Deactivate(db *gorm.DB, req FacilityProfileDeactivateReq) error {
	var err error

	// 獲取機構ID
	var facilityProfile model.FacilityProfile
	if err = db.Where("id = ?", req.FacilityProfileId).First(&facilityProfile).Error; err != nil {
		return err
	}

	// 更新停用狀態
	if err = db.Model(&model.Facility{}).
		Where("id = ?", facilityProfile.FacilityId).
		Updates(map[string]interface{}{
			"deactivated": req.Deactivate,
		}).Error; err != nil {
		return err
	}

	// 查詢所有相關用戶
	var users []xmodel.User
	if err = db.Select("u.*").
		Table("user AS u").
		Joins("JOIN facility_user fu ON fu.user_id = u.id").
		Where("fu.facility_id = ?", facilityProfile.FacilityId).
		Find(&users).Error; err != nil {
		return err
	}

	// 逐個更新用戶狀態
	for _, user := range users {
		userStatus := xmodel.UserStatusDisable
		if req.Deactivate == "N" {
			userStatus = xmodel.UserStatusEnable
		}
		if err = db.Model(&xmodel.User{}).
			Where("id = ?", user.Id).
			Updates(map[string]interface{}{
				"status": userStatus,
			}).Error; err != nil {
			return err
		}
		// 將設備更新為停用狀態
		if req.Deactivate == "Y" {
			if err = db.Model(&xmodel.UserDevice{}).
				Where("user_id = ?", user.Id).
				Updates(map[string]interface{}{
					"status": xmodel.UserDeviceStatusDisable,
				}).Error; err != nil {
				return err
			}
		}
	}

	// 立即清除這些用戶的所有登入緩存 cache:user_device:userId:*
	for _, user := range users {
		err = xredis.DeleteKey(db.Statement.Context, fmt.Sprintf("cache:user_device:%d:*", user.Id))
		if err != nil {
			return err
		}
	}

	return nil
}

// 獲取機構資料
func (s *facilityProfileService) GetCurrentFacilityProfile(db *gorm.DB, facilityId uint64) (model.FacilityProfile, error) {
	var facilityProfile model.FacilityProfile
	if err := db.Where("facility_id = ?", facilityId).
		Where("data_type = ?", model.FacilityProfileDataTypeApproved).
		First(&facilityProfile).Error; err != nil {
		return model.FacilityProfile{}, err
	}
	return facilityProfile, nil
}
