package services

import (
	"fmt"
	"io"
	"mime"
	"mime/multipart"
	"path"
	"path/filepath"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xs3"
	"github.com/Norray/xrocket/xtool"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

var FacilityFileService = new(facilityFileService)

type facilityFileService struct{}

func (s *facilityFileService) CheckIdExist(db *gorm.DB, m *model.FacilityFile, id uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_file.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var err error
	if err = db.First(&m, id).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

func (s *facilityFileService) CheckFileCodeExist(fileCode string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_file.code.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	for _, code := range model.FacilityFileCodes {
		if code == fileCode {
			return true, i18n.Message{}, nil
		}
	}
	return false, msg, nil
}

func (s *facilityFileService) CheckFileExist(db *gorm.DB, files *[]model.FacilityFile, facilityId uint64, fileCodes []string, ids []uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_file.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	if len(ids) == 0 {
		return true, i18n.Message{}, nil
	}
	ids = xtool.Uint64ArrayDeduplication(ids)
	if err := db.Where("facility_id = ?", facilityId).
		Where("file_code IN (?)", fileCodes).
		Where("id IN (?)", ids).Find(files).Error; err != nil {
		return false, msg, err
	}
	if len(ids) == len(*files) {
		return true, i18n.Message{}, nil
	}
	return false, msg, nil
}

type FacilityFileUploadReq struct {
	FacilityId uint64                `form:"facilityId" binding:"required"`
	FileCode   string                `form:"fileCode" binding:"required"`
	File       *multipart.FileHeader `form:"-" swaggerignore:"true"`
}

type FacilityFileUploadResp struct {
	FacilityFileId uint64 `json:"facilityFileId"`
}

func (s *facilityFileService) Upload(db *gorm.DB, req FacilityFileUploadReq) (FacilityFileUploadResp, error) {
	var resp FacilityFileUploadResp
	var err error
	uuidStr := uuid.NewV4().String()
	uuidName := uuidStr + path.Ext(req.File.Filename)

	reader, err := req.File.Open()
	if err != nil {
		return resp, err
	}
	defer func(reader multipart.File) {
		_ = reader.Close()
	}(reader) // Ensure the reader is closed

	// 構建縮略圖
	thumbnailResp, err := ImageService.ConvertToThumbnail(reader, req.File.Filename, uuidStr)
	if err != nil {
		return resp, err
	}

	// 生成文件Model
	facilityFile := model.FacilityFile{
		FacilityId:        req.FacilityId,
		FileCode:          req.FileCode,
		Mode:              xs3.PrivateMode,
		Bucket:            xconfig.OSSConf.Bucket,
		Path:              fmt.Sprintf(OSSFacilityFilePath, req.FacilityId, req.FileCode, uuidName),
		Uuid:              uuidStr,
		OriginFileName:    req.File.Filename,
		FileName:          uuidName,
		FileType:          path.Ext(req.File.Filename),
		FileSize:          uint32(req.File.Size),
		ThumbnailPath:     fmt.Sprintf(OSSFacilityFilePath, req.FacilityId, req.FileCode, thumbnailResp.ThumbnailUuidName),
		ThumbnailFileSize: thumbnailResp.Size,
	}
	if err = db.Create(&facilityFile).Error; err != nil {
		return resp, err
	}
	// 先上傳縮略圖
	err = xs3.UploadObjectFromReader(facilityFile.Bucket, facilityFile.ThumbnailPath, facilityFile.OriginFileName, thumbnailResp.ThumbnailBody)
	if err != nil {
		return resp, err
	}
	// 重置 reader 指針
	_, err = reader.Seek(0, io.SeekStart)
	if err != nil {
		return resp, err
	}
	// 最後再上傳源文件
	err = xs3.UploadObjectFromReader(facilityFile.Bucket, facilityFile.Path, facilityFile.OriginFileName, reader)
	if err != nil {
		// 源文件上傳失敗，刪除縮略圖
		_ = xs3.DeleteObject(facilityFile.Bucket, facilityFile.ThumbnailPath)
		return resp, err
	}
	resp.FacilityFileId = facilityFile.Id
	return resp, nil
}

type FacilityFileGetPreviewReq struct {
	FacilityFileId uint64 `form:"facilityFileId" binding:"required"`
	Thumb          string `form:"thumb" binding:"required,oneof=Y N"`
}

type FacilityFileGetPreviewResp struct {
	FileBytes []byte
	Filename  string
}

func (s *facilityFileService) Preview(db *gorm.DB, req FacilityFileGetPreviewReq) (FacilityFileGetPreviewResp, error) {
	var err error
	var resp FacilityFileGetPreviewResp
	var m model.FacilityFile
	if err = db.First(&m, req.FacilityFileId).Error; err != nil {
		return resp, err
	}
	var object []byte
	if req.Thumb == "Y" {
		object, err = xs3.GetObject(m.Bucket, m.ThumbnailPath)
	} else {
		object, err = xs3.GetObject(m.Bucket, m.Path)
	}
	if err != nil {
		return FacilityFileGetPreviewResp{}, err
	}
	resp.FileBytes = object
	resp.Filename = m.OriginFileName
	return resp, nil
}

func (s *facilityFileService) GetFileMimeType(path string) string {
	ext := filepath.Ext(path)
	mimeType := mime.TypeByExtension(ext)
	if mimeType == "" {
		mimeType = "application/octet-stream"
	}
	return mimeType
}
