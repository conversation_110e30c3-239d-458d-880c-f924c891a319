package model

import (
	"encoding/json"
	"time"

	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xmodel/xtype"
	"github.com/shopspring/decimal"
)

const (
	ProfessionalDataTypeDraft    = "DRAFT"    // 草稿版本 (status=PENDING才可以修改)
	ProfessionalDataTypeApproved = "APPROVED" // 已通過版本 (不能修改)
	ProfessionalDataTypeHistory  = "HISTORY"  // 歷史版本 (不能修改)

	ProfessionalStatusPending   = "PENDING"   // 未提交
	ProfessionalStatusReviewing = "REVIEWING" // 審核中
	ProfessionalStatusApproved  = "APPROVED"  // 已通過
	ProfessionalStatusDeleted   = "DELETED"   // 已刪除

	ProfessionalProfessionMedicalPractitioner = "MEDICAL_PRACTITIONER" // 醫療從業員
	ProfessionalProfessionEnrolledNurse       = "ENROLLED_NURSE"       // 登記護士
	ProfessionalProfessionRegisteredNurse     = "REGISTERED_NURSE"     // 註冊護士
	ProfessionalProfessionPersonalCareWorker  = "PERSONAL_CARE_WORKER" // 個人護理員

	ProfessionalAbnValidStatusY = "Y" // 有效
	ProfessionalAbnValidStatusN = "N" // 無效/未驗證

	ProfessionalSignedAgreementY = "Y" // 已簽署協議
	ProfessionalSignedAgreementN = "N" // 未簽署協議

	ProfessionalDisclosureQuestionAnswerY = "Y" // 是
	ProfessionalDisclosureQuestionAnswerN = "N" // 否

	// Medication Endorsement (Enrolled Nurse) 藥物授權
	ProfessionalMedicationEndorsementY = "Y" // 有藥物授權
	ProfessionalMedicationEndorsementN = "N" // 無藥物授權

	// Preferred Grade (Medical Practitioner) 首選級別
	ProfessionalPreferredGradeResidentMedicalOfficer = "RESIDENT_MEDICAL_OFFICER" // Resident Medical Officer
	ProfessionalPreferredGradeRegistrarUnaccredited  = "REGISTRAR_UNACCREDITED"   // Registrar (unaccredited)
	ProfessionalPreferredGradeRegistrarAccredited    = "REGISTRAR_ACCREDITED"     // Registrar (accredited)
	ProfessionalPreferredGradeFellow                 = "FELLOW"                   // Fellow
	ProfessionalPreferredGradeSpecialist             = "SPECIALIST"               // Specialist

	// Permission to Work in Australia 澳洲工作許可
	ProfessionalPermissionToWorkAustralianCitizen = "AUSTRALIAN_CITIZEN" // Australian Citizen
	ProfessionalPermissionToWorkPermanentResident = "PERMANENT_RESIDENT" // Permanent Resident
	ProfessionalPermissionToWorkVisa              = "VISA"               // Visa With Work Rights
	ProfessionalPermissionToWorkNone              = "NONE"               // No Visa/Work Rights

	// ID Check Deletion Consent ID文件刪除同意
	ProfessionalIdCheckDeletionConsentY = "Y" // 已同意
	ProfessionalIdCheckDeletionConsentN = "N" // 未同意

	ProfessionalProfileFileCodeIndemnityInsuranceCertificate         = "INDENMITY_INSURANCE_CERTIFICATE"            // 專業人士責任保險證明
	ProfessionalProfileFileCodeAustralianPassport                    = "AUSTRALIAN_PASSPORT"                        // 澳洲護照
	ProfessionalProfileFileCodeForeignPassport                       = "FOREIGN_PASSPORT"                           // 外國護照
	ProfessionalProfileFileCodeAustralianBirthCertificate            = "AUSTRALIAN_BIRTH_CERTIFICATE"               // 澳洲出生證明
	ProfessionalProfileFileCodeAustralianCitizenshipCertificate      = "AUSTRALIAN_CITIZENSHIP_CERTIFICATE"         // 澳洲公民證
	ProfessionalProfileFileCodeVisa                                  = "VISA"                                       // 簽證
	ProfessionalProfileFileCodeNationalCriminalCheck                 = "NATIONAL_CRIMINAL_CHECK"                    // 國家犯罪檢查
	ProfessionalProfileFileCodeWorkingWithChildrenOrVulnerablePeople = "WORKING_WITH_CHILDREN_OR_VULNERABLE_PEOPLE" // 兒童/脆弱人群工作檢查
	ProfessionalProfileFileCodeCurrentImmunisationRecords            = "CURRENT_IMMUNISATION_RECORDS"               // 現在的免疫記錄
	ProfessionalProfileFileCodeAdditionalCertification               = "ADDITIONAL_CERTIFICATION"                   // 附加證明
	ProfessionalProfileFileCodeQualificationCertificate              = "QUALIFICATION_CERTIFICATE"                  // 學歷資格證書
)

// 專業人士
type Professional struct {
	Id                uint64          `json:"id" gorm:"primary_key"`
	UserId            uint64          `json:"userId" gorm:"index:user_idx;not null"`                             // 用戶Id
	DataType          string          `json:"dataType" gorm:"type:varchar(32);index:data_type_idx"`              // 數據類型 DRAFT=草稿,APPROVED=已通過,HISTORY=歷史
	FirstName         string          `json:"firstName" gorm:"type:varchar(128);not null"`                       // 名字
	LastName          string          `json:"lastName" gorm:"type:varchar(128);not null"`                        // 姓氏
	MinimumHourlyRate decimal.Decimal `json:"minimumHourlyRate" gorm:"type:decimal(10,2);not null"`              // 最低時薪
	PreferredState    string          `json:"preferredState" gorm:"type:varchar(128);not null"`                  // 首選地區 - 州
	PreferredLocality string          `json:"preferredLocality" gorm:"type:varchar(128);not null"`               // 首選地區 - 城市
	DistanceWithin    decimal.Decimal `json:"distanceWithin" gorm:"type:decimal(10,2);not null"`                 // 距離範圍(KM)
	PermissionToWork  string          `json:"permissionToWork" gorm:"type:varchar(255);not null"`                // 工作許可
	Profession        string          `json:"profession" gorm:"type:varchar(255);index:profession_idx;not null"` // 專業

	Gender        string          `json:"gender" gorm:"type:varchar(32);not null"`                                  // 性別
	DateOfBirth   xtype.NullDate  `json:"dateOfBirth" gorm:"type:date"`                                             // 出生日期
	Address       string          `json:"address" gorm:"type:varchar(1024);not null"`                               // 居住地址
	AddressExtra  string          `json:"addressExtra" gorm:"type:varchar(1024);not null"`                          // 地址附加信息
	LocationState string          `json:"locationState" gorm:"type:varchar(128);index:location_state_idx;not null"` // 州
	LocationCity  string          `json:"locationCity" gorm:"type:varchar(128);index:location_city_idx;not null"`   // 城市
	LocationRoute string          `json:"locationRoute" gorm:"type:varchar(128);index:location_route_idx;not null"` // 街道
	LocationLat   decimal.Decimal `json:"locationLat" gorm:"type:decimal(12,8);not null"`                           // 緯度
	LocationLng   decimal.Decimal `json:"locationLng" gorm:"type:decimal(12,8);not null"`                           // 經度
	//Grade               string          `json:"preferredGrade" gorm:"type:varchar(255);not null"`                         // 首選級別
	ExperienceLevel string `json:"experienceLevel" gorm:"type:varchar(255);not null"` // 經驗級別
	//speciality        string          `json:"preferredSpecialities" gorm:"type:varchar(1024);not null"`                 // 首選專業
	//PreferredSpecialityOtherName string          `json:"preferredSpecialityOtherName" gorm:"type:varchar(255);not null"`           // 首選專業其他
	Language              string `json:"language" gorm:"type:varchar(1024);not null"`           // 語言能力 (逗號分隔多個語言)
	MedicationEndorsement string `json:"medicationEndorsement" gorm:"type:varchar(1);not null"` // 藥物授權 Y N (僅Enrolled Nurse)
	GraduationYear        int32  `json:"graduationYear" gorm:"not null"`                        // 畢業年份(yyyy)

	AhpraNumber       string         `json:"ahpraNumber" gorm:"type:varchar(255);not null"`           // AHPRA註冊號碼
	AhpraExpiryDate   xtype.NullDate `swaggertype:"string" json:"ahpraExpiryDate" gorm:"type:date"`   // AHPRA註冊到期日
	AbnNumber         string         `json:"abnNumber" gorm:"type:varchar(255);not null"`             // ABN號碼
	AbnValid          string         `json:"abnValid" gorm:"type:varchar(1);not null"`                // ABN有效 Y/N
	AbnEntityName     string         `json:"abnEntityName" gorm:"type:varchar(255);not null"`         // ABN實體名稱
	AbnEntityType     string         `json:"abnEntityType" gorm:"type:varchar(255);not null"`         // ABN實體類型
	IdCheckExpiryDate xtype.NullDate `swaggertype:"string" json:"idCheckExpiryDate" gorm:"type:date"` // 身份證明到期日
	SignedAgreement   string         `json:"signedAgreement" gorm:"type:varchar(1);not null"`         // 是否簽署協議 Y/N

	ProfileJson     string     `json:"profileJson" gorm:"type:mediumtext;not null"`                  // 個人資料
	ApplicationTime *time.Time `swaggertype:"string" json:"applicationTime" gorm:"type:datetime(0)"` // 申請日期
	ApprovedUserId  uint64     `json:"approvedUserId" gorm:"not null"`                               // 審核人員
	ApprovedTime    *time.Time `swaggertype:"string" json:"approvedTime" gorm:"type:datetime(0)"`    // 審核時間
	RejectReason    string     `json:"rejectReason" gorm:"type:varchar(1024);not null"`              // 拒絕原因
	UpdatePrompt    string     `json:"updatePrompt" gorm:"type:varchar(150);not null"`               // 更新提示
	Status          string     `json:"status" gorm:"type:varchar(32);index:status_idx;not null"`     // 狀態
	CreateTime      time.Time  `json:"createTime" gorm:"type:datetime(0);not null"`                  // 創建時間
	UpdateTime      time.Time  `json:"updateTime" gorm:"type:datetime(0);not null"`                  // 更新時間
	xmodel.Model
}

func (p *Professional) TableName() string {
	return "professional"
}

func (p *Professional) SwaggerDescription() string {
	return "專業人士"
}

func (p *Professional) UnmarshalProfile(profileJson string) (ProfessionalProfile, error) {
	var profile ProfessionalProfile
	err := json.Unmarshal([]byte(profileJson), &profile)
	if err != nil {
		return profile, err
	}
	return profile, nil
}

func (p *Professional) MarshalProfile(profile ProfessionalProfile) error {
	profileJson, err := json.Marshal(profile)
	if err != nil {
		return err
	}
	p.ProfileJson = string(profileJson)
	return nil
}

type ProfessionalPreferredSpeciality struct {
	Speciality    string `json:"speciality" `    // 首選專業
	SubSpeciality string `json:"subSpeciality" ` // 首選子專業
	Grade         string `json:"grade"`          // 首選級別
}

func (s *ProfessionalPreferredSpeciality) GetSpeciality() string {
	if s.Speciality != "" {
		return s.Speciality
	}
	return s.SubSpeciality
}

type ProfessionalProfile struct {
	Version string `json:"version"` // JSON結構版本

	EmergencyContactFirstName    string `json:"emergencyContactFirstName"`    // 緊急聯絡人名字
	EmergencyContactLastName     string `json:"emergencyContactLastName"`     // 緊急聯絡人姓氏
	EmergencyContactPhone        string `json:"emergencyContactPhone"`        // 緊急聯絡人電話
	EmergencyContactRelationship string `json:"emergencyContactRelationship"` // 緊急聯絡人關係

	PreferredSpecialities []ProfessionalPreferredSpeciality `json:"preferredSpecialities"` // 首選專業

	QualificationName     string `json:"qualificationName"`     // 學歷名稱
	GraduationInstitution string `json:"graduationInstitution"` // 畢業院校
	InstitutionCountry    string `json:"institutionCountry"`    // 院校國家

	Experiences                      []ProfessionalExperience `json:"experiences"`                                                    // 工作經驗
	CvNoContactDetailsConfirmed      string                   `json:"cvNoContactDetailsConfirmed" binding:"omitempty,oneof=Y N"`      // 確認CV無聯絡方式 Y/N
	CompletedStudiesInLastThreeYears string                   `json:"completedStudiesInLastThreeYears" binding:"omitempty,oneof=Y N"` // 過去三年內完成學習 Y/N
	Qualification                    string                   `json:"qualification"`                                                  // 學歷
	QualificationEndDate             string                   `json:"qualificationEndDate" binding:"omitempty,datetime=2006-01-02"`   // 學歷結束日期(YYYY-MM-DD)
	References                       []ProfessionalReference  `json:"references"`                                                     // 推薦人
	HasOverseasCitizenshipOrPr       string                   `json:"hasOverseasCitizenshipOrPr" binding:"omitempty,oneof=Y N"`       // 是否擁有海外公民身份或永久居留權 Y/N
	RequiresStatutoryDeclaration     string                   `json:"requiresStatutoryDeclaration" binding:"omitempty,oneof=Y N"`     // 是否需要法定聲明 Y/N

	HasCompletedInfectionControlTraining string                           `json:"hasCompletedInfectionControlTraining" binding:"omitempty,oneof=Y N"` // 是否完成感染控制培訓 Y/N
	DisclosureQuestions                  []ProfessionalDisclosureQuestion `json:"disclosureQuestions"`                                                // 披露問題
	Files                                []ProfessionalProfileFile        `json:"files"`                                                              // 各類文件信息

	// 100 Point ID Check
	IdCheckDeletionConsent string `json:"idCheckDeletionConsent" binding:"omitempty,oneof=Y N"` // 用戶是否同意ID文件刪除聲明 Y/N
	IdCheckFileTypes       string `json:"idCheckFileTypes"`                                     // 已審核的ID文件類型（逗號分隔）

	WorkingWithChildrenOrVulnerablePeopleStates string `json:"workingWithChildrenOrVulnerablePeopleSelectedStates"` // 有兒童/脆弱人群工作資格的地區
}

// 專業人士文件信息
type ProfessionalProfileFile struct {
	ProfessionalFileIds []uint64 `json:"professionalFileId"`                                 // 專業人士文件Id
	FileCode            string   `json:"fileCode" binding:"required"`                        // 專業人士文件類型
	ExpiryDate          string   `json:"expiryDate" binding:"omitempty,datetime=2006-01-02"` // 到期日(YYYY-MM-DD)
	Number              string   `json:"number"`                                             // 號碼
	Description         string   `json:"description"`                                        // 描述
}

// 工作經驗
type ProfessionalExperience struct {
	Role              string `json:"role"`                                              // 職位
	Speciality        string `json:"speciality" `                                       // 首選專業
	SubSpeciality     string `json:"subSpeciality" `                                    // 首選子專業
	Grade             string `json:"grade"`                                             // 首選級別
	FacilityName      string `json:"facilityName"`                                      // 機構名稱
	FacilityOtherName string `json:"facilityOtherName"`                                 // 機構名稱其他
	StartDate         string `json:"startDate" binding:"omitempty,datetime=2006-01-02"` // 開始日期(YYYY-MM-DD)
	EndDate           string `json:"endDate" binding:"omitempty,datetime=2006-01-02"`   // 結束日期(YYYY-MM-DD)
	StillInRole       string `json:"stillInRole" binding:"omitempty,oneof=Y N"`         // 是否仍在職 Y/N
	AdditionalNotes   string `json:"additionalNotes"`                                   // 其他備註
}

// 推薦人
type ProfessionalReference struct {
	ReferenceFirstName string `json:"referenceFirstName"`                       // 推薦人名字
	ReferenceLastName  string `json:"referenceLastName"`                        // 推薦人姓氏
	ReferencePhone     string `json:"referencePhone"`                           // 推薦人電話
	ReferenceEmail     string `json:"referenceEmail" binding:"omitempty,email"` // 推薦人電郵
	ReferenceFacility  string `json:"referenceFacility"`                        // 推薦人機構
	ReferenceRole      string `json:"referenceRole"`                            // 推薦人職位
}

// 披露問題
type ProfessionalDisclosureQuestion struct {
	QuestionCode string `json:"questionCode" binding:"required"`      // 問題編碼
	Answer       string `json:"answer" binding:"omitempty,oneof=Y N"` // 答案 Y/N
}

// 專業人士資料文件需要參數
const (
	ProfessionalProfileFileNoNeedParam                  = 0         // 不需要
	ProfessionalProfileFileNeedExpiryDate               = 1         // 到期日
	ProfessionalProfileFileNeedNumber                   = 2         // 號碼
	ProfessionalProfileFileNeedDescription              = 4         // 描述
	ProfessionalProfileFileNeedExpiryDateAndNumber      = 1 + 2     // 3 到期日+號碼
	ProfessionalProfileFileNeedExpiryDateAndDescription = 1 + 4     // 5 到期日+描述
	ProfessionalProfileFileNeedNumberAndDescription     = 2 + 4     // 6 號碼+描述
	ProfessionalProfileFileNeedAllParam                 = 1 + 2 + 4 // 7 到期日+號碼+描述
)

var ProfessionalProfileFileNeedParamMap = map[string]int{
	ProfessionalFileCodePhoto:                                                             ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodeAhpraCertificate:                                                  ProfessionalProfileFileNeedExpiryDateAndNumber,
	ProfessionalFileCodeAbn:                                                               ProfessionalProfileFileNeedNumber,
	ProfessionalFileCodeIndemnityInsuranceCertificate:                                     ProfessionalProfileFileNeedExpiryDate,
	ProfessionalFileCodePersonalCareWorkerQualificationExperienceAgedCareDisability:       ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIAGEDCARE:             ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodePersonalCareWorkerQualificationCertIIIDisabilities:                ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodePersonalCareWorkerQualificationCertIIIIndividualSupport:           ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodePersonalCareWorkerQualificationCertIIIIndividualSupportAgedAged:   ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodePersonalCareWorkerQualificationCertIIIIndividualSupportDisability: ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIHomeCommunityCare:    ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodePersonalCareWorkerQualificationCertificateIVAGEDCARE:              ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodePersonalCareWorkerQualificationCertificateIVDisabilities:          ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodePersonalCareWorkerQualificationCertificateIVHomeCommunityCare:     ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodePersonalCareWorkerQualificationDegreeAlliedHealth:                 ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodePersonalCareWorkerQualificationDegreeNursing:                      ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodePersonalCareWorkerQualificationOtherRelevant:                      ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodePersonalCareWorkerQualificationWorkingTowardNursing:               ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodeAustralianPassport:                                                ProfessionalProfileFileNeedExpiryDate,
	ProfessionalFileCodeForeignPassport:                                                   ProfessionalProfileFileNeedExpiryDate,
	ProfessionalFileCodeAustralianBirthCertificate:                                        ProfessionalProfileFileNeedExpiryDate,
	ProfessionalFileCodeAustralianCitizenshipCertificate:                                  ProfessionalProfileFileNeedExpiryDate,
	ProfessionalFileCodeCurrentAustraliaDriverLicence:                                     ProfessionalProfileFileNeedExpiryDate,
	ProfessionalFileCodeAustralianPublicServiceEmployeeIDCard:                             ProfessionalProfileFileNeedExpiryDate,
	ProfessionalFileCodeOtherAustralianGovernmentIssueIDCard:                              ProfessionalProfileFileNeedExpiryDate,
	ProfessionalFileCodeTertiaryStudentIDCard:                                             ProfessionalProfileFileNeedExpiryDate,
	ProfessionalFileCodeCreditDebitAtmCard:                                                ProfessionalProfileFileNeedExpiryDate,
	ProfessionalFileCodeMedicareCard:                                                      ProfessionalProfileFileNeedExpiryDate,
	ProfessionalFileCodeUtilityBillOrRateNotice:                                           ProfessionalProfileFileNeedExpiryDate,
	ProfessionalFileCodeStatementFromFinancialInstitution:                                 ProfessionalProfileFileNeedExpiryDate,
	ProfessionalFileCodeCentrelinkOrPensionCard:                                           ProfessionalProfileFileNeedExpiryDate,
	ProfessionalFileCodeVisa:                                                              ProfessionalProfileFileNeedExpiryDate,
	ProfessionalFileCodeNationalCriminalCheck:                                             ProfessionalProfileFileNeedExpiryDate,
	ProfessionalFileCodeWorkingWithChildrenOrVulnerablePeople:                             ProfessionalProfileFileNeedAllParam, // 描述用於存儲地區
	ProfessionalFileCodeCurrentImmunisationRecords:                                        ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodeCommonwealthStatutoryDeclaration:                                  ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodeAdditionalCertification:                                           ProfessionalProfileFileNeedExpiryDateAndDescription,
	ProfessionalFileCodeQualificationCertificate:                                          ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodeDisclosure:                                                        ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodeSignedAgreement:                                                   ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodeRegistrarAccreditedEnrolment:                                      ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodeFellowshipCertificate:                                             ProfessionalProfileFileNoNeedParam,
	ProfessionalFileCodeSpecialistQualification:                                           ProfessionalProfileFileNoNeedParam,
}
