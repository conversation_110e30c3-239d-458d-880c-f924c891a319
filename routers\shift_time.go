package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/gin-gonic/gin"
)

func shiftTimeFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		r.GET("/shift-times", facility_api.NewShiftTimeController().List)
		r.POST("/shift-times/actions/update", facility_api.NewShiftTimeController().Update)
	}
}
