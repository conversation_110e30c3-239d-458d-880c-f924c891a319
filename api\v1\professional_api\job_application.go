package professional_api

import (
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type JobApplicationController struct{}

func NewJobApplicationController() JobApplicationController {
	return JobApplicationController{}
}

// @Tags Job
// @Summary 獲取我的工作職位信息列表
// @Description
// @Router /v1/professional/my-jobs [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.MyJobListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Param sortingKey query string false "beginTime 開始时间, applyTime 申請时间, hourlyRate 時薪"
// @Param sortingType query string false "a=順序,d=倒序"
// @Success 200 {object} []services.MyJobListResp "Success"
func (con JobApplicationController) MyJob(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.MyJobListReq
	var pageSet xresp.PageSet
	var sortSet xresp.SortingSet
	_ = c.ShouldBindQuery(&pageSet)
	_ = c.ShouldBindQuery(&sortSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.ReqUserId = nc.GetJWTUserId()
		resp, err := services.JobApplicationService.MyJobList(db, req, &pageSet, sortSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job
// @Summary 獲取我的工作職位統計
// @Description 獲取我的工作職位統計
// @Router /v1/professional/my-jobs/actions/statistic [GET]
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} services.MyJobStatisticResp "Success"
func (con JobApplicationController) Statistic(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.MyJobStatisticReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.ReqUserId = nc.GetJWTUserId()
		resp, err := services.JobApplicationService.MyJobStatistic(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job
// @Summary 撤回工作申請
// @Description 專業人員撤回已提交的工作申請
// @Router /v1/professional/my-jobs/actions/withdraw [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.MyJobWithdrawReq true "parameter"
// @Success 200 "Success"
func (con JobApplicationController) Withdraw(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.MyJobWithdrawReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		// 檢查申請是否可以撤回
		var alertMsg []string
		alerter := xapp.NewCK(c)
		alerter.
			Run(func() (bool, i18n.Message, error) {
				return services.JobApplicationService.CheckJobApplicationCanWithdraw(db, req.JobApplicationId, nc.GetJWTUserId())
			})
		alertMsg, err = alerter.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}

		// 執行撤回操作
		tx := db.Begin()
		req.ReqUserId = nc.GetJWTUserId()
		err = services.JobService.MyJobWithdraw(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()

		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job
// @Summary 取消工作申請
// @Description 專業人員取消已開始接洽的工作申請
// @Router /v1/professional/my-jobs/actions/cancel [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.MyJobCancelReq true "parameter"
// @Success 200 "Success"
func (con JobApplicationController) Cancel(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.MyJobCancelReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		// 檢查申請是否可以取消
		var alertMsg []string
		alerter := xapp.NewCK(c)
		alerter.
			Run(func() (bool, i18n.Message, error) {
				return services.JobApplicationService.CheckJobApplicationCanCancel(db, req.JobApplicationId, nc.GetJWTUserId())
			})
		alertMsg, err = alerter.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}

		// 執行取消操作
		tx := db.Begin()
		req.ReqUserId = nc.GetJWTUserId()
		err = services.JobApplicationService.MyJobCancel(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()

		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job
// @Summary 更新工作備註
// @Description 更新工作的備註信息
// @Router /v1/professional/my-jobs/actions/update-calendar-note [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.MyJobUpdateCalendarNoteReq true "parameter"
// @Success 200 "Success"
func (con JobApplicationController) UpdateCalendarNote(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.MyJobUpdateCalendarNoteReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.ReqUserId = nc.GetJWTUserId()

		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.Run(func() (bool, i18n.Message, error) {
			return services.JobApplicationService.CheckJobApplicationCanUpdateCalendarNote(db, req.JobApplicationId, req.ReqUserId)
		})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.JobApplicationService.MyJobUpdateCalendarNote(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Job
// @Summary 接受邀請
// @Description 專業人員接受邀請
// @Router /v1/professional/my-jobs/actions/accept-invite [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.JobAcceptInviteReq true "parameter"
// @Success 200 "Success"
func (con JobApplicationController) AcceptInvite(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobAcceptInviteReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.ReqUserId = nc.GetJWTUserId()

		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) { // 檢查工作是否存在
				return services.JobService.CheckJobCanApplyByProfessional(db, req.JobId, nc.GetJWTUserId())
			}).
			Run(func() (bool, i18n.Message, error) { // 檢查申請是否存在
				return services.JobApplicationService.CheckIdExist(db, &model.JobApplication{}, req.JobApplicationId, req.ReqUserId)
			})

		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		var alertMsg []string
		alerter := xapp.NewCK(c)
		alerter.
			Run(func() (bool, i18n.Message, error) { // 檢查是否可以接受邀請
				return services.JobService.CheckCanAcceptInvite(db, req.FacilityId, req.JobId, req.JobApplicationId)
			})
		alertMsg, err = alerter.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}
		tx := db.Begin()
		err = services.JobApplicationService.AcceptInvite(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags JobApplication
// @Summary 查詢專業人士的工作職位申請列表 (聊天會話)
// @Description 查詢專業人士的工作職位申請列表 (聊天會話)
// @Router /v1/professional/job-applications/actions/list-by-professional-session [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.JobApplicationListByProfessionalSessionReq true "parameter"
// @Success 200 {object} []services.JobApplicationDetailBySessionResp "Success"
func (con JobApplicationController) ListByProfessionalSession(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobApplicationListByProfessionalSessionReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.ProfessionalUserId = nc.GetJWTUserId()

		// 檢查聊天會話是否存在
		checker := xapp.NewCK(c)
		checker.Run(func() (bool, i18n.Message, error) {
			// 檢查機構與專業人士的聊天會話是否存在
			return services.JobApplicationService.CheckFacilityProfessionalApplicationByUserId(db, req.FacilityId, req.ProfessionalUserId)
		})

		checkMsg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		resp, err := services.JobApplicationService.JobApplicationListByProfessionalSession(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags JobApplication
// @Summary 專業人士打開聊天會話
// @Description 專業人士打開聊天會話
// @Router /v1/professional/job-applications/actions/chat [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ChatByProfessionalReq true "parameter"
// @Success 200 "Success"
func (con JobApplicationController) ChatByProfessional(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ChatByProfessionalReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.ReqUserId = nc.GetJWTUserId()

		checker := xapp.NewCK(c)
		checker.Run(func() (bool, i18n.Message, error) {
			return services.JobApplicationService.CheckSessionExistByProfessional(db, req.JobApplicationId, req.ReqUserId, &req.SessionId)
		})

		checkMsg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.AlertResponse(checkMsg)
			return
		}
		tx := db.Begin()
		err = services.JobApplicationService.ChatByProfessional(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}
