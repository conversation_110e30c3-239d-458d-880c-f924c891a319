package routers

import (
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func faqFileSystemRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system").Use(handlers...)
	{
		r.GET("/faq-files/actions/preview-url", system_api.NewFaqFileController().Preview)
		r.POST("/faq-files/actions/upload", system_api.NewFaqFileController().Upload)
	}
}
