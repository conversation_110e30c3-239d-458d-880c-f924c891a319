# 【Medic Crew】後端需求整合文檔 - 最終版本

## 文檔更新說明

本文檔是基於深入代碼分析後的最終需求整合版本，主要修正了以下重要發現：

### 重要修正

1. **GST功能現狀澄清**：
   - 原先認為系統中沒有GST實現，經深入分析發現系統已有完整的GST/稅金處理機制
   - 現有實現包括：10%固定稅率、發票/確認通知單GST計算、PDF顯示等
   - 需求應聚焦於Professional端的GST註冊狀態管理

2. **ABN功能現狀確認**：
   - 系統已有完整的ABN驗證機制
   - 包括澳洲ABN API整合、實體名稱匹配、狀態檢查等
   - 功能已相當完善，無需大幅修改

3. **銀行資料功能現狀**：
   - 系統已有Professional銀行帳戶管理功能
   - 包括銀行分行、帳戶號碼、帳戶名稱等字段
   - 基礎功能完備

## 最終需求整合

### 核心功能模塊

#### 1. Professional Profile功能增強（11.5小時）
- Areas of Experience功能合併（4小時）
- 個人資訊語言功能（2小時）
- 畢業情況輸入（3小時）
- Experience Level選擇（2.5小時）

#### 2. Facility Job功能增強（14小時）
- Allowances輸入功能（5小時）
- Personal Care Worker資格要求調整（3小時）
- Shift Date & Time結構修改（6小時）

#### 3. Master Data管理（6小時）
- Allowances主檔案（4小時）
- Professional GST狀態管理（2小時）

#### 4. 其他功能（4小時）
- Medication Endorsement（2小時）
- Preferred Grade文件上傳校驗（2小時）

### 總開發時間
**35.5小時**

## 開發優先級

### 第一階段（高優先級）- 18小時
1. Areas of Experience功能合併（4小時）
2. Allowances輸入功能（5小時）
3. Personal Care Worker資格要求調整（3小時）
4. Shift Date & Time結構修改（6小時）

### 第二階段（中優先級）- 11小時
1. 個人資訊語言功能（2小時）
2. 畢業情況輸入（3小時）
3. Allowances主檔案（4小時）
4. Professional GST狀態管理（2小時）

### 第三階段（低優先級）- 6.5小時
1. Experience Level選擇（2.5小時）
2. Medication Endorsement（2小時）
3. Preferred Grade文件上傳校驗（2小時）

## 技術實現重點

### GST狀態管理實現
```sql
-- 數據庫修改
ALTER TABLE professional ADD COLUMN gst_registered BOOLEAN DEFAULT FALSE COMMENT 'GST註冊狀態';
ALTER TABLE professional ADD COLUMN gst_number VARCHAR(20) NULL COMMENT 'GST註冊號碼';
```

### 與現有系統整合
- 利用現有ABN驗證機制
- 整合現有稅金計算邏輯
- 保持現有API設計模式

## 風險評估

### 高風險
- Shift Date & Time結構修改：影響現有Job功能

### 中風險
- Areas of Experience功能合併：多專業類型邏輯調整
- GST狀態管理：與現有稅金邏輯整合

### 低風險
- 語言功能、畢業情況等：相對獨立的功能增強

## 後續行動

1. **確認GST狀態管理需求**：與業務方確認Professional GST註冊狀態的具體管理方式
2. **詳細設計**：為每個功能模塊制定詳細的技術設計方案
3. **測試計劃**：制定完整的測試計劃，確保功能質量
4. **分階段實施**：按優先級分階段實施，確保項目進度

---

**文檔版本**：最終版本  
**更新日期**：2024年12月  
**更新說明**：基於深入代碼分析修正GST功能理解，提供準確的需求整合