package model

import (
	"time"

	"github.com/Norray/xrocket/xmodel"
	"github.com/shopspring/decimal"
)

// 職位狀態常量
const (
	JobPositionProfessionMedicalPractitioner = "MEDICAL_PRACTITIONER" // 醫療從業員
	JobPositionProfessionEnrolledNurse       = "ENROLLED_NURSE"       // 登記護士
	JobPositionProfessionRegisteredNurse     = "REGISTERED_NURSE"     // 註冊護士
	JobPositionProfessionPersonalCareWorker  = "PERSONAL_CARE_WORKER" // 個人護理員

	JobScheduleTemplateY = "Y" // 工作發佈計劃模板
	JobScheduleTemplateN = "N" // 正式職位

	JobSupervisionFullySupervised     = "FULLY_SUPERVISED"     // 全監督
	JobSupervisionPartiallySupervised = "PARTIALLY_SUPERVISED" // 部分監督
	JobSupervisionUnsupervised        = "NO_SUPERVISION"       // 無監督

	// 不拆分、按日期拆分、按班次拆分
	JobSplitTypeNo    = "NO"    // 不拆分
	JobSplitTypeDate  = "DATE"  // 按日期拆分
	JobSplitTypeShift = "SHIFT" // 按班次拆分

	JobBreakTimePayableY = "Y" // 支付
	JobBreakTimePayableN = "N" // 不支付

	JobShiftAllocationAutomatic = "AUTOMATIC" // 自動分配
	JobShiftAllocationManual    = "MANUAL"    // 手動分配

	JobStatusPending  = "PENDING"  // 未發佈 - 創建階段，工作信息尚未完整
	JobStatusPublish  = "PUBLISH"  // 已發佈 - 工作職位已發布到平台，可接收應聘申請
	JobStatusDisable  = "DISABLE"  // 已禁用 - 工作職位已禁用，無法接收應聘申請，可恢復PUBLISH狀態
	JobStatusComplete = "COMPLETE" // 已完成 - 所有班次已完成，所有應聘者賬單確認收款後
	JobStatusCancel   = "CANCEL"   // 已取消 - 因各種原因取消工作

	JobPaymentTermsPayInArrears = "PAY_IN_ARREARS" // 事後支付
	JobPaymentTermsPayUpfront   = "PAY_UPFRONT"    // 預付款
)

// Job 工作職位
type Job struct {
	Id                  uint64          `json:"id" gorm:"primary_key"`                                        // 主鍵
	FacilityId          uint64          `json:"facilityId" gorm:"index:facility_idx;not null"`                // 所屬機構Id
	FacilityProfileId   uint64          `json:"facilityProfileId" gorm:"index:facility_profile_idx;not null"` // 所屬機構ProfileId
	CreatedUserId       uint64          `json:"createdUserId" gorm:"index:created_user_idx;not null"`         // 創建者Id
	UpdatedUserId       uint64          `json:"updatedUserId" gorm:"index:updated_user_idx;not null"`         // 更新者Id
	JobScheduleId       uint64          `json:"jobScheduleId" gorm:"index:jobSchedule_idx;not null"`          // 工作發佈計劃Id 如果由模板發佈，則為模板Id
	ScheduleTemplate    string          `json:"scheduleTemplate" gorm:"type:varchar(1);not null"`             // 是否為模板 Y N
	ServiceLocationId   uint64          `json:"serviceLocationId" gorm:"index:service_location_idx;not null"` // 服務地點Id
	JobNo               string          `json:"jobNo" gorm:"type:varchar(32);uniqueIndex;not null"`           // 工作編號
	PositionProfession  string          `json:"positionProfession" gorm:"type:varchar(255);not null"`         // 職位專業 MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER
	NumberOfPeople      int32           `json:"numberOfPeople" gorm:"type:int;not null"`                      // 所需人數
	MinExperienceLevel  string          `json:"minExperienceLevel" gorm:"type:varchar(32);not null"`          // 最低職級要求 selection_type EXPERIENCE_LEVEL_MEDICAL_PRACTITIONER(Profession = Medical Practitioner) EXPERIENCE_LEVEL_REGISTERED_NURSE(Profession = Registered Nurse) 其他專業不填
	PreferredGrade      string          `json:"preferredGrade" gorm:"type:varchar(32);not null"`              // 首選級別 selection_type PREFERRED_GRADE(Profession = Medical Practitioner) 其他專業不填
	Qualification       string          `json:"qualification" gorm:"type:varchar(1024);not null"`             // 護理資格 selection_type PROFESSIONAL_PERSONAL_CARE_WORKER_QUALIFICATION(Profession = Personal Care Worker)
	Specialisation      string          `json:"specialisation" gorm:"type:varchar(64);not null"`              // 專業要求 只能選擇一個專業類型
	Language            string          `json:"language" gorm:"type:varchar(1024);not null"`                  // 語言要求 ENGLISH MANDARIN CANTONESE VIETNAMESE ARABIC HINDI KOREAN JAPANESE
	LanguageRequirement string          `json:"languageRequirement" gorm:"type:varchar(1);not null"`          // 語言要求類型 Y=必須 N=可選
	SupervisionLevel    string          `json:"supervisionLevel" gorm:"type:varchar(32);not null"`            // 監督級別 FULLY_SUPERVISED PARTIALLY_SUPERVISED NO_SUPERVISION
	MinHourlyRate       decimal.Decimal `json:"minHourlyRate" gorm:"type:decimal(10,2);not null"`             // 最低時薪
	MaxHourlyRate       decimal.Decimal `json:"maxHourlyRate" gorm:"type:decimal(10,2);not null"`             // 最高時薪
	SplitType           string          `json:"splitType" gorm:"type:varchar(32);not null"`                   // 分拆(草稿暫存) NO DATE SHIFT
	Duration            decimal.Decimal `json:"duration" gorm:"type:decimal(10,2);not null"`                  // 總工作時長（小時）
	PayHours            decimal.Decimal `json:"payHours" gorm:"type:decimal(10,2);not null"`                  // 支付時長（小時）
	ShiftAllocation     string          `json:"shiftAllocation" gorm:"type:varchar(32);not null"`             // 班次分配方式 AUTOMATIC MANUAL
	ShiftTimeType       string          `json:"shiftTimeType" gorm:"type:varchar(32);not null"`               // 將工作所涵蓋的 AM PM Night 用逗號拼接,要按順序 AM,PM,NIGHT
	WeekdayType         string          `json:"weekdayType" gorm:"type:varchar(32);not null"`                 // 將工作所涵蓋的日期裡包含星期幾,用逗號拼接,要按順序，例如 1,2,3,4,5,6,7
	Remark              string          `json:"remark" gorm:"type:varchar(1024);not null"`                    // 備註
	CalendarNote        string          `json:"calendarNote" gorm:"type:text;not null"`                       // 日曆備註
	CancelReason        string          `json:"cancelReason" gorm:"type:varchar(1024);not null"`              // 取消原因
	Status              string          `json:"status" gorm:"type:varchar(32);not null"`                      // 職位狀態 (PENDING, PUBLISH, IN_PROGRESS, COMPLETE, CANCEL)
	PaymentTerms        string          `json:"paymentTerms" gorm:"type:varchar(32);not null"`                // 付款條件 PAY_IN_ARREARS PAY_UPFRONT
	AcceptedCount       int32           `json:"acceptedCount" gorm:"not null"`                                // 已確認人數
	BeginTime           *time.Time      `swaggertype:"string" json:"beginTime" gorm:"type:datetime(0)"`       // 工作最早開始時間
	EndTime             *time.Time      `swaggertype:"string" json:"endTime" gorm:"type:datetime(0)"`         // 工作最晚結束時間
	PublishTime         *time.Time      `swaggertype:"string" json:"publishTime" gorm:"type:datetime(0)"`     // 發佈時間
	PublishNow          string          `json:"publishNow" gorm:"type:varchar(1);not null"`                   // 是否立即發佈 Y N
	CreateTime          time.Time       `json:"createTime" gorm:"type:datetime(0);not null"`                  // 創建時間
	UpdateTime          *time.Time      `swaggertype:"string" json:"updateTime" gorm:"type:datetime(0)"`      // 更新時間
	xmodel.Model
}

func (Job) TableName() string {
	return "job"
}

func (Job) SwaggerDescription() string {
	return "工作職位信息"
}
