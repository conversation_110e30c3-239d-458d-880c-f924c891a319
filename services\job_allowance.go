package services

import (
	"github.com/Norray/medic-crew/model"
	"gorm.io/gorm"
)

type jobAllowanceService struct{}

var JobAllowanceService = new(jobAllowanceService)

func (s *jobAllowanceService) GetAllowance(db *gorm.DB, jobId uint64, jobShiftId []uint64) ([]model.JobAllowance, error) {
	var jobAllowances []model.JobAllowance
	builder := db.Model(&model.JobAllowance{}).
		Where("job_id = ?", jobId).
		Where("job_shift_id IN (?)", jobShiftId).
		Order("job_id ASC").
		Order("job_shift_id ASC").
		Order("allowance_id ASC")

	if err := builder.Find(&jobAllowances).Error; err != nil {
		return nil, err
	}
	return jobAllowances, nil
}
