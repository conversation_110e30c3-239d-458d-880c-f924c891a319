package services

import (
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gorm.io/gorm"
)

var FacilityBlacklistService = new(facilityBlacklistService)

type facilityBlacklistService struct{}

func (s *facilityBlacklistService) CheckIdExist(db *gorm.DB, m *model.FacilityBlacklist, facilityId uint64, id uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_blacklist.id.does_not_exist",
		Other: "No such record, please try after reloading.",
	}
	var err error
	if err = db.Where("facility_id = ?", facilityId).First(&m, id).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

// 可否加入黑名單
func (s *facilityBlacklistService) CanAddBlacklist(db *gorm.DB, facilityId uint64, userId uint64, facilityBlacklistId ...uint64) (bool, i18n.Message, error) {
	var err error
	// 檢查專業人士是否已經在黑名單中
	msg := i18n.Message{
		ID:    "checker.facility_blacklist.already_in_blacklist",
		Other: "The professional is already in the blacklist.",
	}
	var m model.FacilityBlacklist
	builder := db.Where("facility_id = ? AND user_id = ?", facilityId, userId)
	if len(facilityBlacklistId) > 0 {
		builder = builder.Where("id != ?", facilityBlacklistId[0])
	}
	if err = builder.First(&m).Error; xgorm.IsSqlErr(err) {
		return false, i18n.Message{}, err
	}
	if xgorm.IsNotFoundErr(err) {
		return true, i18n.Message{}, nil
	} else {
		return false, msg, nil
	}
}

type FacilityBlacklistListReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"`
	Name       string `form:"name"`
	Profession string `form:"profession" binding:"omitempty,oneof=MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER"` // 職業 MEDICAL_PRACTITIONER=醫生 ENROLLED_NURSE=註冊護士 REGISTERED_NURSE=登記護士 PERSONAL_CARE_WORKER=照顧員
}

type FacilityBlacklistListResp struct {
	FacilityBlacklistId uint64 `json:"facilityBlacklistId"`
	FacilityId          uint64 `json:"facilityId"`
	UserId              uint64 `json:"userId"`
	ProfessionalId      uint64 `json:"professionalId"`
	ProfessionalPhotoId uint64 `json:"professionalPhotoId"`
	FirstName           string `json:"firstName"`
	LastName            string `json:"lastName"`
	Profession          string `json:"profession"`
	Reason              string `json:"reason"`
}

func (s *facilityBlacklistService) List(db *gorm.DB, req FacilityBlacklistListReq, pageSet *xresp.PageSet) ([]FacilityBlacklistListResp, error) {
	var err error
	var resp []FacilityBlacklistListResp
	builder := db.Table("facility_blacklist AS fb").
		Joins("JOIN professional AS p ON fb.professional_id = p.id").
		Joins("JOIN professional_file_relation AS pfr ON pfr.professional_id = p.id ").
		Joins("JOIN professional_file AS pf ON pfr.professional_file_id = pf.id AND pf.user_id = p.user_id AND pf.file_code = ?", model.ProfessionalFileCodePhoto).
		Joins("JOIN user AS u ON u.id = p.user_id").
		Select([]string{
			"fb.id AS facility_blacklist_id",
			"fb.facility_id",
			"u.id AS user_id",
			"fb.professional_id",
			"pf.id AS professional_photo_id",
			"p.first_name",
			"p.last_name",
			"p.profession",
			"fb.reason",
		}).
		Where("fb.facility_id = ?", req.FacilityId)
	if req.Profession != "" {
		builder = builder.Where("p.profession = ?", req.Profession)
	}
	if req.Name != "" {
		builder = builder.Where("(p.first_name LIKE ? OR p.last_name LIKE ?) OR (CONCAT(p.first_name, ' ', p.last_name) LIKE ?)", "%"+req.Name+"%", "%"+req.Name+"%", "%"+req.Name+"%")
	}
	if err = builder.
		Group("fb.id").
		Scopes(xresp.Paginate(pageSet)).
		Order("fb.id").
		Find(&resp).Error; err != nil {
		return resp, err
	}

	var professionSectionMap map[string]string
	if len(resp) > 0 {
		professionSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
		if err != nil {
			return resp, err
		}
	}
	for i := range resp {
		resp[i].Profession = professionSectionMap[resp[i].Profession]
	}
	return resp, nil
}

type FacilityBlacklistCreateReq struct {
	FacilityId     uint64 `json:"facilityId" binding:"required"`
	ProfessionalId uint64 `json:"professionalId" binding:"required"`
	Reason         string `json:"reason" binding:"required,max=255"`
	UserId         uint64 `json:"-"`
}

type FacilityBlacklistCreateResp struct {
	FacilityBlacklistId uint64 `json:"facilityBlacklistId"`
}

func (s *facilityBlacklistService) Create(db *gorm.DB, req FacilityBlacklistCreateReq) (FacilityBlacklistCreateResp, error) {
	var resp FacilityBlacklistCreateResp
	var err error
	var m model.FacilityBlacklist
	_ = copier.Copy(&m, req)
	if err = db.Create(&m).Error; err != nil {
		return resp, err
	}
	resp.FacilityBlacklistId = m.Id
	return resp, nil
}

type FacilityBlacklistEditReq struct {
	FacilityBlacklistId uint64 `json:"facilityBlacklistId" binding:"required"`
	FacilityId          uint64 `json:"facilityId" binding:"required"`
	Reason              string `json:"reason" binding:"required,max=255"`
}

func (s *facilityBlacklistService) Edit(db *gorm.DB, req FacilityBlacklistEditReq) error {
	var err error
	var m model.FacilityBlacklist
	if err = db.First(&m, req.FacilityBlacklistId).Error; err != nil {
		return err
	}
	updateMap := map[string]interface{}{
		"reason": req.Reason,
	}
	if err = db.Model(&m).Where("id = ? AND facility_id = ?", req.FacilityBlacklistId, req.FacilityId).Updates(updateMap).Error; err != nil {
		return err
	}
	return nil
}

type FacilityBlacklistDeleteReq struct {
	FacilityId          uint64 `json:"facilityId" binding:"required"`
	FacilityBlacklistId uint64 `json:"facilityBlacklistId"  binding:"required"`
}

func (s *facilityBlacklistService) Delete(db *gorm.DB, req FacilityBlacklistDeleteReq) error {
	var err error
	if err = db.Where("id = ? AND facility_id = ?", req.FacilityBlacklistId, req.FacilityId).Delete(&model.FacilityBlacklist{}).Error; err != nil {
		return err
	}
	return nil
}
