package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/Norray/xrocket/xamqp"
	uuid "github.com/satori/go.uuid"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	CreateInvoiceDraftTask           = "create_invoice_draft_task" // 生成發票草稿任務
	JobApplicationProgressUpcoming   = "UPCOMING"                  // 待開始
	JobApplicationProgressInProgress = "IN_PROGRESS"               // 進行中
	JobApplicationProgressApplied    = "APPLIED"                   // 已申請
	JobApplicationProgressComplete   = "COMPLETE"                  // 已完成
	JobApplicationProgressCancel     = "CANCEL"                    // 已取消

	GenerateCompensationConfirmationNoteTask = "generate_compensation_confirmation_note_task"
)

// 工作職位申請服務
var JobApplicationService = new(jobApplicationService)

type jobApplicationService struct{}

// 檢查工作職位申請ID是否存在
func (s *jobApplicationService) CheckIdExist(db *gorm.DB, m *model.JobApplication, id uint64, userId ...uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job_application.id.does_not_exist",
		Other: "No such application record, please try after reloading.",
	}
	var err error
	builder := db.Model(&model.JobApplication{}).Where("id = ?", id)
	if len(userId) > 0 {
		builder = builder.Where("user_id = ?", userId[0])
	}
	if err = builder.First(&m).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

func (s *jobApplicationService) CheckIdExistByFacility(db *gorm.DB, m *model.JobApplication, id uint64, facilityId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job_application.id.does_not_exist",
		Other: "No such application record, please try after reloading.",
	}
	var err error
	builder := db.Model(&model.JobApplication{}).Where("facility_id = ?", facilityId).Where("id = ?", id)
	if err = builder.First(&m).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

// 檢查機構是否存在專業人士的申請記錄
func (s *jobApplicationService) CheckFacilityProfessionalApplication(db *gorm.DB, facilityId uint64, professionalId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job_application.id.does_not_exist",
		Other: "No such application record, please try after reloading.",
	}
	var err error
	if err = db.
		Where("facility_id = ? AND professional_id = ?", facilityId, professionalId).
		Where("status IS NOT NULL AND status <> ?", model.JobApplicationStatusWithdraw).
		First(&model.JobApplication{}).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

// 檢查機構是否存在專業人士的申請記錄
func (s *jobApplicationService) CheckFacilityProfessionalApplicationByUserId(db *gorm.DB, facilityId uint64, professionalUserId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job_application.id.does_not_exist",
		Other: "No such application record, please try after reloading.",
	}
	var err error
	if err = db.
		Where("facility_id = ? AND user_id = ?", facilityId, professionalUserId).
		Where("status IS NOT NULL AND status <> ?", model.JobApplicationStatusWithdraw).
		First(&model.JobApplication{}).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

// 工作職位申請列表請求
type JobApplicationListForFacilityReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"`                                                                                                                                  // 所屬機構Id
	JobId      uint64 `form:"jobId" binding:"required"`                                                                                                                                       // 工作職位Id
	Status     string `form:"status" binding:"omitempty,oneof=APPLY CHATTING WITHDRAW INVITE REJECT DECLINE ACCEPT UPCOMING APPLICATION_CANCEL FACILITY_CANCEL PROFESSIONAL_CANCEL COMPLETE"` // 申請狀態 APPLY: 申請 CHATTING: 聊天 WITHDRAW: 撤銷 INVITE: 邀請 REJECT: 拒絕 DECLINE: 拒絕 ACCEPT: 接受 UPCOMING: 即將開始 APPLICATION_CANCEL FACILITY_CANCEL: 機構取消 PROFESSIONAL_CANCEL: 專業人員取消 COMPLETE: 完成
	Accept     string `form:"accept" binding:"omitempty,oneof=Y N"`                                                                                                                           // 是否已經取錄
}

func (req *JobApplicationListForFacilityReq) GetBaseReq() JobApplicationListBaseReq {
	var resp JobApplicationListBaseReq
	_ = copier.Copy(&resp, req)
	return resp
}

type JobApplicationListForSystemReq struct {
	UserId     uint64 `form:"userId"`                                                                                                                                                         // 用戶Id
	FacilityId uint64 `form:"facilityId"`                                                                                                                                                     // 機構Id
	JobId      uint64 `form:"jobId"`                                                                                                                                                          // 工作職位Id
	Status     string `form:"status" binding:"omitempty,oneof=APPLY CHATTING WITHDRAW INVITE REJECT DECLINE ACCEPT UPCOMING APPLICATION_CANCEL FACILITY_CANCEL PROFESSIONAL_CANCEL COMPLETE"` // 申請狀態 APPLY: 申請 CHATTING: 聊天 WITHDRAW: 撤銷 INVITE: 邀請 REJECT: 拒絕 DECLINE: 拒絕 ACCEPT: 接受 UPCOMING: 即將開始 APPLICATION_CANCEL FACILITY_CANCEL: 機構取消 PROFESSIONAL_CANCEL: 專業人員取消 COMPLETE: 完成
	Accept     string `form:"accept" binding:"omitempty,oneof=Y N"`                                                                                                                           // 是否已經取錄
}

func (req *JobApplicationListForSystemReq) GetBaseReq() JobApplicationListBaseReq {
	var resp JobApplicationListBaseReq
	_ = copier.Copy(&resp, req)
	return resp
}

type JobApplicationListBaseReq struct {
	UserId     uint64
	FacilityId uint64
	JobId      uint64
	Status     string
	Accept     string
}

// 工作職位申請列表響應
type JobApplicationListResp struct {
	ApplicationId       uint64 `json:"applicationId"`       // 申請Id
	FacilityId          uint64 `json:"facilityId"`          // 機構Id
	JobId               uint64 `json:"jobId"`               // 工作Id
	ProfessionalId      uint64 `json:"professionalId"`      // 專業人員Id
	FirstName           string `json:"firstName"`           // 專業人員名字
	LastName            string `json:"lastName"`            // 專業人員姓氏
	ProfessionalPhotoId uint64 `json:"professionalPhotoId"` // 專業人員照片Id
	Score               int    `json:"score"`               // 匹配分數
	Status              string `json:"status"`              // 申請狀態 APPLY: 申請 CHATTING: 聊天 WITHDRAW: 撤銷 INVITE: 邀請 REJECT: 拒絕 DECLINE: 拒絕 ACCEPT: 接受 UPCOMING: 即將開始 APPLICATION_CANCEL FACILITY_CANCEL: 機構取消 PROFESSIONAL_CANCEL: 專業人員取消 COMPLETE: 完成
	Accept              string `json:"accept"`              // 是否已經取錄
	ApplyTime           string `json:"applyTime"`           // 申請時間
}

// 獲取工作職位申請列表 (機構)
func (s *jobApplicationService) List(db *gorm.DB, req JobApplicationListBaseReq, pageSet *xresp.PageSet, sortSet xresp.SortingSet) ([]JobApplicationListResp, error) {
	var resp []JobApplicationListResp

	var job model.Job
	if err := db.Where("facility_id = ?", req.FacilityId).Where("id = ?", req.JobId).First(&job).Error; err != nil {
		return resp, err
	}

	var facilityProfile model.FacilityProfile
	if err := db.Where("facility_id = ?", req.FacilityId).
		Where("status = ?", model.FacilityProfileStatusApproved).
		Where("id = ?", job.FacilityProfileId).
		First(&facilityProfile).Error; err != nil {
		return resp, err
	}

	builder := db.Table("job_application AS ja").
		Joins("JOIN job AS j ON ja.job_id = j.id").
		Joins("JOIN professional AS p ON ja.professional_id = p.id").
		Joins("JOIN user AS u ON p.user_id = u.id").
		Joins("JOIN professional_file_relation AS pfr ON pfr.professional_id = p.id ").
		Joins("JOIN professional_file AS pf ON pfr.professional_file_id = pf.id AND p.user_id = u.id AND pf.file_code = ?", model.ProfessionalFileCodePhoto).
		Select([]string{
			"ja.id AS application_id",
			"ja.facility_id",
			"ja.job_id",
			"ja.professional_id",
			"p.first_name",
			"p.last_name",
			"pf.id AS professional_photo_id",
			"ja.score",
			"ja.status",
			"ja.accept",
			"ja.apply_time",
		}).
		Where("ja.deleted <> ?", model.JobApplicationDeletedY)
	if req.UserId > 0 {
		builder = builder.Where("ja.user_id = ?", req.UserId)
	}
	if req.JobId > 0 {
		builder = builder.Where("ja.job_id = ?", req.JobId)
	}
	if req.FacilityId > 0 {
		builder = builder.Where("ja.facility_id = ?", req.FacilityId)
	}
	// 如果指定了狀態，則按狀態過濾
	if req.Status != "" {
		builder = builder.Where("ja.status = ?", req.Status)
	}

	if req.Accept != "" {
		if req.Accept == model.JobApplicationAcceptY {
			builder = builder.Where("ja.accept = ?", req.Accept).
				Where("ja.status = ?", model.JobApplicationStatusAccept)
		} else {
			builder = builder.Where("ja.accept = ? OR ja.status != ?", req.Accept, model.JobApplicationStatusAccept)
		}
	}
	if job.ShiftAllocation == model.JobShiftAllocationAutomatic {
		// 排序：1.發佈後兩小時內的排前, 2.發佈後兩小時外的排後
		builder = builder.
			Where("DATE_ADD(j.publish_time, INTERVAL 2 HOUR) <= ?", time.Now().UTC().Truncate(time.Second)).
			Order("CASE WHEN ja.apply_time <= DATE_ADD(j.publish_time, INTERVAL 2 HOUR) THEN 1 ELSE 0 END DESC").
			Order("ja.score DESC").
			Order("ja.apply_time ASC").
			Limit(int(job.NumberOfPeople))
	}

	// 排序字段
	sortKeyList := map[string]string{
		"score": "ja.score",
	}

	if err := builder.
		Group("ja.id").
		Scopes(xresp.AddOrder(sortSet, sortKeyList)).
		Order("ja.apply_time ASC").
		Scopes(xresp.Paginate(pageSet)).
		Find(&resp).Error; err != nil {
		return resp, err
	}

	return resp, nil
}

// JobApplicationInquireReq 查詢工作職位申請詳情請求
type JobApplicationInquireForFacilityReq struct {
	FacilityId       uint64 `form:"facilityId" binding:"required"`       // 所屬機構Id
	JobApplicationId uint64 `form:"jobApplicationId" binding:"required"` // 申請Id
}

func (req *JobApplicationInquireForFacilityReq) GetBaseReq() JobApplicationInquireBaseReq {
	var resp JobApplicationInquireBaseReq
	_ = copier.Copy(&resp, req)
	return resp
}

type JobApplicationInquireForSystemReq struct {
	JobApplicationId uint64 `form:"jobApplicationId" binding:"required"` // 申請Id
}

func (req *JobApplicationInquireForSystemReq) GetBaseReq() JobApplicationInquireBaseReq {
	var resp JobApplicationInquireBaseReq
	_ = copier.Copy(&resp, req)
	return resp
}

type JobApplicationInquireBaseReq struct {
	JobApplicationId uint64
}

// JobApplicationInquireResp 查詢工作職位申請詳情響應
type JobApplicationInquireResp struct {
	ApplicationId       uint64 `json:"applicationId"`       // 申請Id
	FacilityId          uint64 `json:"facilityId"`          // 機構Id
	JobId               uint64 `json:"jobId"`               // 工作Id
	ProfessionalId      uint64 `json:"professionalId"`      // 專業人員Id
	ProfessionalPhotoId uint64 `json:"professionalPhotoId"` // 專業人員照片Id
	Score               int    `json:"score"`               // 匹配分數
	Status              string `json:"status"`              // 申請狀態
	ApplyTime           string `json:"applyTime"`           // 申請時間

	// 專業人員信息
	FirstName            string `json:"firstName"`            // 專業人員名字
	LastName             string `json:"lastName"`             // 專業人員姓氏
	PermissionToWork     string `json:"permissionToWork"`     // 工作許可
	PermissionToWorkName string `json:"permissionToWorkName"` // 工作許可名稱

	Profession     string `json:"profession"`     // 專業
	ProfessionName string `json:"professionName"` // 專業名稱
	Gender         string `json:"gender"`         // 性別
	GenderName     string `json:"genderName"`     // 性別名稱
	DateOfBirth    string `json:"dateOfBirth"`    // 出生日期

	PreferredSpecialities                []ProfessionalPreferredSpecialityDetail                `json:"preferredSpecialities"`                                              // 首選專業
	ExperienceLevel                      string                                                 `json:"experienceLevel"`                                                    // 經驗級別
	ExperienceLevelName                  string                                                 `json:"experienceLevelName"`                                                // 經驗級別名稱
	SupervisionRequirement               string                                                 `json:"supervisionRequirement"`                                             // 監督要求
	SupervisionRequirementName           string                                                 `json:"supervisionRequirementName"`                                         // 監督要求名稱
	AbnNumber                            string                                                 `json:"abnNumber"`                                                          // ABN號碼
	Experiences                          []JobApplicationInquireRespExperience                  `json:"experiences"`                                                        // 工作經驗
	CompletedStudiesInLastThreeYears     string                                                 `json:"completedStudiesInLastThreeYears" binding:"omitempty,oneof=Y N"`     // 過去三年內完成學習 Y/N
	Qualification                        string                                                 `json:"qualification"`                                                      // 學歷
	QualificationName                    string                                                 `json:"qualificationName"`                                                  // 學歷名稱
	QualificationEndDate                 string                                                 `json:"qualificationEndDate" binding:"omitempty,datetime=2006-01-02"`       // 學歷結束日期(YYYY-MM-DD)
	HasOverseasCitizenshipOrPr           string                                                 `json:"hasOverseasCitizenshipOrPr" binding:"omitempty,oneof=Y N"`           // 是否擁有海外公民身份或永久居留權 Y/N
	RequiresStatutoryDeclaration         string                                                 `json:"requiresStatutoryDeclaration" binding:"omitempty,oneof=Y N"`         // 是否需要法定聲明 Y/N
	HasCompletedInfectionControlTraining string                                                 `json:"hasCompletedInfectionControlTraining" binding:"omitempty,oneof=Y N"` // 是否完成感染控制培訓 Y/N
	Files                                map[string][]FacilityAccessProfessionalProfileFileInfo `json:"files" gorm:"-"`                                                     // 文件
	FacilityBlacklistId                  uint64                                                 `json:"facilityBlacklistId"`                                                // 機構黑名單Id
}
type JobApplicationInquireRespExperience struct {
	model.ProfessionalExperience
	RoleName          string `json:"roleName"`                    // 角色名稱
	SpecialityName    string `json:"specialityName"`              // 專業名稱
	SubSpecialityName string `json:"subSpecialityName,omitempty"` // 子專業名稱(僅醫生才有)
	GradeName         string `json:"gradeName,omitempty"`         // 級別名稱(僅醫生才有)
}

// 機構訪問專業人員文件類型
var FacilityAccessProfessionalFileTypeMap = map[string]bool{
	model.ProfessionalFileCodePhoto:                                                             true,
	model.ProfessionalFileCodeAhpraCertificate:                                                  true,
	model.ProfessionalFileCodePersonalCareWorkQualification:                                     true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationExperienceAgedCareDisability:       true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIAGEDCARE:             true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationCertIIIDisabilities:                true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationCertIIIIndividualSupport:           true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationCertIIIIndividualSupportAgedAged:   true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationCertIIIIndividualSupportDisability: true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIHomeCommunityCare:    true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationCertificateIVAGEDCARE:              true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationCertificateIVDisabilities:          true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationCertificateIVHomeCommunityCare:     true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationDegreeAlliedHealth:                 true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationDegreeNursing:                      true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationOtherRelevant:                      true,
	model.ProfessionalFileCodePersonalCareWorkerQualificationWorkingTowardNursing:               true,
	model.ProfessionalFileCodeIdCheck:                                                           true,
	model.ProfessionalFileCodeAustralianPassport:                                                true,
	model.ProfessionalFileCodeForeignPassport:                                                   true,
	model.ProfessionalFileCodeAustralianBirthCertificate:                                        true,
	model.ProfessionalFileCodeAustralianCitizenshipCertificate:                                  true,
	model.ProfessionalFileCodeCurrentAustraliaDriverLicence:                                     true,
	model.ProfessionalFileCodeAustralianPublicServiceEmployeeIDCard:                             true,
	model.ProfessionalFileCodeOtherAustralianGovernmentIssueIDCard:                              true,
	model.ProfessionalFileCodeTertiaryStudentIDCard:                                             true,
	model.ProfessionalFileCodeCreditDebitAtmCard:                                                true,
	model.ProfessionalFileCodeMedicareCard:                                                      true,
	model.ProfessionalFileCodeUtilityBillOrRateNotice:                                           true,
	model.ProfessionalFileCodeStatementFromFinancialInstitution:                                 true,
	model.ProfessionalFileCodeCentrelinkOrPensionCard:                                           true,
	model.ProfessionalFileCodeVisa:                                                              true,
	model.ProfessionalFileCodeNationalCriminalCheck:                                             true,
	model.ProfessionalFileCodeWorkingWithChildrenOrVulnerablePeople:                             true,
	model.ProfessionalFileCodeCurrentImmunisationRecords:                                        true,
	model.ProfessionalFileCodeCommonwealthStatutoryDeclaration:                                  true,
	model.ProfessionalFileCodeAdditionalCertification:                                           true,
	model.ProfessionalFileCodeQualificationCertificate:                                          true,
	model.ProfessionalFileCodeRegistrarAccreditedEnrolment:                                      true,
	model.ProfessionalFileCodeFellowshipCertificate:                                             true,
	model.ProfessionalFileCodeSpecialistQualification:                                           true,
}

// 專業人員文件信息
type FacilityAccessProfessionalProfileFileInfo struct {
	ProfessionalFiles []FacilityAccessProfessionalProfileFileDetail `json:"professionalFiles"`     // 文件信息
	Description       string                                        `json:"description,omitempty"` // 描述
}
type FacilityAccessProfessionalProfileFileDetail struct {
	ProfessionalFileId uint64 `json:"professionalFileId"` // 文件ID
	UuidName           string `json:"filename"`           // UUID文件名稱
}

// Inquire 機構查詢工作職位申請詳情
func (s *jobApplicationService) Inquire(db *gorm.DB, req JobApplicationInquireBaseReq) (JobApplicationInquireResp, error) {
	var resp JobApplicationInquireResp

	// 查詢申請記錄
	var application model.JobApplication
	if err := db.First(&application, req.JobApplicationId).Error; err != nil {
		return resp, err
	}

	// 複製基本申請信息
	_ = copier.Copy(&resp, application)
	resp.ApplicationId = application.Id

	// 查詢專業人員信息
	profile, err := ProfessionalProfileService.Inquire(db, ProfessionalProfileInquireReq{
		ProfessionalId: application.ProfessionalId,
	})
	if err != nil {
		return resp, err
	}

	// 複製專業人員信息 忽略狀態
	err = xtool.CopyWithIgnoreFieldNames(&resp, profile, "Status")
	if err != nil {
		return resp, err
	}

	// 過濾機構訪問專業人員文件類型
	for key := range resp.Files {
		if _, ok := FacilityAccessProfessionalFileTypeMap[key]; !ok {
			delete(resp.Files, key)
			continue
		}
	}

	// 獲取各種選項的名稱映射
	specialityCodes := make([]string, 0)
	for _, speciality := range resp.PreferredSpecialities {
		specialityCodes = append(specialityCodes, speciality.Speciality)
	}
	for _, exp := range resp.Experiences {
		if exp.Speciality != "" {
			specialityCodes = append(specialityCodes, exp.Speciality)
		}
		if exp.SubSpeciality != "" {
			specialityCodes = append(specialityCodes, exp.SubSpeciality)
		}
	}
	specialityMap, err := SelectionService.GetMapByCodes(db, specialityCodes)
	if err != nil {
		return resp, err
	}
	var preferredGradeSectionMap map[string]string
	preferredGradeSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypePreferredGrade})
	if err != nil {
		return resp, err
	}

	var professionSectionMap map[string]string
	professionSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
	if err != nil {
		return resp, err
	}

	// 補充名稱
	if resp.PermissionToWork != "" {
		var permissionToWorkMap map[string]string
		permissionToWorkMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalPermissionToWork})
		if err != nil {
			return resp, err
		}
		resp.PermissionToWorkName = permissionToWorkMap[resp.PermissionToWork]
	}
	if resp.Profession != "" {
		resp.ProfessionName = professionSectionMap[resp.Profession]
	}
	if resp.Gender != "" {
		var genderSectionMap map[string]string
		genderSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeGender})
		if err != nil {
			return resp, err
		}
		resp.GenderName = genderSectionMap[resp.Gender]
	}
	if resp.ExperienceLevel != "" {
		var experienceLevelSectionMap map[string]string
		experienceLevelSectionMap, err = SelectionService.GetCodeNameMap(db, []string{
			model.SelectionTypeExperienceLevelMedicalPractitioner,
			model.SelectionTypeExperienceLevelRegisteredNurse,
		})
		if err != nil {
			return resp, err
		}
		resp.ExperienceLevelName = experienceLevelSectionMap[resp.ExperienceLevel]
	}
	if resp.SupervisionRequirement != "" {
		var supervisionRequirementMap map[string]string
		supervisionRequirementMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeSupervisionRequirement})
		if err != nil {
			return resp, err
		}
		resp.SupervisionRequirementName = supervisionRequirementMap[resp.SupervisionRequirement]
	}
	if resp.Qualification != "" {
		var qualificationMap map[string]string
		qualificationMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalPersonalCareWorkerQualification})
		if err != nil {
			return resp, err
		}
		resp.QualificationName = qualificationMap[resp.Qualification]
	}

	// 處理首選專業名稱
	if len(resp.PreferredSpecialities) > 0 {
		for i, _ := range resp.PreferredSpecialities {
			resp.PreferredSpecialities[i].SpecialityName = specialityMap[resp.PreferredSpecialities[i].Speciality]
			if resp.PreferredSpecialities[i].SubSpeciality != "" {
				resp.PreferredSpecialities[i].SubSpecialityName = specialityMap[resp.PreferredSpecialities[i].SubSpeciality]
			}
			if resp.PreferredSpecialities[i].Grade != "" {
				resp.PreferredSpecialities[i].GradeName = preferredGradeSectionMap[resp.PreferredSpecialities[i].Grade]
			}
		}
	}

	// 處理工作經驗的名稱
	for i, exp := range resp.Experiences {
		if exp.Speciality != "" {
			exp.SpecialityName = specialityMap[exp.Speciality]
		}
		if exp.SubSpeciality != "" {
			exp.SubSpecialityName = specialityMap[exp.SubSpeciality]
		}
		if exp.Grade != "" {
			exp.GradeName = preferredGradeSectionMap[exp.Grade]
		}
		resp.Experiences[i] = exp
	}

	// 該專業人士是否在黑名單中
	var blacklist model.FacilityBlacklist
	if err = db.
		Where("facility_id = ?", resp.FacilityId).
		Where("user_id = ?", profile.UserId).
		Select("id").
		First(&blacklist).Error; xgorm.IsSqlErr(err) {
		return resp, err
	}
	if blacklist.Id > 0 {
		resp.FacilityBlacklistId = blacklist.Id
	}
	return resp, nil
}

type MyJobListReq struct {
	FacilityId        uint64          `form:"facilityId"`                                                                                                                                                                           // 所屬機構Id
	Address           string          `form:"address"`                                                                                                                                                                              // 地址
	BeginTime         string          `form:"beginTime" binding:"omitempty,datetime=2006-01-02 15:04:05"`                                                                                                                           // 工作開始時間 (YYYY-MM-DD HH:MM:SS)
	EndTime           string          `form:"endTime" binding:"omitempty,datetime=2006-01-02 15:04:05"`                                                                                                                             // 工作結束時間 (YYYY-MM-DD HH:MM:SS)
	MinHourlyRate     decimal.Decimal `form:"minHourlyRate"`                                                                                                                                                                        // 最低時薪
	Status            string          `form:"status" binding:"required_without=Progress,omitempty,splitin=APPLY INVITE DECLINE ACCEPT APPLICATION_CANCEL WITHDRAW CHATTING APPLICATION_CANCEL FACILITY_CANCEL PROFESSIONAL_CANCEL"` // 職位狀態,可逗號分隔 已申請=APPLY,已邀請=INVITE,已婉拒=DECLINE,已接受=ACCEPT,已取消=APPLICATION_CANCEL,已撤銷=WITHDRAW,聊天中=CHATTING,機構已取消=FACILITY_CANCEL APPLICATION_CANCEL,應聘者已取消=PROFESSIONAL_CANCEL
	Progress          string          `form:"progress" binding:"required_without=Status,omitempty,oneof=UPCOMING IN_PROGRESS APPLIED COMPLETE CANCEL"`                                                                              // 進度篩選 UPCOMING=待開始 IN_PROGRESS=進行中 APPLIED=已申請 COMPLETE=已完成 CANCEL=已取消
	JobStatus         string          `form:"jobStatus" binding:"omitempty,oneof=NO_INVOICE UNPAID PARTIALLY_PAID FULLY_PAID CHATTING PENDING DECLINED CONFIRMED APPLIED_ONLY"`                                                     // 工作狀態篩選 NO_INVOICE=未開立發票 UNPAID=未付款 PARTIALLY_PAID=部分付款 FULLY_PAID=已付款 CHATTING=聊天中 PENDING=待開始 CONFIRMED=已確認 APPLIED_ONLY=只申請
	HideApplied       string          `form:"hideApplied" binding:"omitempty,oneof=Y N"`                                                                                                                                            // 是否隱藏APPLIED狀態的記錄 Y/N
	ReqUserId         uint64          `form:"-"`                                                                                                                                                                                    // 請求者Id
	CalendarBeginTime string          `form:"calendarBeginTime" binding:"omitempty,datetime=2006-01-02 15:04:05"`                                                                                                                   // 日曆開始時間(日曆才用) (YYYY-MM-DD HH:MM:SS)
	CalendarEndTime   string          `form:"calendarEndTime" binding:"omitempty,datetime=2006-01-02 15:04:05"`                                                                                                                     // 日曆結束時間(日曆才用) (YYYY-MM-DD HH:MM:SS)
	NeedShiftTime     string          `form:"needShiftTime" binding:"omitempty,oneof=Y N"`                                                                                                                                          // 是否需要班次時間 Y/N
}
type MyJobListReqBySystem struct {
	UserId            uint64          `form:"userId" binding:"required"`                                                                                                                                // 請求者Id
	FacilityId        uint64          `form:"facilityId"`                                                                                                                                               // 所屬機構Id
	Address           string          `form:"address"`                                                                                                                                                  // 地址
	BeginTime         string          `form:"beginTime" binding:"omitempty,datetime=2006-01-02 15:04:05"`                                                                                               // 工作開始時間 (YYYY-MM-DD HH:MM:SS)
	EndTime           string          `form:"endTime" binding:"omitempty,datetime=2006-01-02 15:04:05"`                                                                                                 // 工作結束時間 (YYYY-MM-DD HH:MM:SS)
	MinHourlyRate     decimal.Decimal `form:"minHourlyRate"`                                                                                                                                            // 最低時薪
	Status            string          `form:"status" binding:"omitempty,oneof=APPLY INVITE DECLINE ACCEPT APPLICATION_CANCEL WITHDRAW CHATTING APPLICATION_CANCEL FACILITY_CANCEL PROFESSIONAL_CANCEL"` // 職位狀態,可逗號分隔 已申請=APPLY,已邀請=INVITE,已婉拒=DECLINE,已接受=ACCEPT,已取消=APPLICATION_CANCEL,已撤銷=WITHDRAW,聊天中=CHATTING,機構已取消=FACILITY_CANCEL APPLICATION_CANCEL,應聘者已取消=PROFESSIONAL_CANCEL
	Progress          string          `form:"progress" binding:"omitempty,oneof=UPCOMING IN_PROGRESS APPLIED COMPLETE CANCEL"`                                                                          // 進度篩選 UPCOMING=待開始 IN_PROGRESS=進行中 APPLIED=已申請 COMPLETE=已完成 CANCEL=已取消
	JobStatus         string          `form:"jobStatus" binding:"omitempty,oneof=NO_INVOICE UNPAID PARTIALLY_PAID FULLY_PAID CHATTING PENDING CONFIRMED APPLIED_ONLY"`                                  // 工作狀態篩選 NO_INVOICE=未開立發票 UNPAID=未付款 PARTIALLY_PAID=部分付款 FULLY_PAID=已付款 CHATTING=聊天中 PENDING=待開始 CONFIRMED=已確認 APPLIED_ONLY=只申請
	HideApplied       string          `form:"hideApplied" binding:"omitempty,oneof=Y N"`                                                                                                                // 是否隱藏APPLIED狀態的記錄 Y/N
	CalendarBeginTime string          `form:"calendarBeginTime" binding:"omitempty,datetime=2006-01-02 15:04:05"`                                                                                       // 日曆開始時間(日曆才用) (YYYY-MM-DD HH:MM:SS)
	CalendarEndTime   string          `form:"calendarEndTime" binding:"omitempty,datetime=2006-01-02 15:04:05"`                                                                                         // 日曆結束時間(日曆才用) (YYYY-MM-DD HH:MM:SS)
	NeedShiftTime     string          `form:"needShiftTime" binding:"omitempty,oneof=Y N"`
}

func (req *MyJobListReqBySystem) GetBaseReq() MyJobListReq {
	var resp MyJobListReq
	_ = copier.Copy(&resp, req)
	resp.ReqUserId = req.UserId
	return resp

}

type MyJobListResp struct {
	JobApplicationId       uint64                                  `json:"jobApplicationId"`            // 工作申請Id
	JobId                  uint64                                  `json:"jobId"`                       // 工作Id
	FacilityId             uint64                                  `json:"facilityId"`                  // 所屬機構Id
	FacilityName           string                                  `json:"facilityName"`                // 機構名稱
	ServiceLocationAddress string                                  `json:"serviceLocationAddress"`      // 服務地點地址
	BeginTime              string                                  `json:"beginTime"`                   // 工作開始時間 (YYYY-MM-DD HH:MM:SS)
	EndTime                string                                  `json:"endTime"`                     // 工作結束時間 (YYYY-MM-DD HH:MM:SS)
	MinHourlyRate          decimal.Decimal                         `json:"minHourlyRate"`               // 最低時薪
	MaxHourlyRate          decimal.Decimal                         `json:"maxHourlyRate"`               // 最高時薪
	Duration               decimal.Decimal                         `json:"duration"`                    // 總工作時長
	CancelReason           string                                  `json:"cancelReason"`                // 取消原因
	CalendarNote           string                                  `json:"calendarNote"`                // 日曆備註
	ShiftTime              []JobSearchForProfessionalShiftTimeResp `json:"shiftTime" gorm:"-"`          // 班次時間(日曆才返回)
	Progress               string                                  `json:"progress,omitempty" gorm:"-"` // 進度 UPCOMING=待開始 IN_PROGRESS=進行中 APPLIED=已申請 COMPLETE=已完成 CANCEL=已取消
	Timezone               string                                  `json:"timezone"`                    // 時區
	Status                 string                                  `json:"status"`                      // 職位狀態
	PayHours               decimal.Decimal                         `json:"payHours"`                    // 工作時數
	ExpRecAmount           decimal.Decimal                         `json:"expRecAmount" gorm:"-"`       // 該工作預計的總收入金額
	InvoiceAmount          decimal.Decimal                         `json:"invoiceAmount" gorm:"-"`      // 該工作應收金額
	UnpaidAmount           decimal.Decimal                         `json:"unpaidAmount" gorm:"-"`       // 該工作未收金額
	JobStatus              string                                  `json:"jobStatus" gorm:"-"`          // 工作狀態
	ProfessionalNames      string                                  `json:"professionalNames" gorm:"-"`  // 已接受專業人士姓名
	JobCurrentStatus       string                                  `json:"-"`                           // 工作當前狀態
	Accept                 string                                  `json:"-"`                           // 是否取錄
	ConfirmationNotesCount int                                     `json:"confirmationNotesCount"`      // 確認單數量（已確認、已發送、已拒絕）
}

// calculateJobApplicationProgress 根據申請狀態和時間計算工作申請進度
func calculateJobApplicationProgress(jobStatus string, status string, beginTime, endTime time.Time, timezone string) string {
	if jobStatus == model.JobStatusCancel {
		return JobApplicationProgressCancel
	}
	if timezone == "" {
		return ""
	}
	tz, err := time.LoadLocation(timezone)
	if err != nil {
		return ""
	}
	nowTime := time.Now().In(tz)

	// 根據 my-job-tab.md 文檔定義的五個標籤
	switch status {
	case model.JobApplicationStatusApply, model.JobApplicationStatusChatting, model.JobApplicationStatusInvite:
		// Applied: 申請中、聊天中或被邀請，且工作結束時間大於當前時間
		if endTime.After(nowTime) {
			return JobApplicationProgressApplied
		}
	case model.JobApplicationStatusAccept:
		// Upcoming: 已接受且工作開始時間大於當前時間
		if beginTime.After(nowTime) {
			return JobApplicationProgressUpcoming
		} else if endTime.After(nowTime) {
			// In Progress: 已接受且當前時間在工作開始和結束時間之間
			return JobApplicationProgressInProgress
		} else {
			// Completed: 已接受且工作結束時間小於當前時間
			return JobApplicationProgressComplete
		}
	case model.JobApplicationStatusApplicationCancel, model.JobApplicationStatusFacilityCancel, model.JobApplicationStatusProfessionalCancel:
		// Cancelled: 機構已取消或專業人員已取消
		return JobApplicationProgressCancel
	}

	// 默認返回空字符串
	return ""
}

func (s *jobApplicationService) MyJobList(db *gorm.DB, req MyJobListReq, pageSet *xresp.PageSet, sortSet xresp.SortingSet) ([]MyJobListResp, error) {
	var resp []MyJobListResp
	var err error

	confirmationNoteBuilder := db.Model(&model.Document{}).
		Table("document as d").
		Select([]string{
			"d.job_application_id",
			"COUNT(*) as confirmation_notes_count",
		}).
		Where("d.category = ?", model.DocumentCategoryConfirmation).
		Where("d.data_type <> ?", model.DocumentDataTypeSystemToFacility).
		Where("d.progress in (?)", []string{model.DocumentProgressConfirm, model.DocumentProgressSent, model.DocumentProgressReject}).
		Group("d.job_application_id")

	if req.FacilityId > 0 {
		confirmationNoteBuilder.Where("d.facility_id = ?", req.FacilityId)
	}

	builder := db.Table("job_application AS ja").
		Select([]string{
			"ja.id AS job_application_id",
			"ja.job_id",
			"j.facility_id",
			"fp.name AS facility_name",
			"IF(sl.address_extra IS NOT NULL AND sl.address_extra != '', CONCAT(sl.address_extra, ' ', sl.address), sl.address) AS service_location_address",
			"j.begin_time",
			"j.end_time",
			"j.min_hourly_rate",
			"j.max_hourly_rate",
			"j.duration",
			"j.pay_hours",
			"ja.calendar_note",
			"ja.status",
			"ja.cancel_reason",
			"sl.timezone AS timezone",
			"j.status AS job_current_status",
			"ja.accept",
			"COALESCE(cn.confirmation_notes_count, 0) AS confirmation_notes_count",
		}).
		Joins("JOIN job j ON j.id = ja.job_id").
		Joins("JOIN facility_profile fp ON fp.facility_id = j.facility_id AND fp.data_type = ?", model.FacilityProfileDataTypeApproved).
		Joins("JOIN service_location sl ON j.service_location_id = sl.id").
		Joins("LEFT JOIN (?) AS cn ON cn.job_application_id = ja.id", confirmationNoteBuilder).
		Where("ja.user_id = ?", req.ReqUserId).
		Where("ja.status <> ?", model.JobApplicationStatusWithdraw).
		Where("ja.deleted <> ?", model.JobApplicationDeletedY)

	if req.FacilityId > 0 {
		builder = builder.Where("j.facility_id = ?", req.FacilityId)
	}

	if req.Address != "" {
		builder = builder.Where("IF(sl.address_extra IS NOT NULL AND sl.address_extra != '', CONCAT(sl.address_extra, ' ', sl.address), sl.address) LIKE ?", "%"+req.Address+"%")
	}

	if req.BeginTime != "" {
		builder = builder.Where(xgorm.ConvertTZSql("j.begin_time", "sl.timezone", ">="), req.BeginTime)
	}

	if req.EndTime != "" {
		builder = builder.Where(xgorm.ConvertTZSql("j.end_time", "sl.timezone", "<="), req.EndTime)
	}

	if !req.MinHourlyRate.IsZero() {
		hourRate, ok := req.MinHourlyRate.Float64()
		if !ok {
			return resp, errors.New("invalid hourly rate")
		}
		builder = builder.Where("j.min_hourly_rate >= ?", hourRate)
	}

	if req.Status != "" {
		builder = builder.Where("ja.status IN (?)", strings.Split(req.Status, ","))
	}
	if req.JobStatus != "" {
		switch req.JobStatus {
		case "NO_INVOICE":
			// 未開立發票
			builder = builder.Joins("LEFT JOIN document AS d ON d.job_application_id = ja.id AND d.category = ?", model.DocumentCategoryInvoice).
				Where("d.id IS NULL")
		case "UNPAID":
			// 有未收款發票
			builder = builder.Joins("LEFT JOIN document AS d_unpaid ON d_unpaid.job_application_id = ja.id AND d_unpaid.category = ? AND d_unpaid.data_type <> ?", model.DocumentCategoryInvoice, model.DocumentDataTypeSystemToFacility).
				Where("d_unpaid.id IS NOT NULL AND d_unpaid.payment_received = ?", model.DocumentProgressPaymentReceivedN)
		case "PARTIALLY_PAID":
			// 有未收款發票也有已收款發票
			invoiceBuilder := db.Model(&model.Document{}).
				Table("document AS d").
				Select("d.job_application_id, COUNT(*) as total_invoices, SUM(CASE WHEN d.payment_received = ? THEN 1 ELSE 0 END) as paid_invoices",
					model.DocumentProgressPaymentReceivedY).
				Where("d.category = ?", model.DocumentCategoryInvoice).
				Where("d.data_type < ?", model.DocumentDataTypeSystemToFacility).
				Where("d.progress = ?", model.DocumentProgressConfirm). // TODO: 這裡統計已經確認的發票還是所有未取消的發票？
				Group("d.job_application_id").
				Having("paid_invoices > 0 AND paid_invoices < total_invoices")
			builder = builder.
				Joins("JOIN (?) as d_partially_paid ON d_partially_paid.job_application_id = ja.id", invoiceBuilder)
		case "FULLY_PAID":
			// 所有發票都已收款
			invoiceBuilder := db.Model(&model.Document{}).
				Table("document AS d").
				Select("d.job_application_id, COUNT(*) as total_invoices, SUM(CASE WHEN d.payment_received = ? THEN 1 ELSE 0 END) as paid_invoices",
					model.DocumentProgressPaymentReceivedY).
				Where("d.category = ?", model.DocumentCategoryInvoice).
				Where("d.data_type < ?", model.DocumentDataTypeSystemToFacility).
				Where("d.progress = ?", model.DocumentProgressConfirm). // TODO: 這裡統計已經確認的發票還是所有未取消的發票？
				Group("d.job_application_id").
				Having("paid_invoices = total_invoices AND total_invoices > 0")
			builder = builder.
				Joins("JOIN (?) as d_fully_paid ON d_fully_paid.job_application_id = ja.id", invoiceBuilder)
		case "CHATTING":
			builder = builder.Where("ja.status IN (?, ?)", model.JobApplicationStatusChatting, model.JobApplicationStatusInvite)
		case "PENDING":
			builder = builder.Where("ja.status = ?", model.JobApplicationStatusApply)
		case "DECLINED":
			builder = builder.Where("ja.status = ?", model.JobApplicationStatusDecline)
		case "APPLIED_ONLY":
			builder = builder.Where("ja.status IN (?)", []string{model.JobApplicationStatusApplicationCancel, model.JobApplicationStatusFacilityCancel, model.JobApplicationStatusProfessionalCancel}).
				Where("ja.accept <> ?", model.JobApplicationAcceptY)
		case "CONFIRMED":
			builder = builder.Where("ja.status IN (?)", []string{model.JobApplicationStatusApplicationCancel, model.JobApplicationStatusFacilityCancel, model.JobApplicationStatusProfessionalCancel}).
				Where("ja.accept = ?", model.JobApplicationAcceptY)
		}
	}

	if req.CalendarBeginTime != "" || req.CalendarEndTime != "" {
		// 日曆篩選，不包含已取消的工作
		builder = builder.Where("ja.status NOT IN (?)", []string{model.JobApplicationStatusApplicationCancel, model.JobApplicationStatusFacilityCancel, model.JobApplicationStatusProfessionalCancel})
		if req.CalendarEndTime != "" {
			builder = builder.Where(xgorm.ConvertTZSql("j.begin_time", "sl.timezone", "<"), req.CalendarEndTime)
		}
		if req.CalendarBeginTime != "" {
			builder = builder.Where(xgorm.ConvertTZSql("j.end_time", "sl.timezone", ">"), req.CalendarBeginTime)
		}
	}

	// 根據 Progress 進行篩選，參照 my-job-tab.md 文檔
	nowStr := time.Now().UTC().Format(xtool.DateTimeSecA1)
	if req.Progress != "" {
		switch req.Progress {
		case JobApplicationProgressUpcoming:
			// Upcoming: 已接受且工作開始時間大於當前時間
			builder = builder.Where("j.status = ?", model.JobStatusPublish).Where("ja.status = ? AND j.begin_time > ?", model.JobApplicationStatusAccept, nowStr)
		case JobApplicationProgressInProgress:
			// In Progress: 已接受且當前時間在工作開始和結束時間之間
			builder = builder.Where("j.status = ?", model.JobStatusPublish).Where("ja.status = ? AND j.begin_time <= ? AND j.end_time >= ?", model.JobApplicationStatusAccept, nowStr, nowStr)
		case JobApplicationProgressApplied:
			// Applied: 申請中、聊天中或被邀請 且 工作開始時間-1小時大於當前時間
			builder = builder.Where("j.status = ?", model.JobStatusPublish).
				Where("ja.status IN (?)", []string{model.JobApplicationStatusApply, model.JobApplicationStatusChatting, model.JobApplicationStatusInvite, model.JobApplicationStatusDecline})
			//Where("DATE_SUB(j.begin_time, INTERVAL 1 HOUR) > ?", nowStr)
		case JobApplicationProgressComplete:
			// Completed: 已接受且工作結束時間小於當前時間
			builder = builder.Where("j.status = ?", model.JobStatusPublish).
				Where("ja.status = ? AND j.end_time < ?", model.JobApplicationStatusAccept, nowStr)
		case JobApplicationProgressCancel:
			// Cancelled: 機構已取消或專業人員已取消
			builder = builder.Where("j.status = ? OR ja.status IN (?)", model.JobStatusCancel, []string{model.JobApplicationStatusApplicationCancel, model.JobApplicationStatusFacilityCancel, model.JobApplicationStatusProfessionalCancel}).
				Where("(ja.accept = ?) OR (ja.accept = ? AND j.status = ?)", model.JobApplicationAcceptY, model.JobApplicationAcceptN, model.JobStatusCancel)
		}
	}

	// 處理 HideApplied 字段
	if req.HideApplied == "Y" {
		builder = builder.Where("ja.status NOT IN (?)", []string{model.JobApplicationStatusApply, model.JobApplicationStatusChatting, model.JobApplicationStatusInvite, model.JobApplicationStatusDecline}).
			Where("(ja.accept = ?) OR (ja.accept = ? AND j.status = ?)", model.JobApplicationAcceptY, model.JobApplicationAcceptN, model.JobStatusCancel)
	}

	sortKeyList := map[string]string{
		"beginTime":  "j.begin_time",
		"applyTime":  "ja.apply_time",
		"hourlyRate": "j.max_hourly_rate", // 按最高時薪降序
	}

	if err = builder.
		Scopes(xresp.AddOrder(sortSet, sortKeyList)).
		Group("ja.id").
		Order("ja.id DESC").
		Scopes(xresp.Paginate(pageSet)).
		Find(&resp).Error; err != nil {
		return resp, err
	}

	var jobIds []uint64
	var jobApplicationIds []uint64
	for i, item := range resp {
		jobIds = append(jobIds, item.JobId)
		jobApplicationIds = append(jobApplicationIds, item.JobApplicationId)
		resp[i].ShiftTime = make([]JobSearchForProfessionalShiftTimeResp, 0)

		// 計算Progress字段
		beginTime, err := time.Parse(xtool.DateTimeSecA1, item.BeginTime)
		if err != nil {
			continue
		}
		endTime, err := time.Parse(xtool.DateTimeSecA1, item.EndTime)
		if err != nil {
			continue
		}

		// 使用獨立方法計算進度
		resp[i].Progress = calculateJobApplicationProgress(item.JobCurrentStatus, item.Status, beginTime, endTime, item.Timezone)
	}

	if len(resp) > 0 {
		var shiftTimesMap map[uint64][]JobSearchForProfessionalShiftTimeResp
		if req.NeedShiftTime == "Y" {
			shiftTimesMap, err = JobShiftTimeService.SetJobShiftTimeList(db, jobIds)
			if err != nil {
				return resp, err
			}
		}
		invoiceAmounts, err := s.GetJobApplicationInvoiceAmount(db, JobApplicationInvoiceAmountReq{
			JobApplicationIds: jobApplicationIds,
		})
		if err != nil {
			return resp, err
		}
		invoiceAmountMap := make(map[uint64]JobApplicationInvoiceAmountResp)
		for _, item := range invoiceAmounts {
			invoiceAmountMap[item.JobApplicationId] = item
		}

		var acceptedApplicantMap map[uint64]string
		acceptedApplicantMap, err = JobService.GetJobAcceptedApplicantNames(db, jobIds)
		if err != nil {
			return resp, err
		}

		for i, job := range resp {
			if req.NeedShiftTime == "Y" {
				job.ShiftTime = shiftTimesMap[job.JobId]
			}

			if professionalNames, ok := acceptedApplicantMap[job.JobId]; ok {
				job.ProfessionalNames = professionalNames
			}

			// 工作預計收入金額
			job.ExpRecAmount = JobService.GetJobExpectedRevenueAmount(db, job.ShiftTime)

			if invoiceAmount, ok := invoiceAmountMap[job.JobApplicationId]; ok {
				// 工作應收金額
				job.InvoiceAmount = invoiceAmount.InvoiceAmount
				// 工作未收金額
				job.UnpaidAmount = invoiceAmount.UnpaidAmount
			}

			job.JobStatus, err = s.GetJobStatus(job)
			if err != nil {
				return resp, err
			}
			// 只有在Progress為空時，才額外計算進度
			if req.Progress == "" {
				job.Progress, err = s.GetProgress(job)
				if err != nil {
					return resp, err
				}
			}
			resp[i] = job
		}
	}

	return resp, nil
}

type JobApplicationInvoiceAmountResp struct {
	JobApplicationId uint64          `json:"jobApplicationId"` // 工作申請Id
	InvoiceAmount    decimal.Decimal `json:"invoiceAmount"`    // 應收金額
	UnpaidAmount     decimal.Decimal `json:"unpaidAmount"`     // 未收金額
}

type JobApplicationInvoiceAmountReq struct {
	JobApplicationIds []uint64 `json:"jobApplicationIds" binding:"required"` // 工作申請Id
}

func (s *jobApplicationService) GetJobApplicationInvoiceAmount(db *gorm.DB, req JobApplicationInvoiceAmountReq) ([]JobApplicationInvoiceAmountResp, error) {
	resp := make([]JobApplicationInvoiceAmountResp, 0)
	if err := db.Table("job_application AS ja").
		Joins("JOIN document AS d ON ja.id = d.job_application_id AND d.category = ? AND d.progress = ? AND d.data_type <> ?", model.DocumentCategoryInvoice, model.DocumentProgressConfirm, model.DocumentDataTypeSystemToFacility).
		Select([]string{
			"ja.id AS job_application_id",
			"SUM(d.grand_total) AS invoice_amount",
			fmt.Sprintf("SUM(CASE WHEN d.payment_received = '%s' THEN d.grand_total ELSE 0 END) AS unpaid_amount", model.DocumentProgressPaymentReceivedN),
		}).
		Where("ja.id IN (?)", req.JobApplicationIds).
		Group("ja.id").
		Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

func (s *jobApplicationService) GetJobStatus(job MyJobListResp) (string, error) {
	var jobStatus string
	if job.JobCurrentStatus == model.JobStatusCancel {
		if job.Accept == model.JobApplicationAcceptY {
			jobStatus = "CONFIRMED"
		} else {
			jobStatus = "APPLIED_ONLY"
		}
		return jobStatus, nil
	}
	switch job.Status {
	case model.JobApplicationStatusApply:
		jobStatus = "PENDING"
	case model.JobApplicationStatusChatting, model.JobApplicationStatusInvite:
		jobStatus = "CHATTING"
	case model.JobApplicationStatusDecline:
		jobStatus = "DECLINED"
	case model.JobApplicationStatusApplicationCancel, model.JobApplicationStatusFacilityCancel, model.JobApplicationStatusProfessionalCancel:
		if job.Accept == model.JobApplicationAcceptY {
			jobStatus = "CONFIRMED"
		} else {
			jobStatus = "APPLIED_ONLY"
		}
	case model.JobApplicationStatusAccept:
		if job.InvoiceAmount.IsZero() {
			jobStatus = "NO_INVOICE" // 未開立發票
		} else if job.UnpaidAmount.IsZero() {
			jobStatus = "FULLY_PAID" // 所有發票都已收款
		} else if job.UnpaidAmount.Equal(job.InvoiceAmount) {
			jobStatus = "UNPAID" // 完全未支付
		} else if job.UnpaidAmount.GreaterThan(decimal.Zero) {
			jobStatus = "PARTIALLY_PAID" // 有未收款發票也有已收款發票
		}
	}
	return jobStatus, nil
}

func (s *jobApplicationService) GetProgress(job MyJobListResp) (string, error) {
	if job.JobCurrentStatus == model.JobStatusCancel {
		return JobApplicationProgressCancel, nil
	}
	// 解析時間
	beginTime, err := time.ParseInLocation(time.RFC3339, job.BeginTime, time.UTC)
	if err != nil {
		return "", err
	}
	endTime, err := time.ParseInLocation(time.RFC3339, job.EndTime, time.UTC)
	if err != nil {
		return "", err
	}

	// 獲取當前時間
	nowTime := time.Now().UTC()

	// 根據 req.Progress 篩選邏輯判斷進度
	switch job.Status {
	case model.JobApplicationStatusAccept:
		// Upcoming: 已接受且工作開始時間大於當前時間
		if beginTime.After(nowTime) {
			return JobApplicationProgressUpcoming, nil
		}
		// In Progress: 已接受且當前時間在工作開始和結束時間之間
		if beginTime.Before(nowTime) && endTime.After(nowTime) {
			return JobApplicationProgressInProgress, nil
		}
		// Completed: 已接受且工作結束時間小於當前時間
		if endTime.Before(nowTime) {
			return JobApplicationProgressComplete, nil
		}
	case model.JobApplicationStatusApply, model.JobApplicationStatusChatting, model.JobApplicationStatusInvite:
		// Applied: 申請中、聊天中或被邀請
		return JobApplicationProgressApplied, nil
	case model.JobApplicationStatusApplicationCancel, model.JobApplicationStatusFacilityCancel, model.JobApplicationStatusProfessionalCancel:
		// Cancelled: 機構已取消或專業人員已取消
		return JobApplicationProgressCancel, nil
	}

	// 如果沒有符合的條件，返回空字符串
	return "", nil
}

type MyJobStatisticReq struct {
	ReqUserId uint64 `json:"-"` // 請求者Id
}

type MyJobStatisticResp struct {
	UpcomingCount   int `json:"upcomingCount"`   // 待開始
	InProgressCount int `json:"inProgressCount"` // 進行中
}

func (s *jobApplicationService) MyJobStatistic(db *gorm.DB, req MyJobStatisticReq) (MyJobStatisticResp, error) {
	var resp MyJobStatisticResp

	nowTime := time.Now().UTC()
	nowStr := nowTime.Format(xtool.DateTimeSecA1)

	builder := db.Table("job_application AS ja").
		Joins("JOIN job AS j ON ja.job_id = j.id").
		Where("ja.deleted <> ?", model.JobApplicationDeletedY).
		Where("ja.status = ? AND j.begin_time > ?", model.JobApplicationStatusAccept, nowStr).
		Where("ja.user_id = ?", req.ReqUserId)

	if err := builder.Pluck("COUNT(DISTINCT ja.id) AS num", &resp.UpcomingCount).Error; err != nil {
		return resp, err
	}

	builder = db.Table("job_application AS ja").
		Joins("JOIN job AS j ON ja.job_id = j.id").
		Select("COUNT(DISTINCT ja.id) as in_progress_count").
		Where("ja.deleted <> ?", model.JobApplicationDeletedY).
		Where("ja.status = ? AND j.begin_time <= ? AND j.end_time >= ?", model.JobApplicationStatusAccept, nowStr, nowStr).
		Where("ja.user_id = ?", req.ReqUserId)

	if err := builder.Pluck("in_progress_count", &resp.InProgressCount).Error; err != nil {
		return resp, err
	}

	return resp, nil
}

// MyJobWithdrawReq 撤回我的工作申請請求
type MyJobWithdrawReq struct {
	JobApplicationId uint64 `json:"jobApplicationId" binding:"required"` // 工作申請Id
	ReqUserId        uint64 `json:"-"`                                   // 請求者Id
}

// CheckJobApplicationCanWithdraw 檢查工作申請是否可以撤回
func (s *jobApplicationService) CheckJobApplicationCanWithdraw(db *gorm.DB, jobApplicationId uint64, userId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job_application.cannot_withdraw",
		Other: "The job application cannot be withdrawn.",
	}
	notFoundMsg := i18n.Message{
		ID:    "checker.job_application.id.does_not_exist",
		Other: "No such application record, please try after reloading.",
	}
	// 查詢申請記錄
	var application model.JobApplication
	var err error
	if err = db.Where("id = ? AND user_id = ?", jobApplicationId, userId).First(&application).Error; xgorm.IsSqlErr(err) {
		if xgorm.IsNotFoundErr(err) {
			return false, msg, nil
		}
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, notFoundMsg, nil
	}

	if application.Status == model.JobApplicationStatusInvite {
		// 機構正在"邀請你加入工作"，請先處理
		return false, i18n.Message{
			ID:    "checker.job_application.cannot_withdraw",
			Other: "You have been invited to join a job by the facility. Please respond to the invitation first.",
		}, nil
	}

	// 只有狀態為已申請(APPLY)
	if application.Status != model.JobApplicationStatusApply && application.Status != model.JobApplicationStatusChatting && application.Status != model.JobApplicationStatusDecline {
		return false, msg, nil
	}

	return true, msg, nil
}

// MyJobWithdraw 撤回工作申請
func (s *jobService) MyJobWithdraw(db *gorm.DB, req MyJobWithdrawReq) error {
	// 查詢申請記錄
	var application model.JobApplication
	// 鎖定行
	if err := db.Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id = ?", req.JobApplicationId).
		Where("user_id = ?", req.ReqUserId).
		Where("status in (?)", []string{model.JobApplicationStatusApply, model.JobApplicationStatusDecline}).
		First(&application).Error; err != nil {
		return err
	}

	// 更新申請狀態為已撤回
	if err := db.Model(&model.JobApplication{}).
		Where("id = ?", req.JobApplicationId).
		Updates(map[string]interface{}{
			"status":        model.JobApplicationStatusWithdraw,
			"withdraw_time": time.Now().UTC().Format(xtool.DateTimeSecA1),
		}).Error; err != nil {
		return err
	}

	// 將其它的撤銷記錄標記為已刪除
	if err := db.Model(&model.JobApplication{}).
		Where("job_id = ?", application.JobId).
		Where("user_id = ?", application.UserId).
		Where("id <> ?", application.Id).
		Where("status = ?", model.JobApplicationStatusWithdraw).
		Updates(map[string]interface{}{
			"deleted": model.JobApplicationDeletedY,
		}).Error; err != nil {
		return err
	}

	return nil
}

// MyJobCancelReq 取消我的工作申請請求
type MyJobCancelReq struct {
	JobApplicationId uint64 `json:"jobApplicationId" binding:"required"` // 工作申請Id
	CancelReason     string `json:"cancelReason" binding:"required"`     // 取消原因
	ReqUserId        uint64 `json:"-"`                                   // 請求者Id
}

// CheckJobApplicationCanCancel 檢查工作申請是否可以取消
func (s *jobApplicationService) CheckJobApplicationCanCancel(db *gorm.DB, jobApplicationId uint64, userId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job_application.cannot_cancel",
		Other: "The job application cannot be cancelled.",
	}
	notFoundMsg := i18n.Message{
		ID:    "checker.job_application.id.does_not_exist",
		Other: "No such application record, please try after reloading.",
	}
	// 查詢申請記錄
	var application model.JobApplication
	var err error
	if err = db.Where("id = ? AND user_id = ?", jobApplicationId, userId).First(&application).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, notFoundMsg, nil
	}
	// 只有狀態為已接受(ACCEPT)才可以取消
	if application.Status != model.JobApplicationStatusChatting && application.Status != model.JobApplicationStatusInvite && application.Status != model.JobApplicationStatusAccept {
		return false, msg, nil
	}
	return true, i18n.Message{}, nil
}

// MyJobCancel 取消工作申請
func (s *jobApplicationService) MyJobCancel(db *gorm.DB, req MyJobCancelReq) error {
	// 查詢申請記錄
	var application model.JobApplication
	// 鎖定行
	if err := db.Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id = ?", req.JobApplicationId).
		Where("user_id = ?", req.ReqUserId).
		Where("status = ? OR status = ? OR status = ?", model.JobApplicationStatusChatting, model.JobApplicationStatusInvite, model.JobApplicationStatusAccept).
		First(&application).Error; err != nil {
		return err
	}
	updateMap := map[string]interface{}{
		"status":         model.JobApplicationStatusProfessionalCancel,
		"cancel_time":    time.Now().UTC().Format(xtool.DateTimeSecA1),
		"cancel_reason":  req.CancelReason,
		"cancel_user_id": req.ReqUserId,
	}

	if application.Status == model.JobApplicationStatusAccept {
		// 更新工作的 accepted count
		var job model.Job
		if err := db.Clauses(clause.Locking{Strength: "UPDATE"}).
			Where("id = ?", application.JobId).
			First(&job).Error; err != nil {
			return err
		}
		job.AcceptedCount = job.AcceptedCount - 1
		if err := db.Save(&job).Error; err != nil {
			return err
		}
	}

	// 更新申請狀態為專業人員已取消
	if err := db.Model(&model.JobApplication{}).
		Where("id = ?", req.JobApplicationId).
		Updates(updateMap).Error; err != nil {
		return err
	}
	return nil
}

// MyJobUpdateCalendarNoteReq 更新工作申請備註請求
type MyJobUpdateCalendarNoteReq struct {
	JobApplicationId uint64 `json:"jobApplicationId" binding:"required"` // 工作申請Id
	Remark           string `json:"remark"`                              // 備註
	ReqUserId        uint64 `json:"-"`                                   // 請求者Id
}

// CheckJobApplicationCanUpdateCalendarNote 檢查工作申請是否可以更新備註
func (s *jobApplicationService) CheckJobApplicationCanUpdateCalendarNote(db *gorm.DB, jobApplicationId uint64, userId uint64) (bool, i18n.Message, error) {
	notFoundMsg := i18n.Message{
		ID:    "checker.job_application.id.does_not_exist",
		Other: "No such application record, please try after reloading.",
	}
	// 查詢申請記錄
	var application model.JobApplication
	var err error
	if err = db.Where("id = ? AND user_id = ?", jobApplicationId, userId).First(&application).Error; xgorm.IsSqlErr(err) {
		return false, i18n.Message{}, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, notFoundMsg, nil
	}
	return true, i18n.Message{}, nil
}

// MyJobUpdateCalendarNote 更新工作申請備註
func (s *jobApplicationService) MyJobUpdateCalendarNote(db *gorm.DB, req MyJobUpdateCalendarNoteReq) error {
	// 更新備註
	if err := db.Model(&model.JobApplication{}).
		Where("user_id = ?", req.ReqUserId).
		Where("id = ?", req.JobApplicationId).
		Update("calendar_note", req.Remark).Error; err != nil {
		return err
	}
	return nil
}

type JobCalendarDayReq struct {
	FacilityId         uint64 `form:"facilityId"`                                                        // 所屬機構Id
	PositionProfession string `form:"positionProfession"`                                                // 職位專業
	ServiceLocationId  uint64 `form:"serviceLocationId"`                                                 // 服務地點Id
	CalendarBeginTime  string `form:"calendarBeginTime" binding:"required,datetime=2006-01-02 15:04:05"` // 日曆開始時間
	CalendarEndTime    string `form:"calendarEndTime" binding:"required,datetime=2006-01-02 15:04:05"`   // 日曆結束時間
}

type JobCalendarDayResp struct {
	JobId                  uint64                                  `json:"jobId"`                  // 工作Id
	PositionProfession     string                                  `json:"positionProfession"`     // 職位專業
	PositionProfessionName string                                  `json:"positionProfessionName"` // 職位專業名稱
	ServiceLocationId      uint64                                  `form:"serviceLocationId"`      // 服務地點Id
	ServiceLocationAddress string                                  `json:"serviceLocationAddress"` // 服務地點地址
	BeginTime              time.Time                               `json:"beginTime"`              // 工作開始時間
	EndTime                time.Time                               `json:"endTime"`                // 工作結束時間
	CalendarNote           string                                  `json:"calendarNote"`           // 日曆備註
	Timezone               string                                  `json:"timezone"`               // 時區
	MinHourlyRate          decimal.Decimal                         `json:"minHourlyRate"`          // 最低時薪
	MaxHourlyRate          decimal.Decimal                         `json:"maxHourlyRate"`          // 最高時薪
	NumberOfPeople         int32                                   `json:"numberOfPeople"`         // 需要人數
	AcceptedCount          int32                                   `json:"acceptedCount"`          // 已接受人數
	Applications           []CalendarJobApplicationResp            `json:"applications" gorm:"-"`  // 工作申請列表
	ShiftTime              []JobSearchForProfessionalShiftTimeResp `json:"shiftTime" gorm:"-"`     // 班次時間(日曆才返回)
}

type CalendarJobApplicationResp struct {
	JobApplicationId        uint64 `json:"jobApplicationId"`        // 工作申請Id
	JobId                   uint64 `json:"jobId"`                   // 工作Id
	UserId                  uint64 `json:"userId"`                  // 報名用戶Id
	ProfessionalId          uint64 `json:"professionalId"`          // 專業人員Id
	ProfessionalPhotoFileId uint64 `json:"professionalPhotoFileId"` // 專業人員照片文件Id
	ProfessionalName        string `json:"professionalName"`        // 專業人員名字
}

func (s *jobApplicationService) GetJobApplication(db *gorm.DB, jobIds []uint64) (map[uint64][]CalendarJobApplicationResp, error) {
	resp := make(map[uint64][]CalendarJobApplicationResp)
	data := make([]CalendarJobApplicationResp, 0)
	if err := db.Table("job_application AS ja").
		Joins("JOIN professional_file_relation AS pfr ON pfr.professional_id = ja.professional_id").
		Joins("JOIN professional_file AS pf ON pfr.professional_file_id = pf.id AND pf.user_id = ja.user_id AND pf.file_code = ?", model.ProfessionalFileCodePhoto).
		Select([]string{
			"ja.id AS job_application_id",
			"ja.job_id",
			"ja.user_id",
			"ja.professional_id",
			"pf.id AS professional_photo_file_id",
			"CONCAT(p.first_name, ' ', p.last_name) AS professional_name",
		}).Joins("JOIN professional p ON ja.professional_id = p.id").
		Where("ja.status = ?", model.JobApplicationStatusAccept).
		Where("ja.job_id IN (?)", jobIds).Order("ja.id").Find(&data).Error; err != nil {
		return nil, err
	}
	for _, item := range data {
		resp[item.JobId] = append(resp[item.JobId], item)
	}
	return resp, nil
}

func (s *jobApplicationService) JobCalendarDay(db *gorm.DB, req JobCalendarDayReq) ([]JobCalendarDayResp, error) {
	var resp []JobCalendarDayResp
	var err error

	builder := db.Table("job AS j").
		Select([]string{
			"j.id AS job_id",
			"j.position_profession",
			"j.service_location_id",
			"IF(sl.address_extra IS NOT NULL AND sl.address_extra != '', CONCAT(sl.address_extra, ' ', sl.address), sl.address) AS service_location_address",
			"j.begin_time",
			"j.end_time",
			"j.calendar_note",
			"sl.timezone",
			"j.min_hourly_rate",
			"j.max_hourly_rate",
			"j.number_of_people",
			"j.accepted_count",
		}).
		Joins("JOIN service_location sl ON j.service_location_id = sl.id").
		Where("j.facility_id = ?", req.FacilityId).Where("j.accepted_count > 0").
		Where("j.status IN (?)", []string{model.JobStatusPublish, model.JobStatusDisable, model.JobStatusComplete})
	if req.CalendarBeginTime != "" || req.CalendarEndTime != "" {
		builder = builder.Joins("JOIN job_shift AS js ON js.job_id = j.id")
		if req.CalendarEndTime != "" {
			builder = builder.Where(xgorm.ConvertTZSql("js.begin_time", "sl.timezone", "<"), req.CalendarEndTime)
		}
		if req.CalendarBeginTime != "" {
			builder = builder.Where(xgorm.ConvertTZSql("js.end_time", "sl.timezone", ">"), req.CalendarBeginTime)
		}
	}
	if req.ServiceLocationId > 0 {
		builder = builder.Where("j.service_location_id = ?", req.ServiceLocationId)
	}
	if req.PositionProfession != "" {
		builder = builder.Where("j.position_profession = ?", req.PositionProfession)
	}
	if err = builder.
		Group("j.id").
		Order("j.begin_time").
		Order("j.id").
		Find(&resp).Error; err != nil {
		return resp, err
	}

	var jobIds []uint64
	for _, item := range resp {
		jobIds = append(jobIds, item.JobId)
	}
	if len(resp) > 0 {
		var shiftTimesMap map[uint64][]JobSearchForProfessionalShiftTimeResp
		var jobApplications map[uint64][]CalendarJobApplicationResp
		var professionSectionMap map[string]string
		shiftTimesMap, err = JobShiftTimeService.SetJobShiftTimeList(db, jobIds)
		if err != nil {
			return resp, err
		}
		jobApplications, err = s.GetJobApplication(db, jobIds)
		if err != nil {
			return resp, err
		}
		professionSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
		if err != nil {
			return resp, err
		}

		for i, job := range resp {
			var found bool
			resp[i].ShiftTime, found = shiftTimesMap[job.JobId]
			if !found {
				resp[i].ShiftTime = make([]JobSearchForProfessionalShiftTimeResp, 0)
			}

			resp[i].Applications, found = jobApplications[job.JobId]
			if !found {
				resp[i].Applications = make([]CalendarJobApplicationResp, 0)
			}

			resp[i].PositionProfessionName = professionSectionMap[job.PositionProfession]
		}
	}

	return resp, nil
}

type JobCalendarMonthReq struct {
	FacilityId        uint64 `form:"facilityId" binding:"required"`                                     // 所屬機構Id
	CalendarBeginTime string `form:"calendarBeginTime" binding:"required,datetime=2006-01-02 15:04:05"` // 日曆開始時間
	CalendarEndTime   string `form:"calendarEndTime" binding:"required,datetime=2006-01-02 15:04:05"`   // 日曆結束時間
}

type JobCalendarMonthResp struct {
	CalendarDate string                     `json:"calendarDate"` // 日曆日期(YYYY-MM-DD)
	Jobs         []JobCalendarMonthDataResp `json:"jobs"`
}
type JobCalendarMonthDataResp struct {
	JobQty             int32  `json:"jobQty"`             // 工作數量
	PositionProfession string `json:"positionProfession"` // 職位專業
}

func (s *jobApplicationService) JobCalendarMonth(db *gorm.DB, req JobCalendarMonthReq) ([]JobCalendarMonthResp, error) {
	resp := make([]JobCalendarMonthResp, 0)
	jobs, err := s.JobCalendarDay(db, JobCalendarDayReq{
		FacilityId:        req.FacilityId,
		CalendarBeginTime: req.CalendarBeginTime,
		CalendarEndTime:   req.CalendarEndTime,
	})
	if err != nil {
		return resp, err
	}
	mapByDate := make(map[string][]JobCalendarMonthDataResp)
	jobAlreadySet := make(map[string]map[uint64]bool)
	arrByDate := make([]string, 0)
	for _, job := range jobs {
		var tz *time.Location
		if tz, err = time.LoadLocation(job.Timezone); err != nil {
			return resp, err
		}
		for _, shiftTime := range job.ShiftTime {
			beginDate := shiftTime.BeginTime.In(tz).Format(xtool.DateDayA)

			if jobMap, exists := jobAlreadySet[beginDate]; exists {
				if alreadySet := jobMap[job.JobId]; alreadySet {
					// 相同的工作,在同一天,不同的時段只計算1
					continue
				}
			}
			if _, ok := mapByDate[beginDate]; !ok {
				arrByDate = append(arrByDate, beginDate)
				jobAlreadySet[beginDate] = make(map[uint64]bool)
				mapByDate[beginDate] = []JobCalendarMonthDataResp{}
			}
			var found bool
			for i, j := range mapByDate[beginDate] {
				if j.PositionProfession == job.PositionProfession {
					j.JobQty++
					mapByDate[beginDate][i] = j
					jobAlreadySet[beginDate][job.JobId] = true
					found = true
					break
				}
			}
			if !found {
				mapByDate[beginDate] = append(mapByDate[beginDate], JobCalendarMonthDataResp{
					JobQty:             1,
					PositionProfession: job.PositionProfession,
				})
				jobAlreadySet[beginDate][job.JobId] = true
			}
		}
	}
	for _, d := range arrByDate {
		resp = append(resp, JobCalendarMonthResp{
			CalendarDate: d,
			Jobs:         mapByDate[d],
		})
	}
	return resp, nil
}

type FacilityJobApplicationCancelReq struct {
	JobId             uint64   `json:"jobId" binding:"required"`                   // 工作Id
	JobApplicationIds []uint64 `json:"jobApplicationIds" binding:"required,min=1"` // 工作申請Id
	FacilityId        uint64   `json:"facilityId" binding:"required"`
	CancelReason      string   `json:"cancelReason" binding:"required"` // 取消原因
	ReqUserId         uint64   `json:"-"`                               // 請求者Id
}

// CheckJobApplicationCanCancel 檢查工作申請是否可以取消
func (s *jobApplicationService) CheckJobApplicationCanCancelByFacility(db *gorm.DB, jobApplicationIds []uint64, jobId, facilityId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job_application.cannot_cancel",
		Other: "The job application cannot be cancelled.",
	}
	notFoundMsg := i18n.Message{
		ID:    "checker.job_application.id.does_not_exist",
		Other: "No such application record, please try after reloading.",
	}
	// 查詢申請記錄
	var applications []model.JobApplication
	jobApplicationIds = xtool.Uint64ArrayDeduplication(jobApplicationIds)
	var err error
	if err = db.Where("id IN (?)", jobApplicationIds).Where("job_id = ?", jobId).Where("facility_id = ?", facilityId).Find(&applications).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if len(jobApplicationIds) != len(applications) {
		return false, notFoundMsg, nil
	}
	// 只有狀態為已接受(ACCEPT)才可以取消
	for _, application := range applications {
		if application.Status != model.JobApplicationStatusAccept {
			return false, msg, nil
		}
	}
	return true, i18n.Message{}, nil
}

func (s *jobApplicationService) JobApplicationCancelByFacility(db *gorm.DB, req FacilityJobApplicationCancelReq) error {
	// 查詢申請記錄
	var applications []model.JobApplication
	// 鎖定行
	if err := db.Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id IN (?)", req.JobApplicationIds).
		Where("facility_id = ?", req.FacilityId).
		Where("status = ?", model.JobApplicationStatusAccept).
		Find(&applications).Error; err != nil {
		return err
	}
	var job model.Job
	if err := db.Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id = ?", req.JobId).
		First(&job).Error; err != nil {
		return err
	}

	for _, application := range applications {
		if application.Status == model.JobApplicationStatusAccept {
			// 更新工作的 accepted count
			job.AcceptedCount = job.AcceptedCount - 1
		}
		cancelStatus := model.JobApplicationStatusProfessionalCancel
		if application.Accept == model.JobApplicationAcceptY {
			cancelStatus = model.JobApplicationStatusFacilityCancel
		}
		// 更新申請狀態為專業人員已取消
		if err := db.Model(&model.JobApplication{}).
			Where("id = ?", application.Id).
			Updates(map[string]interface{}{
				"status":         cancelStatus,
				"cancel_time":    time.Now().UTC(),
				"cancel_reason":  req.CancelReason,
				"cancel_user_id": req.ReqUserId,
			}).Error; err != nil {
			return err
		}
	}

	if err := db.Save(&job).Error; err != nil {
		return err
	}

	// 計算賠付
	if err := s.Compensation(db, req.ReqUserId, req.JobId, applications); err != nil {
		return err
	}

	return nil
}

type GenerateCompensationReq struct {
	JobShiftId        uint64          `json:"jobShiftId"`
	JobApplicationId  uint64          `json:"jobApplicationId"`
	CompensationHours decimal.Decimal `json:"compensationHours"`
	Particular        string          `json:"particular"`
}

func (s *jobApplicationService) Compensation(db *gorm.DB, handleUser uint64, jobId uint64, applications []model.JobApplication) error {
	// 獲取 取消時所有影響的班次
	targetTime := time.Now().UTC()
	nextJobShifts, err := JobShiftTimeService.GetNextValidJobShiftsByJobId(db, jobId, targetTime)
	if err != nil {
		return err
	}

	// 處理班次時間比較邏輯
	userMap := make(map[uint64]bool)
	shiftCancellations := make([]model.JobShiftCancellation, 0)
	for i, shift := range nextJobShifts {
		if shift.BeginTime == nil {
			continue
		}

		if i == 0 {
			// 計算時間差（小時）
			timeDiff := shift.BeginTime.Sub(targetTime)
			hoursDiff := timeDiff.Hours()

			if timeDiff > 0 {
				// beginTime 大於 targetTime 班次未開始
				if hoursDiff > 0 && hoursDiff <= 1 {
					// 1小時內的處理邏輯 - 需要賠付
					compensationHours := decimal.NewFromInt(2)
					if shift.PayHours.LessThan(decimal.NewFromInt(2)) {
						compensationHours = shift.PayHours
					}
					for _, application := range applications {
						if !userMap[application.UserId] {
							shiftCancellations = append(shiftCancellations, model.JobShiftCancellation{
								FacilityId:           application.FacilityId,
								JobShiftId:           shift.Id,
								JobApplicationId:     application.Id,
								ProfessionalId:       application.ProfessionalId,
								RequiresCompensation: "Y",
								CompensationHours:    compensationHours,
								CompensationAmount:   shift.HourlyRate.Mul(compensationHours),
								CompensationReason:   fmt.Sprintf("工作開始前1小時內取消，生成賠付數據"),
								CancelTime:           &targetTime,
								CancelUserId:         handleUser,
							})
							userMap[application.UserId] = true

							err = s.SendGenerateCompensationConfirmationNoteTask(GenerateCompensationReq{
								JobShiftId:        shift.Id,
								JobApplicationId:  application.Id,
								CompensationHours: compensationHours,
								Particular:        "Shift cancelled within 1 hour of start time",
							})
							if err != nil {
								return err
							}
						}
					}
				}
			} else {
				// beginTime 少於 targetTime 班次已開始
				// 根據 shift time 的時數進行賠付
				if hoursDiff >= -4 {
					if shift.Duration.LessThanOrEqual(decimal.NewFromInt(4)) {
						// 如果shift time時間小於4h,按照 4h賠付(payHours 不足時按照 payHours 付)
						compensationHours := decimal.NewFromInt(4)
						if shift.PayHours.LessThan(decimal.NewFromInt(4)) {
							compensationHours = shift.PayHours
						}
						for _, application := range applications {
							if !userMap[application.UserId] {
								shiftCancellations = append(shiftCancellations, model.JobShiftCancellation{
									FacilityId:           application.FacilityId,
									JobShiftId:           shift.Id,
									JobApplicationId:     application.Id,
									ProfessionalId:       application.ProfessionalId,
									RequiresCompensation: "Y",
									CompensationHours:    compensationHours,
									CompensationAmount:   shift.HourlyRate.Mul(compensationHours),
									CompensationReason:   fmt.Sprintf("工作開始後取消，生成賠付數據(4H)"),
									CancelTime:           &targetTime,
									CancelUserId:         handleUser,
								})
								userMap[application.UserId] = true

								err = s.SendGenerateCompensationConfirmationNoteTask(GenerateCompensationReq{
									JobShiftId:        shift.Id,
									JobApplicationId:  application.Id,
									CompensationHours: compensationHours,
									Particular:        "On-Arrival Cancellation",
								})
								if err != nil {
									return err
								}
							}
						}
					} else {
						for _, application := range applications {
							if !userMap[application.UserId] {
								shiftCancellations = append(shiftCancellations, model.JobShiftCancellation{
									FacilityId:           application.FacilityId,
									JobShiftId:           shift.Id,
									JobApplicationId:     application.Id,
									ProfessionalId:       application.ProfessionalId,
									RequiresCompensation: "Y",
									CompensationHours:    shift.PayHours,
									CompensationAmount:   shift.HourlyRate.Mul(shift.PayHours),
									CompensationReason:   fmt.Sprintf("工作開始後取消，生成賠付數據(payHours:%s)", shift.PayHours.String()),
									CancelTime:           &targetTime,
									CancelUserId:         handleUser,
								})
								userMap[application.UserId] = true

								err = s.SendGenerateCompensationConfirmationNoteTask(GenerateCompensationReq{
									JobShiftId:        shift.Id,
									JobApplicationId:  application.Id,
									CompensationHours: shift.PayHours,
									Particular:        "On-Arrival Cancellation",
								})
								if err != nil {
									return err
								}
							}
						}
					}
				}
			}
		} else {
			for _, application := range applications {
				shiftCancellations = append(shiftCancellations, model.JobShiftCancellation{
					FacilityId:           application.FacilityId,
					JobShiftId:           shift.Id,
					JobApplicationId:     application.Id,
					ProfessionalId:       application.ProfessionalId,
					RequiresCompensation: "N",
					CancelTime:           &targetTime,
					CancelUserId:         handleUser,
				})
			}
		}
	}
	return nil
}

func (s *jobApplicationService) SendGenerateCompensationConfirmationNoteTask(req GenerateCompensationReq) error {
	reqData, err := json.Marshal(req)
	if err != nil {
		return err
	}

	// 發送到隊列
	err = xamqp.SendTask(GenerateCompensationConfirmationNoteTask, xamqp.Task{
		MessageId: GenerateCompensationConfirmationNoteTask,
		TaskId:    GenerateCompensationConfirmationNoteTask + "_" + uuid.NewV4().String(),
		Data:      string(reqData),
	})
	if err != nil {
		return err
	}
	return nil
}

// 接受邀請 - 專業人士
type JobAcceptInviteReq struct {
	FacilityId       uint64 `json:"facilityId" binding:"required"`       // 機構Id
	JobId            uint64 `json:"jobId" binding:"required"`            // 工作Id
	JobApplicationId uint64 `json:"jobApplicationId" binding:"required"` // 工作申請Id
	ReqUserId        uint64 `json:"-"`                                   // 請求者Id
}

// 接受邀請 - 專業人士
func (s *jobApplicationService) AcceptInvite(db *gorm.DB, req JobAcceptInviteReq) error {
	if err := JobService.LockJob(db, req.JobId); err != nil {
		return err
	}

	updateMap := map[string]interface{}{
		"status":      model.JobApplicationStatusAccept,
		"accept":      model.JobApplicationAcceptY,
		"accept_time": time.Now().UTC().Truncate(time.Second),
	}
	// 將工作申請狀態設置為已接受
	if err := db.Model(&model.JobApplication{}).
		Where("id = ?", req.JobApplicationId).
		Updates(updateMap).Error; err != nil {
		return err
	}

	// 更新工作確認人數
	if err := db.Model(&model.Job{}).
		Where("id = ?", req.JobId).
		Update("accepted_count", gorm.Expr("accepted_count + 1")).Error; err != nil {
		return err
	}

	// 找到與此工作重疊的工作申請，並將其狀態設置為取消
	var jobShiftItems []model.JobShift
	if err := db.Model(&model.JobShift{}).
		Where("job_id = ?", req.JobId).
		Find(&jobShiftItems).Error; err != nil {
		return err
	}
	// 已經處理的工作id
	handledJobApplicationIds := make([]uint64, 0)
	for _, jobShiftItem := range jobShiftItems {
		var jobApplications []model.JobApplication
		applicationBuilder := db.Model(&model.JobApplication{}).
			Table("job_application AS ja").
			Joins("JOIN job_shift AS js ON js.job_id = ja.job_id").
			Where("ja.user_id = ?", req.ReqUserId).
			Where("ja.deleted <> ?", model.JobApplicationDeletedY).
			Where("ja.status IN (?)", []string{model.JobApplicationStatusApply, model.JobApplicationStatusChatting, model.JobApplicationStatusInvite}).
			Where("js.begin_time < ? AND js.end_time > ?", jobShiftItem.EndTime, jobShiftItem.BeginTime)
		if len(handledJobApplicationIds) > 0 {
			applicationBuilder = applicationBuilder.Where("ja.id NOT IN (?)", handledJobApplicationIds)
		}
		if err := applicationBuilder.Find(&jobApplications).Error; err != nil {
			return err
		}
		withdrawJobApplicationIds := make([]uint64, 0)
		for _, jobApplication := range jobApplications {
			if lo.Contains(handledJobApplicationIds, jobApplication.Id) {
				continue
			}
			withdrawJobApplicationIds = append(withdrawJobApplicationIds, jobApplication.Id)
		}
		if len(withdrawJobApplicationIds) > 0 {
			updateMap = map[string]interface{}{
				"status":        model.JobApplicationStatusWithdraw,
				"withdraw_time": time.Now().UTC().Truncate(time.Second),
			}
			if err := db.Model(&model.JobApplication{}).
				Where("id IN (?)", withdrawJobApplicationIds).
				Where("status IN (?)", []string{model.JobApplicationStatusApply, model.JobApplicationStatusChatting, model.JobApplicationStatusInvite}).
				Updates(updateMap).Error; err != nil {
				return err
			}
			handledJobApplicationIds = append(handledJobApplicationIds, withdrawJobApplicationIds...)
		}
	}
	// 獲取工作信息
	var job model.Job
	if err := db.Model(&model.Job{}).
		Where("id = ?", req.JobId).
		First(&job).Error; err != nil {
		return err
	}
	// 如果工作是預付款並且招滿人, 則生成預付發票
	if job.PaymentTerms == model.JobPaymentTermsPayUpfront && job.AcceptedCount == job.NumberOfPeople {
		createInvoiceReqData, err := json.Marshal(map[string]uint64{
			"jobId": req.JobId,
		})
		if err != nil {
			return err
		}
		// 發送到隊列
		err = xamqp.SendTask(CreateInvoiceDraftTask, xamqp.Task{
			MessageId: CreateInvoiceDraftTask,
			TaskId:    CreateInvoiceDraftTask + "_" + uuid.NewV4().String(),
			Data:      string(createInvoiceReqData),
		})
		if err != nil {
			return err
		}
	}

	// TODO: 發送接受邀請通知

	return nil
}

type JobDeclineInviteReq struct {
	JobId            uint64 `json:"jobId" binding:"required"`            // 工作Id
	JobApplicationId uint64 `json:"jobApplicationId" binding:"required"` // 工作申請Id
}

// 拒絕邀請 - 專業人士
func (s *jobApplicationService) DeclineInvite(db *gorm.DB, req JobDeclineInviteReq) error {
	if err := JobService.LockJob(db, req.JobId); err != nil {
		return err
	}

	updateMap := map[string]interface{}{
		"status":       model.JobApplicationStatusDecline,
		"decline_time": time.Now().UTC().Truncate(time.Second),
	}
	// 將工作申請狀態設置為已拒絕
	if err := db.Model(&model.JobApplication{}).
		Where("id = ?", req.JobApplicationId).
		Where("status = ?", model.JobApplicationStatusInvite).
		Updates(updateMap).Error; err != nil {
		return err
	}

	// TODO: 發送拒絕邀請通知

	return nil
}

// 機構查詢專業人士的申請列表
type JobApplicationListByFacilitySessionReq struct {
	FacilityId         uint64 `form:"facilityId" binding:"required"`         // 機構Id
	ProfessionalUserId uint64 `form:"professionalUserId" binding:"required"` // 專業人士Id
}

// 機構查詢專業人士的申請列表
type JobApplicationListBySessionReq struct {
	FacilityId         uint64 // 機構Id
	ProfessionalUserId uint64 // 專業人士Id
}
type JobApplicationDetailBySessionResp struct {
	JobApplicationId       uint64                                  `json:"jobApplicationId"`                // 工作申請Id
	JobId                  uint64                                  `json:"jobId"`                           // 工作Id
	PositionProfession     string                                  `json:"positionProfession"`              // 職位專業
	PositionProfessionName string                                  `json:"positionProfessionName" gorm:"-"` // 職位專業名稱
	MinHourlyRate          decimal.Decimal                         `json:"minHourlyRate"`                   // 最低時薪
	MaxHourlyRate          decimal.Decimal                         `json:"maxHourlyRate"`                   // 最高時薪
	ServiceLocationAddress string                                  `json:"serviceLocationAddress"`          // 服務地點地址
	Timezone               string                                  `json:"timezone"`                        // 時區
	Status                 string                                  `json:"status"`                          // 工作申請狀態
	NumberOfPeople         int32                                   `json:"numberOfPeople"`                  // 需求人數
	AcceptedCount          int32                                   `json:"acceptedCount"`                   // 確認人數
	InvitedCount           int32                                   `json:"invitedCount"`                    // 邀請中的人數
	ShiftTime              []JobSearchForProfessionalShiftTimeResp `json:"shiftTime" gorm:"-"`              // 班次時間(日曆才返回)
}

func (s *jobApplicationService) JobApplicationListBySession(db *gorm.DB, req JobApplicationListBySessionReq) ([]JobApplicationDetailBySessionResp, error) {
	var err error
	resp := make([]JobApplicationDetailBySessionResp, 0)

	builder := db.Model(&model.JobApplication{}).
		Table("job_application AS ja").
		Joins("JOIN job AS j ON j.id = ja.job_id").
		Joins("JOIN service_location AS sl ON sl.id = j.service_location_id").
		Joins("LEFT JOIN job_application AS ia ON ia.job_id = j.id AND ia.status = ?", model.JobApplicationStatusInvite).
		Select([]string{
			"ja.id as job_application_id",
			"j.id as job_id",
			"j.position_profession as position_profession",
			"j.min_hourly_rate as min_hourly_rate",
			"j.max_hourly_rate as max_hourly_rate",
			"sl.address as service_location_address",
			"sl.timezone as timezone",
			"ja.status as status",
			"j.number_of_people as number_of_people",
			"j.accepted_count as accepted_count",
			"COUNT(ia.id) as invited_count",
		}).
		Where("ja.facility_id = ?", req.FacilityId).
		Where("ja.user_id = ?", req.ProfessionalUserId).
		Where("ja.status in (?)", []string{model.JobApplicationStatusApply, model.JobApplicationStatusChatting, model.JobApplicationStatusInvite, model.JobApplicationStatusAccept, model.JobApplicationStatusDecline}).
		Where("ja.deleted <> ?", model.JobApplicationDeletedY).
		Where("j.status = ?", model.JobStatusPublish)

	nowTime := time.Now().UTC().Truncate(time.Second)
	builder = builder.Where("DATE_SUB(j.begin_time, INTERVAL 1 HOUR) > ?", nowTime). // 工作開始時間前1小時
												Where("j.publish_time < ?", nowTime) // 工作已經發佈

	if err = builder.Group("ja.id").Find(&resp).Error; err != nil {
		return nil, err
	}
	if len(resp) == 0 {
		resp = make([]JobApplicationDetailBySessionResp, 0)
		return resp, nil
	}
	jobIds := make([]uint64, 0)
	for _, v := range resp {
		jobIds = append(jobIds, v.JobId)
	}
	jobIds = xtool.Uint64ArrayDeduplication(jobIds)
	var shiftTimesMap map[uint64][]JobSearchForProfessionalShiftTimeResp
	shiftTimesMap, err = JobShiftTimeService.SetJobShiftTimeList(db, jobIds)
	if err != nil {
		return resp, err
	}
	var professionSectionMap map[string]string
	professionSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
	if err != nil {
		return resp, err
	}
	for i, v := range resp {
		if shiftTimes, ok := shiftTimesMap[v.JobId]; ok {
			resp[i].ShiftTime = shiftTimes
		}
		if v, ok := professionSectionMap[v.PositionProfession]; ok {
			resp[i].PositionProfessionName = v
		}
	}

	return resp, nil
}

func (s *jobApplicationService) JobApplicationListByFacilitySession(db *gorm.DB, req JobApplicationListByFacilitySessionReq) ([]JobApplicationDetailBySessionResp, error) {
	var r JobApplicationListBySessionReq
	_ = copier.Copy(&r, req)
	return s.JobApplicationListBySession(db, r)
}

// 專業人士查詢在某個機構的已申請列表
type JobApplicationListByProfessionalSessionReq struct {
	FacilityId         uint64 `form:"facilityId" binding:"required"` // 機構Id
	ProfessionalUserId uint64 `form:"-" json:"-"`                    // 專業人士Id
}

func (s *jobApplicationService) JobApplicationListByProfessionalSession(db *gorm.DB, req JobApplicationListByProfessionalSessionReq) ([]JobApplicationDetailBySessionResp, error) {
	var r JobApplicationListBySessionReq
	_ = copier.Copy(&r, req)
	return s.JobApplicationListBySession(db, r)
}

// 查詢在某個申請記錄的詳細資料
func (s *jobApplicationService) GetJobApplicationDetail(db *gorm.DB, jobApplicationId uint64) (JobApplicationDetailBySessionResp, error) {
	var err error
	var resp JobApplicationDetailBySessionResp
	builder := db.Model(&model.JobApplication{}).
		Table("job_application AS ja").
		Joins("JOIN job AS j ON j.id = ja.job_id").
		Joins("JOIN service_location AS sl ON sl.id = j.service_location_id").
		Joins("LEFT JOIN job_application AS ia ON ia.job_id = j.id AND ia.status = ?", model.JobApplicationStatusInvite).
		Select([]string{
			"ja.id as job_application_id",
			"j.id as job_id",
			"j.position_profession as position_profession",
			"j.min_hourly_rate as min_hourly_rate",
			"j.max_hourly_rate as max_hourly_rate",
			"sl.address as service_location_address",
			"sl.timezone as timezone",
			"ja.status as status",
			"j.number_of_people as number_of_people",
			"j.accepted_count as accepted_count",
			"COUNT(ia.id) as invited_count",
		}).
		Where("ja.id = ?", jobApplicationId)
	if err = builder.First(&resp).Error; err != nil {
		return resp, err
	}
	shiftTimesMap, err := JobShiftTimeService.SetJobShiftTimeList(db, []uint64{resp.JobId})
	if err != nil {
		return resp, err
	}
	resp.ShiftTime = shiftTimesMap[resp.JobId]

	var professionSectionMap map[string]string
	professionSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
	if err != nil {
		return resp, err
	}
	if v, ok := professionSectionMap[resp.PositionProfession]; ok {
		resp.PositionProfessionName = v
	}

	return resp, nil
}

// 查詢在多個申請記錄的詳細資料
func (s *jobApplicationService) GetJobApplicationDetailList(db *gorm.DB, jobApplicationIds []uint64) (map[uint64]JobApplicationDetailBySessionResp, error) {
	var err error
	resp := make(map[uint64]JobApplicationDetailBySessionResp)
	var result []JobApplicationDetailBySessionResp
	builder := db.Model(&model.JobApplication{}).
		Table("job_application AS ja").
		Joins("JOIN job AS j ON j.id = ja.job_id").
		Joins("JOIN service_location AS sl ON sl.id = j.service_location_id").
		Joins("LEFT JOIN job_application AS ia ON ia.job_id = j.id AND ia.status = ?", model.JobApplicationStatusInvite).
		Select([]string{
			"ja.id as job_application_id",
			"j.id as job_id",
			"j.position_profession",
			"j.min_hourly_rate",
			"j.max_hourly_rate",
			"sl.address as service_location_address",
			"sl.timezone",
			"ja.status",
			"j.number_of_people",
			"j.accepted_count",
			"COUNT(ia.id) as invited_count",
		}).
		Where("ja.id IN (?)", jobApplicationIds)
	if err = builder.Group("ja.id").Find(&result).Error; err != nil {
		return resp, err
	}
	if len(result) == 0 {
		return resp, nil
	}

	jobIds := make([]uint64, 0)
	for _, v := range result {
		jobIds = append(jobIds, v.JobId)
	}
	jobIds = xtool.Uint64ArrayDeduplication(jobIds)
	var shiftTimesMap map[uint64][]JobSearchForProfessionalShiftTimeResp
	shiftTimesMap, err = JobShiftTimeService.SetJobShiftTimeList(db, jobIds)
	if err != nil {
		return resp, err
	}
	var professionSectionMap map[string]string
	professionSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
	if err != nil {
		return resp, err
	}
	for i, v := range result {
		if shiftTimes, ok := shiftTimesMap[v.JobId]; ok {
			result[i].ShiftTime = shiftTimes
		}
		if v, ok := professionSectionMap[v.PositionProfession]; ok {
			result[i].PositionProfessionName = v
		}
	}
	for _, v := range result {
		resp[v.JobApplicationId] = v
	}
	return resp, nil
}

// 機構發起聊天
type ChatByFacilityReq struct {
	FacilityId       uint64 `json:"facilityId" binding:"required"`       // 機構Id
	JobApplicationId uint64 `json:"jobApplicationId" binding:"required"` // 工作申請Id
	ReqUserId        uint64 `json:"-"`                                   // 請求者Id
}

func (s *jobApplicationService) ChatByFacility(db *gorm.DB, req ChatByFacilityReq) error {
	var jobApplication model.JobApplication
	if err := db.
		Where("id = ?", req.JobApplicationId).
		Where("facility_id = ?", req.FacilityId).
		First(&jobApplication).Error; err != nil {
		return err
	}
	if jobApplication.FacilityId != req.FacilityId {
		return errors.New("job application not found")
	}
	if jobApplication.Status == model.JobApplicationStatusApply {
		if err := db.
			Model(&jobApplication).
			Update("status", model.JobApplicationStatusChatting).Error; err != nil {
			return err
		}
	}

	// 創建聊天會話
	sessionId, _, err := WsMessageService.GetOrCreateSession(db, req.FacilityId, model.WsMessageSenderTypeFacility, jobApplication.UserId, model.WsMessageSenderTypeProfessional, true)
	if err != nil {
		return err
	}
	if err = db.Model(&model.WsSessionView{}).
		Where("session_id = ?", sessionId).
		Where("facility_id = ?", req.FacilityId).
		Update("last_interact_time", time.Now().UTC().Truncate(time.Second)).Error; err != nil {
		return err
	}

	return nil
}

func (s *jobApplicationService) CheckSessionExistByProfessional(db *gorm.DB, jobApplicationId uint64, userId uint64, wsSessionId *uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "job_application.session_not_found",
		Other: "Session not found",
	}
	var jobApplication model.JobApplication
	if err := db.
		Where("id = ?", jobApplicationId).
		Where("user_id = ?", userId).
		First(&jobApplication).Error; err != nil {
		return false, i18n.Message{}, err
	}

	// 創建聊天會話
	sessionId, _, err := WsMessageService.GetOrCreateSession(db, jobApplication.FacilityId, model.WsMessageSenderTypeFacility, jobApplication.UserId, model.WsMessageSenderTypeProfessional, false)
	if err != nil {
		return false, i18n.Message{}, err
	}
	if sessionId == 0 {
		return false, msg, nil
	}
	*wsSessionId = sessionId
	return true, i18n.Message{}, nil
}

// 專業人士發起聊天
type ChatByProfessionalReq struct {
	JobApplicationId uint64 `json:"jobApplicationId" binding:"required"` // 工作申請Id
	ReqUserId        uint64 `json:"-"`                                   // 專業人士用戶Id
	SessionId        uint64 `json:"-"`                                   // 聊天會話Id
}

func (s *jobApplicationService) ChatByProfessional(db *gorm.DB, req ChatByProfessionalReq) error {
	if err := db.Model(&model.WsSessionView{}).
		Where("session_id = ?", req.SessionId).
		Where("professional_user_id = ?", req.ReqUserId).
		Update("last_interact_time", time.Now().UTC().Truncate(time.Second)).Error; err != nil {
		return err
	}

	return nil
}
