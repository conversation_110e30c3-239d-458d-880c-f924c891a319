package services

import (
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xmodel"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gorm.io/gorm"
)

var casbinTableNameMap = map[string]string{
	"CONSOLE": "console_casbin",
}

var CasbinService = new(casbinService)

type casbinService struct{}

type Casbin struct {
	Id     uint64 `json:"id" gorm:"primary_key"`
	Ptype  string `json:"ptype" gorm:"type:varchar(100);uniqueIndex:unique_index;not null"`
	V0     string `json:"v0" gorm:"type:varchar(100);uniqueIndex:unique_index;not null"`
	V1     string `json:"v1" gorm:"type:varchar(100);uniqueIndex:unique_index;not null"`
	V2     string `json:"v2" gorm:"type:varchar(100);uniqueIndex:unique_index;not null"`
	V3     string `json:"v3" gorm:"type:varchar(100);uniqueIndex:unique_index;not null"`
	V4     string `json:"v4" gorm:"type:varchar(100);uniqueIndex:unique_index;not null"`
	V5     string `json:"v5" gorm:"type:varchar(100);uniqueIndex:unique_index;not null"`
	Level  string `json:"level" gorm:"type:varchar(255);not null"`
	Remark string `json:"remark" gorm:"type:varchar(255);not null"`
	xmodel.Model
}

func (Casbin) TableName() string {
	return ""
}

type CasbinReq interface {
	NameOfCasbin() string
}

func CasbinTableName(c CasbinReq) string {
	return casbinTableNameMap[c.NameOfCasbin()]
}

func (s *casbinService) CheckCasbinUnique(db *gorm.DB, req CasbinReq, ptype string, v0 string, v1 string, v2 string, v3 string, v4 string, v5 string, exceptId ...uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.casbin.police.already_exists",
		Other: "Police already exists.",
	}
	var err error
	var count int64
	builder := db.Table(CasbinTableName(req))
	if len(exceptId) > 0 {
		builder = builder.Where("id <> ?", exceptId[0])
	}
	if err = builder.
		Where("ptype = ?", ptype).
		Where("v0 = ?", v0).
		Where("v1 = ?", v1).
		Where("v2 = ?", v2).
		Where("v3 = ?", v3).
		Where("v4 = ?", v4).
		Where("v5 = ?", v5).
		Count(&count).Error; err != nil {
		return false, msg, err
	}
	if count == 0 {
		return true, msg, nil
	} else {
		return false, msg, nil
	}
}

func (s *casbinService) CheckIdExist(db *gorm.DB, req CasbinReq, casbin *Casbin, id uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.casbin.id.does_not_exist",
		Other: "Policy does not exist.",
	}
	var err error
	if err = db.Table(CasbinTableName(req)).First(casbin, id).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

type CasbinReloadReq struct {
	CasbinName string `json:"casbinName" binding:"required"`
}

func (req CasbinReloadReq) NameOfCasbin() string {
	return req.CasbinName
}

type CasbinCreateReq struct {
	CasbinName string `json:"casbinName" binding:"required"`
	Ptype      string `json:"ptype" binding:"required,oneof=p g"`
	V0         string `json:"v0" binding:"required"`
	V1         string `json:"v1" binding:"required"`
	V2         string `json:"v2" binding:"required_if=Ptype p"`
	V3         string `json:"v3"`
	V4         string `json:"v4"`
	V5         string `json:"v5"`
	Level      string `json:"level"`
	Remark     string `json:"remark"`
}

func (req CasbinCreateReq) NameOfCasbin() string {
	return req.CasbinName
}

type CasbinCreateResp struct {
	CasbinId uint64 `json:"casbinId"`
}

func (s *casbinService) Create(db *gorm.DB, req CasbinCreateReq) (CasbinCreateResp, error) {
	var resp CasbinCreateResp
	var err error
	var m Casbin
	_ = copier.Copy(&m, req)
	if err = db.Table(CasbinTableName(req)).Create(&m).Error; err != nil {
		return resp, err
	}
	resp.CasbinId = m.Id
	return resp, nil
}

type CasbinListReq struct {
	CasbinName     string `form:"casbinName" binding:"required"`
	Ptype          string `form:"ptype"`
	V0             string `form:"v0"`
	V1             string `form:"v1"`
	V2             string `form:"v2"`
	V3             string `form:"v3"`
	V4             string `form:"v4"`
	V5             string `form:"v5"`
	Level          string `form:"level"`
	Remark         string `form:"remark"`
	OnlyNoneAction string `form:"onlyNoneAction"` // 傳 Y 只顯示那些不存在對應的 action 的 casbin
}

func (req CasbinListReq) NameOfCasbin() string {
	return req.CasbinName
}

type CasbinListResp struct {
	CasbinId uint64 `json:"casbinId"`
	Ptype    string `json:"ptype"`
	V0       string `json:"v0"`
	V1       string `json:"v1"`
	V2       string `json:"v2"`
	V3       string `json:"v3"`
	V4       string `json:"v4"`
	V5       string `json:"v5"`
	Level    string `json:"level"`
	Remark   string `json:"remark"`
}

func (s *casbinService) List(db *gorm.DB, req CasbinListReq, pageSet *xresp.PageSet) ([]CasbinListResp, error) {
	var err error
	var resp []CasbinListResp
	builder := db.Table(CasbinTableName(req) + " AS c").Select([]string{
		"c.id AS casbin_id",
		"c.ptype",
		"c.v0",
		"c.v1",
		"c.v2",
		"c.v3",
		"c.v4",
		"c.v5",
		"c.level",
		"c.remark",
	})
	if req.Ptype != "" {
		builder = builder.Where("c.ptype = ?", req.Ptype)
	}
	if req.V0 != "" {
		builder = builder.Where("c.v0 = ?", req.V0)
	}
	if req.V1 != "" {
		builder = builder.Where("c.v1 LIKE ?", "%"+req.V1+"%")
	}
	if req.V2 != "" {
		builder = builder.Where("c.v2 LIKE ?", "%"+req.V2+"%")
	}
	if req.Level != "" {
		builder = builder.Where("c.level = ?", req.Level)
	}
	if req.Remark != "" {
		builder = builder.Where("c.remark LIKE ?", "%"+req.Remark+"%")
	}
	if req.OnlyNoneAction == "Y" {
		builder = builder.Joins("LEFT JOIN action a ON a.code = c.v0").Where("a.id IS NULL").Where("BINARY c.v0 = LOWER(c.v0)") // 不包含大寫的
	}
	if err = builder.Scopes(xresp.Paginate(pageSet)).
		Order("c.ptype").
		Order("c.v1").
		Order("c.v0").
		Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

type CasbinEditReq struct {
	CasbinName string `json:"casbinName" binding:"required"`
	CasbinId   uint64 `json:"casbinId" binding:"required"`
	Ptype      string `json:"ptype" binding:"required,oneof=p g"`
	V0         string `json:"v0" binding:"required"`
	V1         string `json:"v1" binding:"required"`
	V2         string `json:"v2" binding:"required_if=Ptype p"`
	V3         string `json:"v3"`
	V4         string `json:"v4"`
	V5         string `json:"v5"`
	Level      string `json:"level"`
	Remark     string `json:"remark"`
}

func (req CasbinEditReq) NameOfCasbin() string {
	return req.CasbinName
}

func (s *casbinService) Edit(db *gorm.DB, req CasbinEditReq) error {
	var err error
	var m Casbin
	if err = db.Table(CasbinTableName(req)).First(&m, req.CasbinId).Error; err != nil {
		return err
	}
	_ = copier.Copy(&m, req)
	if err = db.Table(CasbinTableName(req)).Save(&m).Error; err != nil {
		return err
	}
	return nil
}

type CasbinInquireReq struct {
	CasbinName string `form:"casbinName" binding:"required"`
	CasbinId   uint64 `form:"casbinId" binding:"required"`
}

func (req CasbinInquireReq) NameOfCasbin() string {
	return req.CasbinName
}

type CasbinInquireResp struct {
	CasbinId uint64 `json:"casbinId"`
	Ptype    string `json:"ptype"`
	V0       string `json:"v0"`
	V1       string `json:"v1"`
	V2       string `json:"v2"`
	V3       string `json:"v3"`
	V4       string `json:"v4"`
	V5       string `json:"v5"`
	Level    string `json:"level"`
	Remark   string `json:"remark"`
}

func (s *casbinService) Inquire(db *gorm.DB, req CasbinInquireReq) (CasbinInquireResp, error) {
	var err error
	var resp CasbinInquireResp
	var m Casbin
	if err = db.Table(CasbinTableName(req)).First(&m, req.CasbinId).Error; err != nil {
		return resp, err
	}
	_ = copier.Copy(&resp, m)
	resp.CasbinId = m.Id
	return resp, nil
}

type CasbinDeleteReq struct {
	CasbinName string `json:"casbinName" binding:"required"`
	CasbinId   uint64 `json:"casbinId" binding:"required"`
}

func (req CasbinDeleteReq) NameOfCasbin() string {
	return req.CasbinName
}

func (s *casbinService) Delete(db *gorm.DB, req CasbinDeleteReq) error {
	var err error
	if err = db.Table(CasbinTableName(req)).Where("id = ?", req.CasbinId).Delete(&Casbin{}).Error; err != nil {
		return err
	}
	return nil
}
