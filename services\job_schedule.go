package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/Norray/xrocket/xamqp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xmodel/xtype"
	"github.com/jinzhu/copier"
	"github.com/jinzhu/now"
	"github.com/samber/lo"
	uuid "github.com/satori/go.uuid"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xtool"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	JobScheduleStatusNotStarted = "NOT_STARTED" // 還未發佈第一個job的計劃
	JobScheduleStatusTerminated = "TERMINATED"  // 未完成但被終止的計劃
	JobScheduleStatusInProgress = "IN_PROGRESS" // 已經有發佈工作的計劃
	JobScheduleStatusCompleted  = "COMPLETED"   // 已經發佈完所有工作的計劃

	JobSchedulePublishTask = "job_schedule_publish_task" // 發佈任務隊列
)

// jobScheduleService 工作排程服務實現
type jobScheduleService struct{}

var JobScheduleService = new(jobScheduleService)

// region ---------------------------------------------------- Checker ----------------------------------------------------

// CheckIdExist 檢查工作排程Id是否存在
func (s *jobScheduleService) CheckIdExist(db *gorm.DB, m *model.JobSchedule, id uint64, facilityId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job_schedule.id.does_not_exist",
		Other: "The schedule does not exist.",
	}
	var err error
	if err = db.Where("facility_id = ?", facilityId).
		First(&m, id).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	}
	return true, msg, nil
}

// 檢查工作排程重複類型設置
func (s *jobScheduleService) CheckRepeatTypeSettings(req JobScheduleCreateReq) (bool, i18n.Message, error) {
	switch req.RepeatType {
	case model.JobScheduleRepeatDaily:
		// 檢查日重複設定
		if req.DailyInterval <= 0 {
			return false, i18n.Message{ID: "job_schedule.daily_interval.required", Other: "Daily repeat interval must be greater than 0."}, nil
		}
	case model.JobScheduleRepeatWeekly:
		// 檢查週重複設定
		if req.WeeklyInterval <= 0 {
			return false, i18n.Message{ID: "job_schedule.weekly_interval.required", Other: "Weekly repeat interval must be greater than 0."}, nil
		}
		if req.WeekDays == "" {
			return false, i18n.Message{ID: "job_schedule.week_days.required", Other: "At least one weekday must be specified for weekly repeats."}, nil
		}
	case model.JobScheduleRepeatMonthly:
		// 檢查月重複設定
		if req.MonthlyInterval <= 0 {
			return false, i18n.Message{ID: "job_schedule.monthly_interval.required", Other: "Monthly repeat interval must be greater than 0."}, nil
		}
		if req.MonthlyType == model.JobScheduleMonthlyTypeDay {
			if req.MonthlyDayOfMonth < 1 || req.MonthlyDayOfMonth > 31 {
				return false, i18n.Message{ID: "job_schedule.monthly_day_of_month.invalid", Other: "Day of month must be between 1 and 31."}, nil
			}
		} else if req.MonthlyType == model.JobScheduleMonthlyTypeWeekday {
			if req.MonthlyWeekIndex < 1 || req.MonthlyWeekIndex > 5 {
				return false, i18n.Message{ID: "job_schedule.monthly_week_index.invalid", Other: "Week index must be between 1 and 5."}, nil
			}
		} else {
			return false, i18n.Message{ID: "job_schedule.monthly_type.invalid", Other: "Invalid monthly repeat type."}, nil
		}
	default:
		return false, i18n.Message{ID: "job_schedule.repeat_type.invalid", Other: "Invalid repeat type."}, nil
	}
	return true, i18n.Message{}, nil
}

// 檢查工作排程日期範圍
func (s *jobScheduleService) CheckDateRange(beginDate, endDate string) (bool, i18n.Message, error) {
	parsedBeginDate, err := time.Parse(xtool.DateDayA, beginDate)
	if err != nil {
		return false, i18n.Message{ID: "job_schedule.begin_date.invalid", Other: "Invalid start date format."}, nil
	}

	parsedEndDate, err := time.Parse(xtool.DateDayA, endDate)
	if err != nil {
		return false, i18n.Message{ID: "job_schedule.end_date.invalid", Other: "Invalid end date format."}, nil
	}

	if parsedEndDate.Before(parsedBeginDate) {
		return false, i18n.Message{ID: "job_schedule.date_range.invalid", Other: "End date cannot be earlier than start date."}, nil
	}

	return true, i18n.Message{}, nil
}

// 檢查工作班次時間
func (s *jobScheduleService) CheckShiftTime(jobShiftItems []JobScheduleShiftItem, timezone string) (bool, i18n.Message, error) {
	tz, err := time.LoadLocation(timezone)
	if err != nil {
		return false, i18n.Message{ID: "job_schedule.timezone.invalid", Other: "Invalid timezone."}, nil
	}

	// 先轉為時間戳數組
	shiftTimeTimes := make([][]int64, 0)
	nowTime := time.Now().In(tz)
	today := nowTime.Format(xtool.DateDayA)
	nextDay := now.With(nowTime).AddDate(0, 0, 1).Format(xtool.DateDayA)
	for _, jobShiftItem := range jobShiftItems {
		if jobShiftItem.BeginTime == "" || jobShiftItem.EndTime == "" {
			return false, i18n.Message{ID: "job_schedule.shift_time.required", Other: "Shift time is required."}, nil
		}
		beginTime, err := time.ParseInLocation(xtool.DateTimeSecA1, fmt.Sprintf("%s %s:00", today, jobShiftItem.BeginTime), tz)
		if err != nil {
			return false, i18n.Message{ID: "job_schedule.shift_time.invalid", Other: "Invalid begin time format."}, nil
		}
		var endTime time.Time
		if jobShiftItem.BeginTime > jobShiftItem.EndTime {
			endTime, err = time.ParseInLocation(xtool.DateTimeSecA1, fmt.Sprintf("%s %s:00", nextDay, jobShiftItem.EndTime), tz)
		} else {
			endTime, err = time.ParseInLocation(xtool.DateTimeSecA1, fmt.Sprintf("%s %s:00", today, jobShiftItem.EndTime), tz)
		}
		if err != nil {
			return false, i18n.Message{ID: "job_schedule.shift_time.invalid", Other: "Invalid end time format."}, nil
		}
		shiftTimeTimes = append(shiftTimeTimes, []int64{beginTime.Unix(), endTime.Unix()})
	}
	// 排序
	sort.Slice(shiftTimeTimes, func(i, j int) bool {
		return shiftTimeTimes[i][0] < shiftTimeTimes[j][0]
	})
	// 檢查是否重疊
	for i := 0; i < len(shiftTimeTimes); i++ {
		aStart, aEnd := shiftTimeTimes[i][0], shiftTimeTimes[i][1]
		for j := i + 1; j < len(shiftTimeTimes); j++ {
			bStart, bEnd := shiftTimeTimes[j][0], shiftTimeTimes[j][1]
			// 檢查是否重疊（允許結束時間等於開始時間）
			if aStart < bEnd && bStart < aEnd {
				return false, i18n.Message{ID: "job_schedule.shift_time.overlap", Other: "Shift time overlaps."}, nil
			}
		}
	}

	return true, i18n.Message{}, nil
}

// endregion ---------------------------------------------------- Checker ----------------------------------------------------

// region ---------------------------------------------------- Calculator ----------------------------------------------------

// 計算排程的所有日期
func (s *jobScheduleService) CalcJobScheduleDates(schedule model.JobSchedule, startTime time.Time, timezone string) ([]string, error) {
	var err error
	var dates []string
	// 遍歷所有日期，根據重複類型計算
	switch schedule.RepeatType {
	case model.JobScheduleRepeatDaily:
		dates, err = s.CalcDailyDates(schedule, startTime, timezone)
	case model.JobScheduleRepeatWeekly:
		dates, err = s.CalcWeeklyDates(schedule, startTime, timezone)
	case model.JobScheduleRepeatMonthly:
		dates, err = s.CalcMonthlyDates(schedule, startTime, timezone)
	}
	if err != nil {
		return nil, err
	}
	return dates, nil
}

func (s *jobScheduleService) CalcDailyDates(schedule model.JobSchedule, startTime time.Time, timezone string) ([]string, error) {
	var dates []string
	tz, err := time.LoadLocation(timezone)
	if err != nil {
		return nil, err
	}
	beginDateStr := schedule.BeginDate.String()          // 開始日期 YYYY-MM-DD
	endDateStr := schedule.EndDate.String()              // 結束日期 YYYY-MM-DD
	startTimeString := startTime.Format(xtool.TimeHourG) // 開始時間 HH:MM
	beginTime, err := time.ParseInLocation(xtool.DateTimeSecA1, beginDateStr+" "+startTimeString, tz)
	if err != nil {
		return nil, err
	}
	endTime, err := time.ParseInLocation(xtool.DateTimeSecA1, endDateStr+" "+startTimeString, tz)
	if err != nil {
		return nil, err
	}
	nowTime := time.Now().In(tz)
	calcDate := beginTime
	step := int(schedule.DailyInterval)
	for calcDate.Before(endTime) || calcDate.Equal(endTime) {
		// nowTime需在calcDate的60分鐘前
		diff := calcDate.Sub(nowTime)
		if diff.Minutes() > 60 {
			dates = append(dates, calcDate.Format(xtool.DateDayA))
		}
		calcDate = now.With(calcDate.AddDate(0, 0, step)).BeginningOfDay()
	}
	return dates, nil
}

func (s *jobScheduleService) CalcWeeklyDates(schedule model.JobSchedule, startTime time.Time, timezone string) ([]string, error) {
	var dates []string
	tz, err := time.LoadLocation(timezone)
	if err != nil {
		return nil, err
	}
	beginDateStr := schedule.BeginDate.String()          // 開始日期 YYYY-MM-DD
	endDateStr := schedule.EndDate.String()              // 結束日期 YYYY-MM-DD
	startTimeString := startTime.Format(xtool.TimeHourG) // 開始時間 HH:MM
	beginTime, err := time.ParseInLocation(xtool.DateTimeSecA1, beginDateStr+" "+startTimeString, tz)
	if err != nil {
		return nil, err
	}
	endTime, err := time.ParseInLocation(xtool.DateTimeSecA1, endDateStr+" "+startTimeString, tz)
	if err != nil {
		return nil, err
	}
	nowTime := time.Now().In(tz)
	overTime := now.With(endTime).EndOfDay()
	calcDate := beginTime
	step := int(schedule.WeeklyInterval)
	for (calcDate.Before(endTime) || calcDate.Equal(endTime)) && calcDate.Before(overTime) {
		weekDaysStr := strings.Split(schedule.WeekDays, ",")
		var weekDays []int
		for _, weekDayStr := range weekDaysStr {
			weekDay, err := strconv.Atoi(weekDayStr)
			if err != nil {
				return nil, err
			}
			// 將星期日轉換為0，方便Go計算
			if weekDay == 7 {
				weekDay = 0
			}
			weekDays = append(weekDays, weekDay)
		}
		// 排序
		sort.Ints(weekDays)
		currentWeekDay := int(calcDate.Weekday())
		// 遍歷整個星期0-6
		for i := currentWeekDay; i < 7; i++ {
			if calcDate.After(overTime) {
				break
			}
			if lo.Contains(weekDays, i) {
				diff := calcDate.Sub(nowTime)
				if diff.Minutes() > 60 {
					dates = append(dates, calcDate.Format(xtool.DateDayA))
				}
			}
			if i < 6 {
				calcDate = calcDate.AddDate(0, 0, 1)
			}
		}
		// 轉到本周日，再加step週
		calcDate = now.With(calcDate).BeginningOfWeek().AddDate(0, 0, 7*step)
	}
	return dates, nil
}

func (s *jobScheduleService) CalcMonthlyDates(schedule model.JobSchedule, startTime time.Time, timezone string) ([]string, error) {
	var dates []string
	tz, err := time.LoadLocation(timezone)
	if err != nil {
		return nil, err
	}
	beginDateStr := schedule.BeginDate.String()          // 開始日期 YYYY-MM-DD
	endDateStr := schedule.EndDate.String()              // 結束日期 YYYY-MM-DD
	startTimeString := startTime.Format(xtool.TimeHourG) // 開始時間 HH:MM
	beginTime, err := time.ParseInLocation(xtool.DateTimeSecA1, beginDateStr+" "+startTimeString, tz)
	if err != nil {
		return nil, err
	}
	endTime, err := time.ParseInLocation(xtool.DateTimeSecA1, endDateStr+" "+startTimeString, tz)
	if err != nil {
		return nil, err
	}
	calcDate := beginTime
	nowTime := time.Now().In(tz)
	overTime := now.With(endTime).EndOfDay()
	step := schedule.MonthlyInterval
	for (calcDate.Before(endTime) || calcDate.Equal(endTime)) && calcDate.Before(overTime) {
		if schedule.MonthlyType == model.JobScheduleMonthlyTypeDay {
			if calcDate.Day() <= int(schedule.MonthlyDayOfMonth) {
				targetDate := time.Date(calcDate.Year(), calcDate.Month(), int(schedule.MonthlyDayOfMonth), 0, 0, 0, 0, calcDate.Location())
				diff := targetDate.Sub(nowTime)
				if diff.Minutes() > 60 {
					dates = append(dates, targetDate.Format(xtool.DateDayA))
				}
			}
		} else if schedule.MonthlyType == model.JobScheduleMonthlyTypeWeekday {
			targetDate := s.GetWeekIndexAndTuesday(calcDate, int(schedule.MonthlyWeekIndex), int(schedule.MonthlyWeekDay))
			if calcDate.Before(targetDate) || calcDate.Equal(targetDate) {
				//targetDate = time.Date(calcDate.Year(), calcDate.Month(), schedule.MonthlyDayOfMonth, 0, 0, 0, 0, calcDate.Location())
				diff := targetDate.Sub(nowTime)
				if diff.Minutes() > 60 {
					dates = append(dates, targetDate.Format(xtool.DateDayA))
				}
			}
		}
		// 轉到1號，再加step月
		calcDate = now.With(calcDate).BeginningOfMonth().AddDate(0, int(step), 0)
	}
	return dates, nil
}

// 計算某個日期所在月第N個星期幾的日期
// weekDayIndex 1-5，1表示該月的第一個指定星期幾，5表示該月的最後一個指定星期幾
// weekDay 1-7，表示星期幾（1=星期一, 2=星期二, ..., 7=星期日）
func (s *jobScheduleService) GetWeekIndexAndTuesday(date time.Time, weekDayIndex, weekDay int) time.Time {
	// 將ISO標準（1-7）轉換為Go的Weekday（0-6）
	goWeekDay := convertToGoWeekDay(weekDay)

	// 獲取該月的第一天
	firstDayOfMonth := time.Date(date.Year(), date.Month(), 1, 0, 0, 0, 0, date.Location())

	// 如果 weekDayIndex 是 5，表示該月的最後一個指定星期幾
	if weekDayIndex == 5 {
		// 獲取該月的最後一天
		lastDayOfMonth := time.Date(date.Year(), date.Month()+1, 0, 0, 0, 0, 0, date.Location())

		// 從月底倒推，找到最後一個符合 weekDay 的日期
		targetDate := lastDayOfMonth
		for int(targetDate.Weekday()) != goWeekDay {
			targetDate = targetDate.AddDate(0, 0, -1)
		}
		return targetDate
	}

	// 找到該月第一個指定的星期幾
	firstTargetDay := firstDayOfMonth
	for int(firstTargetDay.Weekday()) != goWeekDay {
		firstTargetDay = firstTargetDay.AddDate(0, 0, 1)
	}

	// 計算第 N 個星期幾的日期（第一個星期幾加上 (weekDayIndex-1) 週）
	targetDay := firstTargetDay.AddDate(0, 0, 7*(weekDayIndex-1))
	return targetDay
}

// endregion ---------------------------------------------------- Calculator ----------------------------------------------------

type JobScheduleShiftItem struct {
	BeginTime        string            `json:"beginTime" binding:"required,datetime=15:04,max=5"` // 開始時間(HH:MM)
	EndTime          string            `json:"endTime" binding:"required,datetime=15:04,max=5"`   // 結束時間(HH:MM)
	Duration         decimal.Decimal   `json:"duration" binding:"required"`                       // 總時長（小時）
	BreakDuration    decimal.Decimal   `json:"breakDuration" binding:"required"`                  // 休息時間（小時）
	PayHours         decimal.Decimal   `json:"payHours" binding:"required"`                       // 支付時長（小時）
	ShiftPeriod      string            `json:"shiftPeriod" binding:"required"`                    // 班次時間段
	HourlyRate       decimal.Decimal   `json:"hourlyRate" binding:"required"`                     // 時薪
	AllowanceAmount  decimal.Decimal   `json:"allowanceAmount"`                                   // 津貼總金額
	BreakTimePayable string            `json:"breakTimePayable" binding:"required,oneof=Y N"`     // 休息時間是否支付薪酬 Y N
	Allowances       []JobAllowanceReq `json:"allowances" binding:"omitempty,dive" gorm:"-"`      // 津貼設定
}

// 校驗津貼設定
func (s *jobScheduleService) CheckAllowances(db *gorm.DB, jobShiftItems []JobScheduleShiftItem) (bool, i18n.Message, error) {
	// 收集所有津貼ID
	allowanceIds := make([]uint64, 0)
	for _, item := range jobShiftItems {
		for _, allowance := range item.Allowances {
			allowanceIds = append(allowanceIds, allowance.AllowanceId)
		}
	}

	if len(allowanceIds) == 0 {
		return true, i18n.Message{}, nil
	}

	// 去重
	allowanceIds = lo.Uniq(allowanceIds)

	// 檢查津貼是否存在
	var count int64
	if err := db.Model(&model.Allowance{}).Where("id IN (?)", allowanceIds).Count(&count).Error; err != nil {
		return false, i18n.Message{}, err
	}

	if count != int64(len(allowanceIds)) {
		return false, i18n.Message{
			ID:    "checker.job_schedule.allowance.not_exist",
			Other: "Some allowances do not exist.",
		}, nil
	}

	return true, i18n.Message{}, nil
}

// 校驗班次時間範圍和津貼總金額
func (s *jobScheduleService) CheckShiftTimeAndAllowances(db *gorm.DB, jobShiftItems []JobScheduleShiftItem) (bool, i18n.Message, error) {
	// 先校驗班次時間
	if ok, msg, err := s.CheckShiftTime(jobShiftItems, ""); !ok || err != nil {
		return ok, msg, err
	}

	// 校驗津貼
	return s.CheckAllowances(db, jobShiftItems)
}

func (s *jobScheduleService) ToJobShiftItem(item JobScheduleShiftItem, timezone string) (JobShiftItem, error) {
	var beginTime time.Time
	var endTime time.Time
	var err error
	tz := time.UTC
	if timezone != "" {
		tz, err = time.LoadLocation(timezone)
		if err != nil {
			return JobShiftItem{}, err
		}
	}

	if item.BeginTime != "" {
		timeStr := fmt.Sprintf("1970-01-01 %s:00", item.BeginTime) // 使用固定日期，因為只需要時間部分
		beginTime, err = time.ParseInLocation(xtool.DateTimeSecA1, timeStr, tz)
		if err != nil {
			return JobShiftItem{}, err
		}
	}
	if item.EndTime != "" {
		timeStr := fmt.Sprintf("1970-01-01 %s:00", item.EndTime) // 使用固定日期，因為只需要時間部分
		endTime, err = time.ParseInLocation(xtool.DateTimeSecA1, timeStr, tz)
		if err != nil {
			return JobShiftItem{}, err
		}
	}
	jobShiftItem := JobShiftItem{
		BeginTime:        &beginTime, // 開始時間(YYYY-MM-DD HH:MM:SS)
		EndTime:          &endTime,   // 結束時間(YYYY-MM-DD HH:MM:SS)
		Duration:         item.Duration,
		BreakDuration:    item.BreakDuration,
		PayHours:         item.PayHours,
		ShiftPeriod:      item.ShiftPeriod,
		HourlyRate:       item.HourlyRate,
		AllowanceAmount:  item.AllowanceAmount,
		BreakTimePayable: item.BreakTimePayable,
		Allowances:       item.Allowances,
	}
	return jobShiftItem, nil
}

func (s *jobScheduleService) ToJobScheduleShiftItem(item JobShiftItem, timezone string) (JobScheduleShiftItem, error) {
	var beginTimeString string
	var endTimeString string
	var err error
	tz := time.UTC
	if timezone != "" {
		tz, err = time.LoadLocation(timezone)
		if err != nil {
			return JobScheduleShiftItem{}, err
		}
	}
	if item.BeginTime != nil {
		beginTimeString = item.BeginTime.In(tz).Format(xtool.TimeHourG1)
	}
	if item.EndTime != nil {
		endTimeString = item.EndTime.In(tz).Format(xtool.TimeHourG1)
	}
	jobScheduleShiftItem := JobScheduleShiftItem{
		BeginTime:     beginTimeString, // 開始時間(HH:MM)
		EndTime:       endTimeString,   // 結束時間(HH:MM)
		Duration:      item.Duration,
		BreakDuration: item.BreakDuration,
		PayHours:      item.PayHours,
		ShiftPeriod:   item.ShiftPeriod,
		HourlyRate:    item.HourlyRate,
	}
	return jobScheduleShiftItem, nil
}

// region ---------------------------------------------------- Create ----------------------------------------------------

// 創建工作排程請求
type JobScheduleCreateReq struct {
	// 排程相關資訊
	FacilityId  uint64 `json:"facilityId" binding:"required"`                                   // 所屬機構Id
	Name        string `json:"name" binding:"required,max=255"`                                 // 排程名稱
	RepeatType  string `json:"repeatType" binding:"required,max=32,oneof=DAILY WEEKLY MONTHLY"` // 重複類型 DAILY WEEKLY MONTHLY
	BeginDate   string `json:"beginDate" binding:"required,datetime=2006-01-02"`                // 計劃開始日期(YYYY-MM-DD)
	EndDate     string `json:"endDate" binding:"required,datetime=2006-01-02"`                  // 計劃結束日期(YYYY-MM-DD)
	AdvanceDays int32  `json:"advanceDays" binding:"required,min=0"`                            // 提前幾天發佈

	// 日重複設定
	DailyInterval int32 `json:"dailyInterval" binding:"required_if=RepeatType DAILY,omitempty,min=1"` // 每幾天

	// 週重複設定
	WeeklyInterval int32  `json:"weeklyInterval" binding:"required_if=RepeatType WEEKLY,omitempty,min=1"`                   // 每幾週
	WeekDays       string `json:"weekDays" binding:"required_if=RepeatType WEEKLY,omitempty,max=255,splitin=1 2 3 4 5 6 7"` // 週幾，多個以逗號分隔 1-7

	// 月重複設定
	MonthlyType       string `json:"monthlyType" binding:"required_if=RepeatType MONTHLY,omitempty,max=32,oneof=DAY WEEKDAY"`                 // 按日期還是按週幾 DAY WEEKDAY
	MonthlyInterval   int32  `json:"monthlyInterval" binding:"required_if=RepeatType MONTHLY,omitempty,min=1"`                                // 每幾日/週
	MonthlyDayOfMonth int32  `json:"monthlyDayOfMonth" binding:"required_if_and=RepeatType MONTHLY;MonthlyType DAY,omitempty,min=1,max=31"`   // 日-指定日 1-31
	MonthlyWeekIndex  int32  `json:"monthlyWeekIndex" binding:"required_if_and=RepeatType MONTHLY;MonthlyType WEEKDAY,omitempty,min=1,max=5"` // 週-第幾週 1-5, 5=該月最後一週
	MonthlyWeekDay    int32  `json:"monthlyWeekDay" binding:"required_if_and=RepeatType MONTHLY;MonthlyType WEEKDAY,omitempty,min=1,max=7"`   // 週-週幾 1-7

	// 工作相關資訊
	Draft               string `json:"draft" binding:"omitempty,oneof=Y N"`                                                                                                               // 是否為草稿 Y N
	PositionProfession  string `json:"positionProfession" binding:"required,max=255,oneof=MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER"`                     // 職位專業 MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER
	NumberOfPeople      int32  `json:"numberOfPeople" binding:"required"`                                                                                                                 // 所需人數
	ServiceLocationId   uint64 `json:"serviceLocationId" binding:"required"`                                                                                                              // 服務地點Id
	MinExperienceLevel  string `json:"minExperienceLevel" binding:"required_if=PositionProfession MEDICAL_PRACTITIONER,required_if=PositionProfession REGISTERED_NURSE,omitempty,max=32"` // 最低職級要求
	PreferredGrade      string `json:"preferredGrade" binding:"required_if=PositionProfession MEDICAL_PRACTITIONER,required_if=PositionProfession REGISTERED_NURSE,omitempty,max=32"`     // 首選級別 selection_type PREFERRED_GRADE(Profession = Medical Practitioner) 其他專業不填
	Qualification       string `json:"qualification" binding:"required_if=PositionProfession PERSONAL_CARE_WORKER,omitempty,max=1024"`                                                    // 護理資格 selection_type PROFESSIONAL_PERSONAL_CARE_WORKER_QUALIFICATION(Profession = Personal Care Worker)
	Specialisation      string `json:"specialisation" binding:"required,max=64"`                                                                                                          // 專業要求
	Language            string `json:"language" binding:"omitempty,max=255"`                                                                                                              // 語言要求 多個以逗號分隔 selection_type LANGUAGE
	LanguageRequirement string `json:"languageRequirement" binding:"omitempty,oneof=Y N"`                                                                                                 // 語言要求類型 Y=必須 N=可選
	SupervisionLevel    string `json:"supervisionLevel" binding:"omitempty,oneof=FULLY_SUPERVISED PARTIALLY_SUPERVISED NO_SUPERVISION"`                                                   // 監督級別 FULLY_SUPERVISED PARTIALLY_SUPERVISED NO_SUPERVISION

	Benefits        string                 `json:"benefits" binding:"omitempty,max=1024"`                     // 福利 多個以逗號分隔
	ShiftAllocation string                 `json:"shiftAllocation" binding:"required,oneof=AUTOMATIC MANUAL"` // 班次分配方式 AUTOMATIC MANUAL
	Remark          string                 `json:"remark" binding:"omitempty,max=1024"`                       // 備註
	JobShiftItems   []JobScheduleShiftItem `json:"jobShiftItems" binding:"required,min=1,dive"`               // 班次時間

	// 內部使用，不從請求中獲取
	CreatedUserId    uint64 `json:"-"` // 創建者Id
	UpdatedUserId    uint64 `json:"-"` // 更新者Id
	ScheduleTemplate string `json:"-"` // 是否為模板
}

// CreateJobScheduleDraftReq 創建工作排程草稿請求
type CreateJobScheduleDraftReq struct {
	// 排程相關資訊
	FacilityId  uint64 `json:"facilityId" binding:"required"`                                    // 所屬機構Id
	Name        string `json:"name" binding:"required,max=255"`                                  // 排程名稱
	RepeatType  string `json:"repeatType" binding:"omitempty,max=32,oneof=DAILY WEEKLY MONTHLY"` // 重複類型
	BeginDate   string `json:"beginDate" binding:"omitempty,datetime=2006-01-02"`                // 計劃開始日期(YYYY-MM-DD)
	EndDate     string `json:"endDate" binding:"omitempty,datetime=2006-01-02"`                  // 計劃結束日期(YYYY-MM-DD)
	AdvanceDays int32  `json:"advanceDays" binding:"omitempty,min=0"`                            // 提前幾天發佈
	// 日重複設定
	DailyInterval int32 `json:"dailyInterval" binding:"omitempty,min=1"` // 每幾天
	// 週重複設定
	WeeklyInterval int32  `json:"weeklyInterval" binding:"omitempty,min=1"`                   // 每幾週
	WeekDays       string `json:"weekDays" binding:"omitempty,max=255,splitin=1 2 3 4 5 6 7"` // 週幾，多個以逗號分隔 1-7
	// 月重複設定
	MonthlyInterval   int32  `json:"monthlyInterval" binding:"omitempty,min=1"`                // 每幾個月
	MonthlyType       string `json:"monthlyType" binding:"omitempty,max=32,oneof=DAY WEEKDAY"` // 按日期還是按週幾 DAY WEEKDAY
	MonthlyDayOfMonth int32  `json:"monthlyDayOfMonth" binding:"omitempty,min=1,max=31"`       // 指定日期
	MonthlyWeekIndex  int32  `json:"monthlyWeekIndex" binding:"omitempty,min=1,max=5"`         // 第幾週 1-5
	MonthlyWeekDay    int32  `json:"monthlyWeekDay" binding:"omitempty,max=7"`                 // 週幾 1-7

	// 工作相關資訊
	Draft               string `json:"draft" binding:"required,oneof=Y N"`                                                                                            // 是否為草稿 Y N
	PositionProfession  string `json:"positionProfession" binding:"required,max=255,oneof=MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER"` // 職位專業 MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER
	NumberOfPeople      int32  `json:"numberOfPeople" binding:"omitempty"`                                                                                            // 所需人數
	ServiceLocationId   uint64 `json:"serviceLocationId" binding:"omitempty"`                                                                                         // 服務地點Id
	MinExperienceLevel  string `json:"minExperienceLevel" binding:"omitempty,max=32"`                                                                                 // 最低職級要求
	PreferredGrade      string `json:"preferredGrade" binding:"omitempty,max=32"`                                                                                     // 首選級別 selection_type PREFERRED_GRADE(Profession = Medical Practitioner) 其他專業不填
	Qualification       string `json:"qualification" binding:"omitempty,max=1024"`                                                                                    // 護理資格 selection_type PROFESSIONAL_PERSONAL_CARE_WORKER_QUALIFICATION(Profession = Personal Care Worker)
	Specialisation      string `json:"specialisation" binding:"omitempty,max=64"`                                                                                     // 專業要求
	Language            string `json:"language" binding:"omitempty,max=255"`                                                                                          // 語言要求 多個逗號分割 selection_type LANGUAGE
	LanguageRequirement string `json:"languageRequirement" binding:"omitempty,oneof=Y N"`                                                                             // 語言要求類型 Y=必須 N=可選
	SupervisionLevel    string `json:"supervisionLevel" binding:"omitempty,oneof=FULLY_SUPERVISED PARTIALLY_SUPERVISED NO_SUPERVISION"`                               // 監督級別 FULLY_SUPERVISED PARTIALLY_SUPERVISED NO_SUPERVISION

	Benefits        string                 `json:"benefits" binding:"omitempty,max=1024"`                      // 福利
	ShiftAllocation string                 `json:"shiftAllocation" binding:"omitempty,oneof=AUTOMATIC MANUAL"` // 班次分配方式 AUTOMATIC MANUAL
	Remark          string                 `json:"remark" binding:"omitempty,max=1024"`                        // 備註
	JobShiftItems   []JobScheduleShiftItem `json:"jobShiftItems" binding:"omitempty"`                          // 班次時間
	// 內部使用，不從請求中獲取
	CreatedUserId    uint64 `json:"-"` // 創建者Id
	UpdatedUserId    uint64 `json:"-"` // 更新者Id
	ScheduleTemplate string `json:"-"` // 是否為模板
}

// 創建工作排程響應
type JobScheduleCreateResp struct {
	JobScheduleId uint64 `json:"jobScheduleId"` // 工作排程Id
}

func (s *jobScheduleService) Create(db *gorm.DB, req JobScheduleCreateReq) (JobScheduleCreateResp, error) {
	var resp JobScheduleCreateResp
	var err error
	nowTime := time.Now().UTC().Truncate(time.Second)

	var serviceLocation model.ServiceLocation
	if req.ServiceLocationId != 0 {
		if err = db.Where("id = ?", req.ServiceLocationId).First(&serviceLocation).Error; err != nil {
			return resp, err
		}
	}
	var schedule model.JobSchedule
	_ = copier.Copy(&schedule, req)
	schedule.BeginDate = xtype.NewNullDate(req.BeginDate)
	schedule.EndDate = xtype.NewNullDate(req.EndDate)
	schedule.CreateTime = nowTime
	schedule.UpdateTime = nil
	schedule.PositionProfession = req.PositionProfession
	if req.Draft == "Y" {
		schedule.Status = model.JobScheduleStatusDisable
	} else {
		schedule.Status = model.JobScheduleStatusEnable
	}

	// 清空
	schedule.DailyInterval = 0
	schedule.WeeklyInterval = 0
	schedule.WeekDays = ""
	schedule.MonthlyInterval = 0
	schedule.MonthlyType = ""
	schedule.MonthlyDayOfMonth = 0
	schedule.MonthlyWeekIndex = 0
	schedule.MonthlyWeekDay = 0

	// 根據重複類型設置相應的參數
	switch req.RepeatType {
	case model.JobScheduleRepeatDaily:
		schedule.DailyInterval = req.DailyInterval

	case model.JobScheduleRepeatWeekly:
		schedule.WeeklyInterval = req.WeeklyInterval
		schedule.WeekDays = req.WeekDays

	case model.JobScheduleRepeatMonthly:
		schedule.MonthlyInterval = req.MonthlyInterval
		schedule.MonthlyType = req.MonthlyType

		if req.MonthlyType == model.JobScheduleMonthlyTypeDay {
			schedule.MonthlyDayOfMonth = req.MonthlyDayOfMonth
		} else {
			schedule.MonthlyWeekIndex = req.MonthlyWeekIndex
			schedule.MonthlyWeekDay = req.MonthlyWeekDay
		}
	}
	if err = db.Create(&schedule).Error; err != nil {
		return resp, err
	}

	var jobShiftItems []JobShiftItem
	for _, item := range req.JobShiftItems {
		newItem, err := s.ToJobShiftItem(item, serviceLocation.Timezone)
		if err != nil {
			return resp, err
		}
		jobShiftItems = append(jobShiftItems, newItem)
	}

	// 創建工作職位
	jobReq := JobCreateReq{
		JobScheduleId:      schedule.Id,
		ScheduleTemplate:   model.JobScheduleTemplateY, // 設為模板
		Draft:              req.Draft,
		FacilityId:         req.FacilityId,
		PositionProfession: req.PositionProfession,
		NumberOfPeople:     req.NumberOfPeople,
		ServiceLocationId:  req.ServiceLocationId,
		MinExperienceLevel: req.MinExperienceLevel,
		PreferredGrade:     req.PreferredGrade,
		Specialisation:     req.Specialisation,
		Language:           req.Language,
		SupervisionLevel:   req.SupervisionLevel,

		SplitType: model.JobSplitTypeNo,

		Benefits:        req.Benefits,
		ShiftAllocation: req.ShiftAllocation,
		Remark:          req.Remark,
		JobShiftItems:   jobShiftItems,
		CreatedUserId:   req.CreatedUserId,
		UpdatedUserId:   0,
		Qualification:   req.Qualification,
	}

	// 創建工作模板
	_, err = JobService.Create(db, jobReq)
	if err != nil {
		return resp, err
	}

	// 更新排程日期
	if err = s.UpdateJobScheduleDate(db, req.FacilityId, schedule.Id, serviceLocation.Timezone); err != nil {
		return resp, err
	}

	// 發送發佈任務隊列
	if req.Draft == "N" {
		if err = s.SendPublishTask(schedule.Id); err != nil {
			return resp, err
		}
	}

	resp.JobScheduleId = schedule.Id

	return resp, nil
}

func (s *jobScheduleService) UpdateJobScheduleDate(db *gorm.DB, facilityId uint64, jobScheduleId uint64, timezone string) error {
	// 把所有未發佈的日期都刪除
	if err := db.Model(&model.JobScheduleDate{}).
		Where("facility_id = ?", facilityId).
		Where("job_schedule_id = ?", jobScheduleId).
		Where("status = ?", model.JobScheduleDateStatusPending).
		Delete(&model.JobScheduleDate{}).Error; err != nil {
		return err
	}

	var publishedDates []string
	if err := db.Model(&model.JobScheduleDate{}).
		Where("facility_id = ?", facilityId).
		Where("job_schedule_id = ?", jobScheduleId).
		Where("status = ?", model.JobScheduleDateStatusPublished).
		Order("date ASC").
		Pluck("date", &publishedDates).Error; err != nil {
		return err
	}
	var job model.Job
	if err := db.Model(&model.Job{}).
		Where("facility_id = ?", facilityId).
		Where("schedule_template = ?", model.JobScheduleTemplateY).
		Where("job_schedule_id = ?", jobScheduleId).
		First(&job).Error; err != nil {
		return err
	}
	var schedule model.JobSchedule
	if err := db.Model(&model.JobSchedule{}).
		Where("facility_id = ?", facilityId).
		Where("id = ?", jobScheduleId).
		First(&schedule).Error; err != nil {
		return err
	}

	if job.BeginTime == nil {
		return nil
	}
	if !schedule.BeginDate.Valid || !schedule.EndDate.Valid {
		return nil
	}
	tz, err := time.LoadLocation(timezone)
	if err != nil {
		return err
	}

	jobBeginTime := job.BeginTime.In(tz)
	jobBeginTimeStr := jobBeginTime.Format(xtool.TimeHourG)
	BeginDateStr := schedule.BeginDate.String()
	beginTime, err := time.ParseInLocation(xtool.DateTimeSecA1, fmt.Sprintf("%s %s", BeginDateStr, jobBeginTimeStr), tz)
	if err != nil {
		return err
	}

	// 重新創建日期
	nowStr := time.Now().Format(xtool.DateDayA)
	var jobScheduleDates []model.JobScheduleDate
	dates, err := s.CalcJobScheduleDates(schedule, beginTime, timezone)
	if err != nil {
		return err
	}
	for _, date := range dates {
		if !lo.Contains(publishedDates, date) && (date >= nowStr) {
			jobScheduleDates = append(jobScheduleDates, model.JobScheduleDate{
				FacilityId:    facilityId,
				JobScheduleId: jobScheduleId,
				Date:          date,
				Status:        model.JobScheduleDateStatusPending,
			})
		}
	}
	if len(jobScheduleDates) > 0 {
		if err = db.Create(&jobScheduleDates).Error; err != nil {
			return err
		}
	}
	return nil
}

// endregion ---------------------------------------------------- Create ----------------------------------------------------

// region ---------------------------------------------------- Edit ----------------------------------------------------

// 更新工作排程請求
type JobScheduleEditReq struct {
	// 排程相關資訊
	JobScheduleId uint64 `json:"jobScheduleId" binding:"required"`                                // 排程Id
	FacilityId    uint64 `json:"facilityId" binding:"required"`                                   // 所屬機構Id
	Name          string `json:"name" binding:"required,max=255"`                                 // 排程名稱
	RepeatType    string `json:"repeatType" binding:"required,max=32,oneof=DAILY WEEKLY MONTHLY"` // 重複類型
	BeginDate     string `json:"beginDate" binding:"required,datetime=2006-01-02"`                // 計劃開始日期(YYYY-MM-DD)
	EndDate       string `json:"endDate" binding:"required,datetime=2006-01-02"`                  // 計劃結束日期(YYYY-MM-DD)
	AdvanceDays   int32  `json:"advanceDays" binding:"required,min=0"`                            // 提前幾天發佈

	// 日重複設定
	DailyInterval int32 `json:"dailyInterval" binding:"required_if=RepeatType DAILY,omitempty,min=1"` // 每幾天

	// 週重複設定
	WeeklyInterval int32  `json:"weeklyInterval" binding:"required_if=RepeatType WEEKLY,omitempty,min=1"`                   // 每幾週
	WeekDays       string `json:"weekDays" binding:"required_if=RepeatType WEEKLY,omitempty,max=255,splitin=1 2 3 4 5 6 7"` // 週幾，多個以逗號分隔 1-7

	// 月重複設定
	MonthlyType       string `json:"monthlyType" binding:"required_if=RepeatType MONTHLY,omitempty,max=32,oneof=DAY WEEKDAY"`                 // 按日期還是按週幾 DAY WEEKDAY
	MonthlyInterval   int32  `json:"monthlyInterval" binding:"required_if=RepeatType MONTHLY,omitempty,min=1"`                                // 每幾日/週
	MonthlyDayOfMonth int32  `json:"monthlyDayOfMonth" binding:"required_if_and=RepeatType MONTHLY;MonthlyType DAY,omitempty,min=1,max=31"`   // 日-指定日 1-31
	MonthlyWeekIndex  int32  `json:"monthlyWeekIndex" binding:"required_if_and=RepeatType MONTHLY;MonthlyType WEEKDAY,omitempty,min=1,max=5"` // 週-第幾週 1-5, 5=該月最後一週
	MonthlyWeekDay    int32  `json:"monthlyWeekDay" binding:"required_if_and=RepeatType MONTHLY;MonthlyType WEEKDAY,omitempty,min=1,max=7"`   // 週-週幾 1-7

	// 工作相關資訊
	Draft               string `json:"draft" binding:"required,oneof=Y N"`                                                                                                                // 是否為草稿 Y N
	PositionProfession  string `json:"positionProfession" binding:"required,max=255,oneof=MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER"`                     // 職位專業 MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER
	NumberOfPeople      int32  `json:"numberOfPeople" binding:"required"`                                                                                                                 // 所需人數
	ServiceLocationId   uint64 `json:"serviceLocationId" binding:"required"`                                                                                                              // 服務地點Id
	MinExperienceLevel  string `json:"minExperienceLevel" binding:"required_if=PositionProfession MEDICAL_PRACTITIONER,required_if=PositionProfession REGISTERED_NURSE,omitempty,max=32"` // 最低職級要求
	PreferredGrade      string `json:"preferredGrade" binding:"required_if=PositionProfession MEDICAL_PRACTITIONER,omitempty,max=32"`                                                     // 首選級別 selection_type PREFERRED_GRADE(Profession = Medical Practitioner) 其他專業不填
	Qualification       string `json:"qualification" binding:"required_if=PositionProfession PERSONAL_CARE_WORKER,omitempty,max=1024"`                                                    // 護理資格 selection_type PROFESSIONAL_PERSONAL_CARE_WORKER_QUALIFICATION(Profession = Personal Care Worker)
	Specialisation      string `json:"specialisation" binding:"required,max=64"`                                                                                                          // 專業要求
	Language            string `json:"language" binding:"omitempty,max=255"`                                                                                                              // 語言要求 多個以逗號分隔 ENGLISH MANDARIN CANTONESE VIETNAMESE ARABIC HINDI KOREAN JAPANESE
	LanguageRequirement string `json:"languageRequirement" binding:"omitempty,oneof=Y N"`                                                                                                 // 語言要求類型 Y=必須 N=可選
	SupervisionLevel    string `json:"supervisionLevel" binding:"omitempty,oneof=FULLY_SUPERVISED PARTIALLY_SUPERVISED NO_SUPERVISION"`                                                   // 監督級別 FULLY_SUPERVISED PARTIALLY_SUPERVISED NO_SUPERVISION

	Benefits        string                 `json:"benefits" binding:"omitempty,max=1024"`                     // 福利
	ShiftAllocation string                 `json:"shiftAllocation" binding:"required,oneof=AUTOMATIC MANUAL"` // 班次分配方式 AUTOMATIC MANUAL
	Remark          string                 `json:"remark" binding:"omitempty,max=1024"`                       // 備註
	JobShiftItems   []JobScheduleShiftItem `json:"jobShiftItems" binding:"required,min=1,dive"`               // 班次時間

	// 內部使用，不從請求中獲取
	UpdatedUserId uint64 `json:"-"` // 更新者Id
}

// 更新工作排程草稿請求
type JobScheduleEditDraftReq struct {
	// 排程相關資訊
	JobScheduleId uint64 `json:"jobScheduleId" binding:"required"`                                 // 排程Id
	FacilityId    uint64 `json:"facilityId" binding:"required"`                                    // 所屬機構Id
	Name          string `json:"name" binding:"required,max=255"`                                  // 排程名稱
	RepeatType    string `json:"repeatType" binding:"omitempty,max=32,oneof=DAILY WEEKLY MONTHLY"` // 重複類型
	BeginDate     string `json:"beginDate" binding:"omitempty,datetime=2006-01-02"`                // 計劃開始日期(YYYY-MM-DD)
	EndDate       string `json:"endDate" binding:"omitempty,datetime=2006-01-02"`                  // 計劃結束日期(YYYY-MM-DD)
	AdvanceDays   int32  `json:"advanceDays" binding:"omitempty,min=0"`                            // 提前幾天發佈

	// 日重複設定
	DailyInterval int32 `json:"dailyInterval" binding:"omitempty,min=1"` // 每幾天

	// 週重複設定
	WeeklyInterval int32  `json:"weeklyInterval" binding:"omitempty,min=1"`                   // 每幾週
	WeekDays       string `json:"weekDays" binding:"omitempty,max=255,splitin=1 2 3 4 5 6 7"` // 週幾，多個以逗號分隔 1-7

	// 月重複設定
	MonthlyInterval   int32  `json:"monthlyInterval" binding:"omitempty,min=1"`                // 每幾個月
	MonthlyType       string `json:"monthlyType" binding:"omitempty,max=32,oneof=DAY WEEKDAY"` // 按日期還是按週幾
	MonthlyDayOfMonth int32  `json:"monthlyDayOfMonth" binding:"omitempty,min=1,max=31"`       // 指定日期
	MonthlyWeekIndex  int32  `json:"monthlyWeekIndex" binding:"omitempty,min=1,max=5"`         // 第幾週
	MonthlyWeekDay    int32  `json:"monthlyWeekDay" binding:"omitempty,max=7"`                 // 週幾 1-7

	// 工作相關資訊
	Draft               string `json:"draft" binding:"required,oneof=Y N"`                                                                                            // 是否為草稿 Y N
	PositionProfession  string `json:"positionProfession" binding:"required,max=255,oneof=MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER"` // 職位專業 MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER
	NumberOfPeople      int32  `json:"numberOfPeople" binding:"omitempty"`                                                                                            // 所需人數
	ServiceLocationId   uint64 `json:"serviceLocationId" binding:"omitempty"`                                                                                         // 服務地點Id
	MinExperienceLevel  string `json:"minExperienceLevel" binding:"omitempty,max=32"`                                                                                 // 最低職級要求
	PreferredGrade      string `json:"preferredGrade" binding:"omitempty,max=32"`                                                                                     // 首選級別 selection_type PREFERRED_GRADE(Profession = Medical Practitioner) 其他專業不填
	Qualification       string `json:"qualification" binding:"omitempty,max=1024"`                                                                                    // 護理資格 selection_type PROFESSIONAL_PERSONAL_CARE_WORKER_QUALIFICATION(Profession = Personal Care Worker)
	Specialisation      string `json:"specialisation" binding:"omitempty,max=64"`                                                                                     // 專業要求
	Language            string `json:"language" binding:"omitempty,max=255"`                                                                                          // 語言要求 多個以逗號分隔 ENGLISH MANDARIN CANTONESE VIETNAMESE ARABIC HINDI KOREAN JAPANESE
	LanguageRequirement string `json:"languageRequirement" binding:"omitempty,oneof=Y N"`                                                                             // 語言要求類型 Y=必須 N=可選
	SupervisionLevel    string `json:"supervisionLevel" binding:"omitempty,oneof=FULLY_SUPERVISED PARTIALLY_SUPERVISED NO_SUPERVISION"`                               // 監督級別 FULLY_SUPERVISED PARTIALLY_SUPERVISED NO_SUPERVISION

	Benefits        string                 `json:"benefits" binding:"omitempty,max=1024"`                      // 福利
	ShiftAllocation string                 `json:"shiftAllocation" binding:"omitempty,oneof=AUTOMATIC MANUAL"` // 班次分配方式 AUTOMATIC MANUAL
	Remark          string                 `json:"remark" binding:"omitempty,max=1024"`                        // 備註
	JobShiftItems   []JobScheduleShiftItem `json:"jobShiftItems" binding:"omitempty,dive"`                     // 班次時間

	// 內部使用，不從請求中獲取
	UpdatedUserId uint64 `json:"-"` // 更新者Id
}

// UpdateJobScheduleResp 更新工作排程響應
type UpdateJobScheduleResp struct {
	Id uint64 `json:"id"` // 工作排程Id
}

// 更新完整工作排程（包含更新工作和排程資訊）
func (s *jobScheduleService) Edit(db *gorm.DB, req JobScheduleEditReq) error {
	var err error
	var schedule model.JobSchedule
	if err = db.Where("id = ?", req.JobScheduleId).First(&schedule).Error; err != nil {
		return err
	}

	// 更新排程信息
	_ = copier.Copy(&schedule, req)
	nowTime := time.Now().UTC().Truncate(time.Second)
	schedule.BeginDate = xtype.NewNullDate(req.BeginDate)
	schedule.EndDate = xtype.NewNullDate(req.EndDate)
	schedule.UpdatedUserId = req.UpdatedUserId
	schedule.UpdateTime = &nowTime
	schedule.PositionProfession = req.PositionProfession
	if req.Draft == "Y" {
		schedule.Status = model.JobScheduleStatusDisable
	} else {
		schedule.Status = model.JobScheduleStatusEnable
	}

	// 清空重複類型相關的字段
	schedule.DailyInterval = 0
	schedule.WeeklyInterval = 0
	schedule.WeekDays = ""
	schedule.MonthlyInterval = 0
	schedule.MonthlyType = ""
	schedule.MonthlyDayOfMonth = 0
	schedule.MonthlyWeekIndex = 0
	schedule.MonthlyWeekDay = 0

	// 根據重複類型設置相應的參數
	switch req.RepeatType {
	case model.JobScheduleRepeatDaily:
		schedule.DailyInterval = req.DailyInterval

	case model.JobScheduleRepeatWeekly:
		schedule.WeeklyInterval = req.WeeklyInterval
		schedule.WeekDays = req.WeekDays

	case model.JobScheduleRepeatMonthly:
		schedule.MonthlyInterval = req.MonthlyInterval
		schedule.MonthlyType = req.MonthlyType

		if req.MonthlyType == model.JobScheduleMonthlyTypeDay {
			schedule.MonthlyDayOfMonth = req.MonthlyDayOfMonth
		} else {
			schedule.MonthlyWeekIndex = req.MonthlyWeekIndex
			schedule.MonthlyWeekDay = req.MonthlyWeekDay
		}
	}

	// 更新資料庫
	if err = db.Save(&schedule).Error; err != nil {
		return err
	}
	var job model.Job
	if err = db.Where("job_schedule_id = ?", schedule.Id).First(&job).Error; err != nil {
		return err
	}
	var serviceLocation model.ServiceLocation
	if req.ServiceLocationId != 0 {
		if err = db.Where("id = ?", req.ServiceLocationId).First(&serviceLocation).Error; err != nil {
			return err
		}
	}

	// 與Create方法保持一致的處理模式，創建jobShiftItems變數
	var jobShiftItems []JobShiftItem
	for _, item := range req.JobShiftItems {
		newItem, err := s.ToJobShiftItem(item, serviceLocation.Timezone)
		if err != nil {
			return err
		}
		jobShiftItems = append(jobShiftItems, newItem)
	}

	// 更新工作職位
	jobEditReq := JobEditReq{
		Draft:              req.Draft,
		FacilityId:         req.FacilityId,
		JobId:              job.Id,
		PositionProfession: req.PositionProfession,
		NumberOfPeople:     req.NumberOfPeople,
		ServiceLocationId:  req.ServiceLocationId,
		MinExperienceLevel: req.MinExperienceLevel,
		PreferredGrade:     req.PreferredGrade,
		Specialisation:     req.Specialisation,
		Language:           req.Language,
		SupervisionLevel:   req.SupervisionLevel,

		SplitType: model.JobSplitTypeNo,

		Benefits:        req.Benefits,
		ShiftAllocation: req.ShiftAllocation,
		Remark:          req.Remark,
		JobShiftItems:   jobShiftItems,
		UpdatedUserId:   req.UpdatedUserId,
		Qualification:   req.Qualification,
	}

	// 更新工作模板
	if err = JobService.Edit(db, jobEditReq); err != nil {
		return err
	}

	// 更新排程日期
	if err = s.UpdateJobScheduleDate(db, req.FacilityId, schedule.Id, serviceLocation.Timezone); err != nil {
		return err
	}

	// 發送發佈任務隊列
	if req.Draft == "N" {
		if err = s.SendPublishTask(schedule.Id); err != nil {
			return err
		}
	}
	return nil
}

// endregion ---------------------------------------------------- Edit ----------------------------------------------------

// region ---------------------------------------------------- UpdateStatus ----------------------------------------------------

// JobScheduleUpdateStatusReq 更新工作排程狀態請求
type JobScheduleUpdateStatusReq struct {
	FacilityId    uint64 `json:"facilityId" binding:"required"`                  // 機構Id
	JobScheduleId uint64 `json:"jobScheduleId" binding:"required"`               // 排程Id
	Status        string `json:"status" binding:"required,oneof=ENABLE DISABLE"` // 狀態 ENABLE DISABLE
	CancelJob     string `json:"cancelJob" binding:"omitempty,oneof=Y N"`        // 是否取消工作 Y N
}

// 檢查是否可以更新工作排程狀態
func (s *jobScheduleService) CheckCanUpdateStatus(db *gorm.DB, jobScheduleId uint64, targetStatus string) (bool, i18n.Message, error) {
	var err error
	var jobSchedule model.JobSchedule

	// 檢查工作排程是否存在
	if err = db.First(&jobSchedule, jobScheduleId).Error; xgorm.IsSqlErr(err) {
		return false, i18n.Message{}, err
	}

	// 如果當前狀態與目標狀態相同，無需更新
	if jobSchedule.Status == targetStatus {
		return false, i18n.Message{
			ID:    "checker.job_schedule.status.same",
			Other: "The schedule status is already set to the target status.",
		}, nil
	}
	if targetStatus == model.JobScheduleStatusDisable {
		// 所有計劃已經全部發佈，不能更新狀態
		if err = db.Table("job_schedule_date AS jsd").
			Select("jsd.id").
			Where("jsd.job_schedule_id = ?", jobScheduleId).
			Where("jsd.status = ?", model.JobScheduleDateStatusPending).
			First(&model.JobScheduleDate{}).Error; xgorm.IsSqlErr(err) {
			return false, i18n.Message{}, err
		}
		if xgorm.IsNotFoundErr(err) {
			return false, i18n.Message{
				ID:    "checker.job_schedule.status.has_published_job",
				Other: "The completed publication schedule cannot be stopped.",
			}, nil
		} else {
			return true, i18n.Message{}, nil
		}
	}
	return true, i18n.Message{}, nil
}

// UpdateJobScheduleStatus 更新工作排程狀態
func (s *jobScheduleService) UpdateJobScheduleStatus(db *gorm.DB, req JobScheduleUpdateStatusReq) error {
	var err error

	// 檢查工作排程是否存在
	var jobSchedule model.JobSchedule
	if err = db.First(&jobSchedule, req.JobScheduleId).Error; err != nil {
		return err
	}

	updateMap := map[string]interface{}{
		"status":      req.Status,
		"update_time": xtype.NotNullString(time.Now().Format(xtool.DateTimeSecA1)),
	}

	// 保存工作排程
	if err = db.Model(&jobSchedule).Where("id = ?", req.JobScheduleId).Updates(updateMap).Error; err != nil {
		return err
	}
	if req.Status == model.JobScheduleStatusDisable && req.CancelJob == "Y" {
		// 查詢工作申請記錄，無人申請的jobId，取消工作
		var jobIds []uint64
		if err := db.Table("job AS j").
			Joins("JOIN job_schedule AS js ON js.id = j.job_schedule_id AND js.id = ?", req.JobScheduleId).
			Joins("LEFT JOIN job_application AS ja ON ja.job_id = j.id AND ja.facility_id = ?", req.FacilityId).
			Select("j.id").
			Where("js.id = ?", req.JobScheduleId).
			Where("ja.id IS NULL").
			Distinct().
			Scan(&jobIds).Error; err != nil {
			return err
		}

		for _, jobId := range jobIds {
			if err := JobService.UpdateStatus(db, JobUpdateStatusReq{
				FacilityId:   req.FacilityId,
				JobId:        jobId,
				Status:       model.JobStatusCancel,
				CancelReason: "Schedule Posting Stopped",
			}); err != nil {
				return err
			}
		}
	}
	return nil
}

// endregion ---------------------------------------------------- UpdateStatus ----------------------------------------------------

// region ---------------------------------------------------- List ----------------------------------------------------

// JobScheduleListReq 查詢工作排程列表請求
type JobScheduleListReq struct {
	FacilityId         uint64 `form:"facilityId" binding:"required"`                                                 // 機構Id
	Draft              string `form:"draft" binding:"required,oneof=Y N"`                                            // 是否草稿 Y N
	PositionProfession string `form:"positionProfession" binding:"omitempty"`                                        // 職位
	Status             string `form:"status" binding:"omitempty,oneof=NOT_STARTED TERMINATED IN_PROGRESS COMPLETED"` // 狀態 NOT_STARTED TERMINATED IN_PROGRESS COMPLETED, 草稿無效
}

type JobScheduleListResp struct {
	JobScheduleId      uint64                 `json:"jobScheduleId"`           // 排程Id
	JobId              uint64                 `json:"-"`                       // 工作Id
	FacilityId         uint64                 `json:"facilityId"`              // 機構Id
	ServiceLocationId  uint64                 `json:"serviceLocationId"`       // 服務地點Id
	Timezone           string                 `json:"-"`                       // 服務地點時區
	Name               string                 `json:"name"`                    // 排程名稱
	PositionProfession string                 `json:"positionProfession"`      // 職位
	RepeatType         string                 `json:"repeatType"`              // 重複類型
	BeginDate          string                 `json:"beginDate"`               // 計劃開始日期
	EndDate            string                 `json:"endDate"`                 // 計劃結束日期
	Status             string                 `json:"status"`                  // 計劃狀態
	JobShiftItems      []JobScheduleShiftItem `json:"jobShiftItems" gorm:"-"`  // 班次時間
	Total              int32                  `json:"total"`                   // 總工作數量
	PublishedCount     int32                  `json:"publishedCount"`          // 已發佈工作數量
	PendingCount       int32                  `json:"pendingCount"`            // 待發佈工作數量
	ScheduleStatus     string                 `json:"scheduleStatus" gorm:"-"` // 排程狀態 NOT_STARTED TERMINATED IN_PROGRESS COMPLETED

	// 日重複設定
	DailyInterval int32 `json:"dailyInterval"` // 每幾天
	// 週重複設定
	WeeklyInterval int32  `json:"weeklyInterval"`                                             // 每幾週
	WeekDays       string `json:"weekDays" binding:"omitempty,max=255,splitin=1 2 3 4 5 6 7"` // 週幾，多個以逗號分隔 1-7
	// 月重複設定
	MonthlyInterval   int32  `json:"monthlyInterval"`   // 每幾個月
	MonthlyType       string `json:"monthlyType"`       // 按日期還是按週幾 DAY WEEKDAY
	MonthlyDayOfMonth int32  `json:"monthlyDayOfMonth"` // 指定日期 1-31
	MonthlyWeekIndex  int32  `json:"monthlyWeekIndex"`  // 第幾週 1-5
	MonthlyWeekDay    int32  `json:"monthlyWeekDay"`    // 週幾 1-7
}

func (s *jobScheduleService) GetScheduleStatus(status string, publishedCount int32, total int32) string {
	if status == model.JobScheduleStatusDisable {
		return JobScheduleStatusTerminated
	}
	if publishedCount == total {
		return JobScheduleStatusCompleted
	} else {
		if publishedCount == 0 {
			return JobScheduleStatusNotStarted
		} else if publishedCount < total {
			return JobScheduleStatusInProgress
		} else {
			return JobScheduleStatusCompleted
		}
	}
}

// 獲取工作的排程列表
func (s *jobScheduleService) List(db *gorm.DB, req JobScheduleListReq, pageSet *xresp.PageSet, sortSet xresp.SortingSet) ([]JobScheduleListResp, error) {
	var err error
	var resp []JobScheduleListResp

	jobScheduleDateBuilder := db.Table("job_schedule_date AS jsd").
		Select([]string{
			"jsd.job_schedule_id",
			"jsd.facility_id",
			"COUNT(jsd.id) AS total",
			"SUM(CASE WHEN jsd.status = 'PENDING' THEN 1 ELSE 0 END) AS pending_count",
			"SUM(CASE WHEN jsd.status = 'PUBLISHED' THEN 1 ELSE 0 END) AS published_count",
		}).
		Where("jsd.facility_id = ?", req.FacilityId).
		Group("jsd.job_schedule_id")

	builder := db.Table("job_schedule AS js").
		Joins("JOIN job AS j ON j.job_schedule_id = js.id AND j.schedule_template = ?", model.JobScheduleTemplateY).
		Joins("LEFT JOIN (?) AS jsd ON jsd.job_schedule_id = js.id AND jsd.facility_id = js.facility_id", jobScheduleDateBuilder).
		Joins("LEFT JOIN service_location AS sl ON sl.id = j.service_location_id").
		Select([]string{
			"js.id as job_schedule_id",
			"j.id as job_id",
			"js.facility_id",
			"js.name",
			"js.position_profession",
			"js.repeat_type",
			"js.begin_date",
			"js.end_date",
			"js.status",
			"jsd.total",
			"jsd.published_count",
			"jsd.pending_count",
			"js.daily_interval",
			"js.weekly_interval",
			"js.week_days",
			"js.monthly_interval",
			"js.monthly_type",
			"js.monthly_day_of_month",
			"js.monthly_week_index",
			"js.monthly_week_day",
			"sl.timezone",
		}).
		Where("js.facility_id = ?", req.FacilityId)
	if req.PositionProfession != "" {
		builder = builder.Where("js.position_profession = ?", req.PositionProfession)
	}
	if req.Draft == "Y" {
		builder = builder.Where("js.status = ?", model.JobScheduleStatusDisable).Where("j.status = ?", model.JobStatusPending)
	} else {
		builder = builder.Where("(j.status <> ?)", model.JobStatusPending)
		switch req.Status {
		case JobScheduleStatusNotStarted: // 還未發佈第一個job的計劃
			builder = builder.Where("jsd.published_count = ?", 0).Where("js.status = ?", model.JobScheduleStatusEnable)
		case JobScheduleStatusTerminated: // 被終止的計劃
			builder = builder.Where("jsd.published_count > jsd.pending_count").Where("js.status = ?", model.JobScheduleStatusDisable)
		case JobScheduleStatusInProgress: // 已經有發佈工作的計劃
			builder = builder.Where("jsd.total > jsd.published_count AND jsd.published_count > ?", 0).Where("js.status = ?", model.JobScheduleStatusEnable)
		case JobScheduleStatusCompleted: // 已經發佈完所有工作的計劃
			builder = builder.Where("jsd.total = jsd.published_count").Where("js.status = ?", model.JobScheduleStatusEnable)
		}
	}
	sortKeyList := map[string]string{
		"createTime": "js.create_time",
		"beginTime":  "js.begin_date",
	}
	if err = builder.
		Scopes(xresp.AddOrder(sortSet, sortKeyList)).
		Order("js.begin_date DESC").
		Order("js.id ASC").
		Group("js.id").
		Scopes(xresp.Paginate(pageSet)).
		Find(&resp).Error; err != nil {
		return resp, err
	}

	jobIds := make([]uint64, 0)
	for _, item := range resp {
		jobIds = append(jobIds, item.JobId)
	}

	var professionSectionMap map[string]string
	var jobShiftItemsMap map[uint64][]JobShiftItem
	if len(resp) > 0 {
		professionSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
		if err != nil {
			return resp, err
		}

		jobShiftItemsMap, err = JobService.GetJobShiftItems(db, req.FacilityId, jobIds)
		if err != nil {
			return resp, err
		}
	}

	for i := range resp {
		resp[i].ScheduleStatus = s.GetScheduleStatus(resp[i].Status, resp[i].PublishedCount, resp[i].Total)

		if jobShiftItems, ok := jobShiftItemsMap[resp[i].JobId]; ok {
			var jobShiftItemsResp []JobScheduleShiftItem
			for _, item := range jobShiftItems {
				jobScheduleShiftItem, err := s.ToJobScheduleShiftItem(item, resp[i].Timezone)
				if err != nil {
					return resp, err
				}
				jobShiftItemsResp = append(jobShiftItemsResp, jobScheduleShiftItem)
			}
			resp[i].JobShiftItems = jobShiftItemsResp
		} else {
			resp[i].JobShiftItems = make([]JobScheduleShiftItem, 0)
		}

		if resp[i].PositionProfession != "" {
			resp[i].PositionProfession = professionSectionMap[resp[i].PositionProfession]
		}
	}
	return resp, nil
}

// endregion ---------------------------------------------------- List ----------------------------------------------------

// region ---------------------------------------------------- Inquire ----------------------------------------------------

// JobScheduleInquireReq 查詢工作排程詳情請求
type JobScheduleInquireReq struct {
	FacilityId    uint64 `form:"facilityId" binding:"required"`    // 機構Id
	JobScheduleId uint64 `form:"jobScheduleId" binding:"required"` // 排程Id
}

// JobScheduleInquireResp 查詢工作排程詳情響應
type JobScheduleInquireResp struct {
	// 排程基本信息
	JobScheduleId     uint64 `json:"jobScheduleId"`     // 排程Id
	JobId             uint64 `json:"-"`                 // 工作Id
	FacilityId        uint64 `json:"facilityId"`        // 機構Id
	Name              string `json:"name"`              // 排程名稱
	RepeatType        string `json:"repeatType"`        // 重複類型
	BeginDate         string `json:"beginDate"`         // 計劃開始日期
	EndDate           string `json:"endDate"`           // 計劃結束日期
	AdvanceDays       int32  `json:"advanceDays"`       // 提前幾天發佈
	Status            string `json:"status"`            // 計劃狀態
	CreateTime        string `json:"createTime"`        // 創建時間
	UpdateTime        string `json:"updateTime"`        // 更新時間
	DailyInterval     int32  `json:"dailyInterval"`     // 每幾天
	WeeklyInterval    int32  `json:"weeklyInterval"`    // 每幾週
	WeekDays          string `json:"weekDays"`          // 週幾，多個以逗號分隔 1-7
	MonthlyInterval   int32  `json:"monthlyInterval"`   // 每幾個月
	MonthlyType       string `json:"monthlyType"`       // 按日期還是按週幾
	MonthlyDayOfMonth int32  `json:"monthlyDayOfMonth"` // 指定日期
	MonthlyWeekIndex  int32  `json:"monthlyWeekIndex"`  // 第幾週
	MonthlyWeekDay    int32  `json:"monthlyWeekDay"`    // 週幾 1-7

	// 關聯工作信息
	PositionProfession     string `json:"positionProfession"`     // 職位專業
	NumberOfPeople         int32  `json:"numberOfPeople"`         // 所需人數
	ServiceLocationId      uint64 `json:"serviceLocationId"`      // 服務地點Id
	Timezone               string `json:"timezone"`               // 服務地點時區
	ServiceLocationAddress string `json:"serviceLocationAddress"` // 服務地點地址
	JobStatus              string `json:"jobStatus"`              // 工作狀態
	MinExperienceLevel     string `json:"minExperienceLevel"`     // 最低職級要求
	PreferredGrade         string `json:"preferredGrade"`         // 首選級別
	Qualification          string `json:"qualification"`          // 護理資格
	Specialisation         string `json:"specialisation"`         // 專業要求
	Language               string `json:"language"`               // 語言要求
	SupervisionLevel       string `json:"supervisionLevel"`       // 監督級別

	Duration        decimal.Decimal        `json:"duration"`                // 總工作時長（小時）
	PayHours        decimal.Decimal        `json:"payHours"`                // 支付時長（小時）
	Benefits        string                 `json:"benefits"`                // 福利
	ShiftAllocation string                 `json:"shiftAllocation"`         // 班次分配方式
	Remark          string                 `json:"remark"`                  // 備註
	BeginTime       string                 `json:"beginTime"`               // 工作開始時間
	EndTime         string                 `json:"endTime"`                 // 工作結束時間
	JobShiftItems   []JobScheduleShiftItem `json:"jobShiftItems" gorm:"-"`  // 班次時間
	ScheduleStatus  string                 `json:"scheduleStatus" gorm:"-"` // 排程狀態
	PendingCount    int32                  `json:"pendingCount" gorm:"-"`   // 待發佈工作數量
	Total           int32                  `json:"total" gorm:"-"`          // 總工作數量
	Draft           string                 `json:"draft" gorm:"-"`          // 是否草稿 Y N
}

type JobScheduleDateSummary struct {
	JobScheduleId  uint64 `json:"jobScheduleId"`
	FacilityId     uint64 `json:"facilityId"`
	Total          int32  `json:"total"`
	PublishedCount int32  `json:"publishedCount"`
	PendingCount   int32  `json:"pendingCount"`
}

// 查詢工作排程詳情
func (s *jobScheduleService) Inquire(db *gorm.DB, req JobScheduleInquireReq) (JobScheduleInquireResp, error) {
	var resp JobScheduleInquireResp
	var schedule model.JobSchedule

	// 查詢排程信息
	if err := db.
		Where("facility_id = ?", req.FacilityId).
		Where("id = ?", req.JobScheduleId).
		First(&schedule).Error; err != nil {
		return resp, err
	}

	// 查詢相關工作信息
	var job model.Job
	if err := db.Where("job_schedule_id = ?", schedule.Id).
		Where("schedule_template = ?", model.JobScheduleTemplateY).
		Where("facility_id = ?", req.FacilityId).
		First(&job).Error; err != nil {
		return resp, err
	}

	// 獲取服務地點地址
	var serviceLocation model.ServiceLocation
	if job.ServiceLocationId > 0 {
		if err := db.
			Where("facility_id = ?", req.FacilityId).
			Where("id = ?", job.ServiceLocationId).
			First(&serviceLocation).Error; xgorm.IsSqlErr(err) {
			return resp, err
		}
	}

	// 填充排程信息
	_ = copier.Copy(&resp, &schedule)
	resp.JobScheduleId = schedule.Id

	// 填充工作信息
	_ = copier.Copy(&resp, &job)
	resp.JobId = job.Id
	resp.ServiceLocationAddress = serviceLocation.Address
	resp.Timezone = serviceLocation.Timezone
	resp.ServiceLocationId = serviceLocation.Id
	resp.Draft = "N"
	if job.Status == model.JobStatusPending {
		resp.Draft = "Y"
	}

	// 加載班次時間
	jobShiftItems, err := JobService.LoadJobShiftItems(db, resp.FacilityId, resp.JobId)
	if err != nil {
		return resp, err
	}
	resp.JobShiftItems = make([]JobScheduleShiftItem, len(jobShiftItems))
	for i, item := range jobShiftItems {
		resp.JobShiftItems[i], err = s.ToJobScheduleShiftItem(item, resp.Timezone)
		if err != nil {
			return resp, err
		}
	}

	// 獲取工作排程日期摘要
	var jobScheduleDateSummary JobScheduleDateSummary
	if err := db.Table("job_schedule_date AS jsd").
		Select([]string{
			"jsd.job_schedule_id",
			"jsd.facility_id",
			"COUNT(jsd.id) AS total",
			"SUM(CASE WHEN jsd.status = 'PENDING' THEN 1 ELSE 0 END) AS pending_count",
			"SUM(CASE WHEN jsd.status = 'PUBLISHED' THEN 1 ELSE 0 END) AS published_count",
		}).
		Where("jsd.facility_id = ?", req.FacilityId).
		Where("jsd.job_schedule_id = ?", schedule.Id).
		Group("jsd.job_schedule_id").
		Scan(&jobScheduleDateSummary).Error; err != nil {
		return resp, err
	}
	resp.PendingCount = jobScheduleDateSummary.PendingCount
	resp.Total = jobScheduleDateSummary.Total
	resp.ScheduleStatus = s.GetScheduleStatus(schedule.Status, jobScheduleDateSummary.PublishedCount, jobScheduleDateSummary.Total)

	// 獲取福利的名稱
	if err = db.Table("benefit AS b").
		Joins("JOIN job_benefit AS jb ON b.id = jb.benefit_id").
		Where("jb.job_id = ?", resp.JobId).
		Select("GROUP_CONCAT(b.id) AS benefits").
		Group("jb.job_id").
		Pluck("benefits", &resp.Benefits).Error; err != nil {
		return resp, err
	}

	return resp, nil
}

// endregion ---------------------------------------------------- Inquire ----------------------------------------------------

// region ---------------------------------------------------- Lock ----------------------------------------------------
// 鎖定工作排程記錄
func (s *jobScheduleService) LockFacilityJobScheduleRecord(db *gorm.DB, facilityId uint64) error {
	if err := db.
		Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("facility_id = ?", facilityId).
		Find(&[]model.JobSchedule{}).Error; xgorm.IsSqlErr(err) {
		return err
	}
	return nil
}

// endregion ---------------------------------------------------- Lock ----------------------------------------------------

// region ---------------------------------------------------- Delete ----------------------------------------------------

// JobScheduleDeleteReq 刪除工作排程請求
type JobScheduleDeleteReq struct {
	FacilityId    uint64 `json:"facilityId" binding:"required"`    // 所屬機構Id
	JobScheduleId uint64 `json:"jobScheduleId" binding:"required"` // 工作排程Id
}

// 檢查是否可以刪除工作排程
func (s *jobScheduleService) CheckCanDelete(db *gorm.DB, facilityId uint64, jobScheduleId uint64) (bool, i18n.Message, error) {
	var err error
	var jobSchedule model.JobSchedule

	// 檢查工作排程是否存在
	if err = db.Where("facility_id = ?", facilityId).First(&jobSchedule, jobScheduleId).Error; err != nil {
		if xgorm.IsNotFoundErr(err) {
			return false, i18n.Message{
				ID:    "checker.job_schedule.id.does_not_exist",
				Other: "No such schedule record, please try after reloading.",
			}, nil
		}
		return false, i18n.Message{}, err
	}

	// 檢查是否有已發佈的工作日期, 如果有則不能刪除
	if err = db.Model(&model.JobScheduleDate{}).
		Where("job_schedule_id = ?", jobScheduleId).
		Where("status = ?", model.JobScheduleDateStatusPublished).
		First(&model.JobScheduleDate{}).Error; xgorm.IsSqlErr(err) {
		return false, i18n.Message{}, err
	}
	if xgorm.IsNotFoundErr(err) {
		return true, i18n.Message{}, nil
	} else {
		return false, i18n.Message{
			ID:    "checker.job_schedule.cannot_delete.published",
			Other: "The schedule has published jobs and cannot be deleted.",
		}, nil
	}
}

// Delete 刪除工作排程
func (s *jobScheduleService) Delete(db *gorm.DB, req JobScheduleDeleteReq) error {
	var err error
	// 刪除相關的工作排程日期
	if err = db.
		Where("facility_id = ?", req.FacilityId).
		Where("job_schedule_id = ?", req.JobScheduleId).
		Delete(&model.JobScheduleDate{}).Error; err != nil {
		return err
	}

	// 刪除模板工作的班次時間
	var job model.Job
	if err = db.
		Where("job_schedule_id = ?", req.JobScheduleId).
		Where("schedule_template = ?", model.JobScheduleTemplateY).
		First(&job).Error; err == nil {
		if err = db.
			Where("facility_id = ?", req.FacilityId).
			Where("job_id = ?", job.Id).
			Delete(&model.JobShift{}).Error; err != nil {
			return err
		}

		// 刪除模板工作
		if err = db.
			Where("facility_id = ?", req.FacilityId).
			Where("id = ?", job.Id).
			Delete(&job).Error; err != nil {
			return err
		}
	} else if !xgorm.IsNotFoundErr(err) {
		return err
	}

	// 刪除工作排程
	if err = db.
		Where("facility_id = ?", req.FacilityId).
		Where("id = ?", req.JobScheduleId).
		Delete(&model.JobSchedule{}).Error; err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- Delete ----------------------------------------------------

// 查詢計劃將生成的工作數量（根據預填參數，未生成計劃）

// JobSchedulePotentialCountReq 查詢潛在工作數量請求
type JobSchedulePotentialCountReq struct {
	// 基本資訊
	FacilityId        uint64 `json:"facilityId" binding:"required"`        // 所屬機構Id
	JobScheduleId     uint64 `json:"jobScheduleId" binding:"omitempty"`    // 排程Id, 編輯模式下需要提供
	AdvanceDays       int32  `json:"advanceDays" binding:"omitempty"`      // 提前幾天發佈
	ServiceLocationId uint64 `json:"serviceLocationId" binding:"required"` // 服務地點Id

	// 排程相關資訊
	RepeatType string `json:"repeatType" binding:"required,max=32,oneof=DAILY WEEKLY MONTHLY"` // 重複類型 DAILY WEEKLY MONTHLY
	BeginDate  string `json:"beginDate" binding:"required,datetime=2006-01-02"`                // 計劃開始日期(YYYY-MM-DD)
	EndDate    string `json:"endDate" binding:"required,datetime=2006-01-02"`                  // 計劃結束日期(YYYY-MM-DD)

	// 日重複設定
	DailyInterval int32 `json:"dailyInterval" binding:"required_if=RepeatType DAILY,omitempty,min=1"` // 每幾天

	// 週重複設定
	WeeklyInterval int32  `json:"weeklyInterval" binding:"required_if=RepeatType WEEKLY,omitempty,min=1"`                   // 每幾週
	WeekDays       string `json:"weekDays" binding:"required_if=RepeatType WEEKLY,omitempty,max=255,splitin=1 2 3 4 5 6 7"` // 週幾，多個以逗號分隔 1-7

	// 月重複設定
	MonthlyType       string `json:"monthlyType" binding:"required_if=RepeatType MONTHLY,omitempty,max=32,oneof=DAY WEEKDAY"`                 // 按日期還是按週幾 DAY WEEKDAY
	MonthlyInterval   int32  `json:"monthlyInterval" binding:"required_if=RepeatType MONTHLY,omitempty,min=1"`                                // 每幾日/週
	MonthlyDayOfMonth int32  `json:"monthlyDayOfMonth" binding:"required_if_and=RepeatType MONTHLY;MonthlyType DAY,omitempty,min=1,max=31"`   // 日-指定日 1-31
	MonthlyWeekIndex  int32  `json:"monthlyWeekIndex" binding:"required_if_and=RepeatType MONTHLY;MonthlyType WEEKDAY,omitempty,min=1,max=5"` // 週-第幾週 1-5, 5=該月最後一週
	MonthlyWeekDay    int32  `json:"monthlyWeekDay" binding:"required_if_and=RepeatType MONTHLY;MonthlyType WEEKDAY,omitempty,min=1,max=7"`   // 週-週幾 1-7

	// 班次時間
	JobShiftItems []JobScheduleShiftItem `json:"jobShiftItems" binding:"required,min=1,dive"` // 班次時間
}

// JobSchedulePotentialCountResp 查詢潛在工作數量響應
type JobSchedulePotentialCountResp struct {
	PotentialJobCount int32 `json:"potentialJobCount"` // 潛在工作數量
	ImmediateJobCount int32 `json:"immediateJobCount"` // 立即生成的工作數量
}

// CheckHasValidJobDates 檢查是否有有效的工作日期
func (s *jobScheduleService) CheckHasValidJobDates(db *gorm.DB, req JobScheduleCreateReq) (bool, i18n.Message, error) {
	// 創建一個臨時請求對象
	countReq := JobSchedulePotentialCountReq{
		FacilityId:        req.FacilityId,
		ServiceLocationId: req.ServiceLocationId,
		RepeatType:        req.RepeatType,
		BeginDate:         req.BeginDate,
		EndDate:           req.EndDate,
		DailyInterval:     req.DailyInterval,
		WeeklyInterval:    req.WeeklyInterval,
		WeekDays:          req.WeekDays,
		MonthlyInterval:   req.MonthlyInterval,
		MonthlyType:       req.MonthlyType,
		MonthlyDayOfMonth: req.MonthlyDayOfMonth,
		MonthlyWeekIndex:  req.MonthlyWeekIndex,
		MonthlyWeekDay:    req.MonthlyWeekDay,
		JobShiftItems:     req.JobShiftItems,
	}

	// 計算有效工作日期
	resp, err := s.CalcPotentialJobCount(db, countReq)
	if err != nil {
		return false, i18n.Message{}, err
	}

	// 如果沒有有效的工作日期，返回錯誤
	if resp.PotentialJobCount <= 0 {
		return false, i18n.Message{
			ID:    "job_schedule.no_valid_dates",
			Other: "No valid job dates will be generated with the current settings.",
		}, nil
	}

	return true, i18n.Message{}, nil
}

// 計算排程可能生成的工作數量
func (s *jobScheduleService) CalcPotentialJobCount(db *gorm.DB, req JobSchedulePotentialCountReq) (JobSchedulePotentialCountResp, error) {
	var resp JobSchedulePotentialCountResp
	var err error

	var serviceLocation model.ServiceLocation
	if err = db.Model(&model.ServiceLocation{}).
		Where("id = ?", req.ServiceLocationId).
		First(&serviceLocation).Error; err != nil {
		return resp, err
	}
	tz, err := time.LoadLocation(serviceLocation.Timezone)
	if err != nil {
		return resp, err
	}

	beginDate, err := time.ParseInLocation(xtool.DateDayA, req.BeginDate, tz)
	if err != nil {
		return resp, err
	}
	endDate, err := time.ParseInLocation(xtool.DateDayA, req.EndDate, tz)
	if err != nil {
		return resp, err
	}
	startTime := time.Date(beginDate.Year(), beginDate.Month(), beginDate.Day(), 0, 0, 0, 0, tz)
	endTime := time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 0, 0, 0, 0, tz)
	// 創建一個臨時的 JobSchedule 對象
	schedule := model.JobSchedule{
		RepeatType:        req.RepeatType,
		BeginDate:         xtype.NewNullDate(req.BeginDate),
		EndDate:           xtype.NewNullDate(req.EndDate),
		DailyInterval:     req.DailyInterval,
		WeeklyInterval:    req.WeeklyInterval,
		WeekDays:          req.WeekDays,
		MonthlyInterval:   req.MonthlyInterval,
		MonthlyType:       req.MonthlyType,
		MonthlyDayOfMonth: req.MonthlyDayOfMonth,
		MonthlyWeekIndex:  req.MonthlyWeekIndex,
		MonthlyWeekDay:    req.MonthlyWeekDay,
	}

	// 檢查日期範圍
	if startTime.After(endTime) {
		return resp, errors.New("begin date must be before end date")
	}

	// 查找 JobShiftItems 中最早的開始時間
	var earliestShiftTime time.Duration
	if len(req.JobShiftItems) > 0 {
		earliestShiftTime = 24 * time.Hour // 默認最大值
		for _, item := range req.JobShiftItems {
			if item.BeginTime == "" {
				continue
			}

			// 解析時間字符串 (格式如: "09:00")
			parts := strings.Split(item.BeginTime, ":")
			if len(parts) != 2 {
				continue
			}

			hour, errHour := strconv.Atoi(parts[0])
			minute, errMinute := strconv.Atoi(parts[1])
			if errHour != nil || errMinute != nil {
				continue
			}

			// 計算從午夜開始的分鐘數
			shiftTime := time.Duration(hour)*time.Hour + time.Duration(minute)*time.Minute
			if shiftTime < earliestShiftTime {
				earliestShiftTime = shiftTime
			}
		}
	}

	// 計算工作日期
	jobDates, err := s.CalcJobScheduleDates(schedule, startTime, serviceLocation.Timezone)
	if err != nil {
		return resp, err
	}

	// 獲取當前時間和當前日期字符串
	nowTime := time.Now().In(tz)
	nowDateStr := nowTime.Format(xtool.DateDayA)

	// 如果是編輯模式，查詢已發佈的日期
	var publishedDates []string
	if req.JobScheduleId > 0 {
		// 查詢已發佈的日期
		if err = db.Model(&model.JobScheduleDate{}).
			Where("facility_id = ?", req.FacilityId).
			Where("job_schedule_id = ?", req.JobScheduleId).
			Where("status = ?", model.JobScheduleDateStatusPublished).
			Order("date ASC").
			Pluck("date", &publishedDates).Error; err != nil {
			return resp, err
		}
	}

	// 獲取 advance_days 參數
	advanceDays := req.AdvanceDays

	// 過濾掉已過期的日期和已發佈的日期
	validJobDates := make([]string, 0)
	immediateJobDates := make([]string, 0)

	for _, dateStr := range jobDates {
		// 如果是已發佈的日期，則跳過不計算（在編輯模式中使用）
		if req.JobScheduleId > 0 && lo.Contains(publishedDates, dateStr) {
			continue
		}

		// 解析工作日期
		jobDate, err := time.ParseInLocation(xtool.DateDayA, dateStr, tz)
		if err != nil {
			continue
		}

		// 計算實際班次開始時間 (日期 + 最早班次時間)
		jobStartDateTime := jobDate.Add(earliestShiftTime)

		// 計算提前發佈截止時間 (班次時間 - 1小時)
		publishDateTime := jobStartDateTime.Add(-1 * time.Hour)

		// 如果提前發佈時間已經過去，則跳過此日期
		if publishDateTime.Before(nowTime) {
			continue
		}

		validJobDates = append(validJobDates, dateStr)

		// 計算立即發佈的工作數量
		// 參考 job_schedule_publish_task.go 中的條件：
		// 1. 日期在當前日期之後
		// 2. 距離發佈日期（當前日期）小於等於 advance_days
		if dateStr >= nowDateStr {
			// 計算日期與當前日期的差距（天數），保留小數部分
			dayDiffHours := jobDate.Sub(nowTime.Truncate(24 * time.Hour)).Hours()
			dayDiff := dayDiffHours / 24.0

			// 如果日期與當前日期的差距小於等於 advance_days，則為立即發佈
			if dayDiff <= float64(advanceDays) {
				immediateJobDates = append(immediateJobDates, dateStr)
			}
		}
	}

	// 設置響應
	resp.PotentialJobCount = int32(len(validJobDates))
	resp.ImmediateJobCount = int32(len(immediateJobDates))

	return resp, nil
}

func (s *jobScheduleService) SendPublishTask(jobScheduleId uint64) error {
	req := map[string]uint64{
		"jobScheduleId": jobScheduleId,
	}
	str, _ := json.Marshal(req)
	err := xamqp.SendTask(JobSchedulePublishTask, xamqp.Task{
		MessageId: JobSchedulePublishTask,
		TaskId:    JobSchedulePublishTask + "_" + uuid.NewV4().String(),
		Data:      string(str),
	})
	if err != nil {
		return err
	}
	return nil
}

// 將Go的Weekday（0-6）轉換為ISO標準（1-7）
func convertWeekDay(goWeekDay int) int {
	// Go的Weekday: 0=週日, 1=週一, ..., 6=週六
	// ISO 8601: 1=週一, 2=週二, ..., 7=週日
	if goWeekDay == 0 {
		return 7 // 將週日從0轉換為7
	}
	return goWeekDay // 其他天數保持不變，因為Go的1-6正好對應ISO的1-6
}

// 將ISO標準（1-7）轉換為Go的Weekday（0-6）
func convertToGoWeekDay(isoWeekDay int) int {
	// ISO 8601: 1=週一, 2=週二, ..., 7=週日
	// Go的Weekday: 0=週日, 1=週一, ..., 6=週六
	if isoWeekDay == 7 {
		return 0 // 將週日從7轉換為0
	}
	return isoWeekDay // 其他天數保持不變，因為ISO的1-6正好對應Go的1-6
}
