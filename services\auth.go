package services

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xhermes"
	"github.com/Norray/xrocket/xi18n"
	"github.com/Norray/xrocket/xredis"
	"github.com/matcornic/hermes/v2"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"

	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xjwt"
	"github.com/Norray/xrocket/xmail"
	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xtool"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gorm.io/gorm"
)

var canNotFoundRefreshMsg = i18n.Message{
	ID:    "alerter.refresh_token.can_not_found",
	Other: "Authorization timed out, please reauthorize.",
}

var systemForLogin = map[string][]string{
	"SYSTEM": {model.UserUserTypeSuperAdmin, model.UserUserTypeSystemAdmin},    // 系統管理員
	"USER":   {model.UserUserTypeFacilityUser, model.UserUserTypeProfessional}, // 用戶
}

const (
	cacheRefreshTokenUserInfo = "cache:refresh_token:user_info:%s"
)

const (
	companyUserForgetPasswordKeyPrefix = "validation:company_user:forget_password:%s:%s" // %s: uuid, %s: type
	companyUserForgetPasswordUuid      = "uuid"
	companyUserForgetPasswordUser      = "user"
	companyUserForgetPasswordCacheTime = 30 * time.Minute
)

var AuthService = new(authService)

type authService struct{}

// region ---------- Checker ----------

func (s *authService) CheckEmailExist(db *gorm.DB, m *xmodel.User, email string) (bool, i18n.Message, error) {
	var err error
	msg := i18n.Message{
		ID:    "checker.user.email.does_not_exist",
		Other: "This email address is not registered.",
	}

	// 只檢查專業人士和機構用戶
	err = db.Where("email = ?", email).Where("user_type IN (?)", []string{model.UserUserTypeFacilityUser, model.UserUserTypeProfessional}).First(&m).Error
	if xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	}
	// 如果是機構用戶，必須是機構主用戶
	if m.UserType == model.UserUserTypeFacilityUser {
		var facilityUser model.FacilityUser
		err = db.Where("user_id = ?", m.Id).Where("primary_user = ?", model.FacilityUserPrimaryUserY).First(&facilityUser).Error
		if xgorm.IsSqlErr(err) {
			return false, msg, err
		}
		if xgorm.IsNotFoundErr(err) {
			return false, msg, nil
		}
	}
	return true, i18n.Message{}, nil
}

// endregion ---------- Checker ----------

// region ---------- 登入 ----------

type LoginReq struct {
	Username     string `json:"username" binding:"required_without=RefreshToken"` //賬號
	Password     string `json:"password" binding:"required_without=RefreshToken"` //密碼
	Dk           string `json:"dk" binding:"required"`                            //設備
	System       string `json:"system" binding:"required,oneof=SYSTEM USER"`      // 系統 SYSTEM:系統管理員 USER:機構和專業人士
	RefreshToken string `json:"refreshToken"`                                     // 如果傳了，則無視 username 和 password 的值
}

type LoginResp struct {
	TK     string `json:"tk"`
	UserId uint64 `json:"userId"`
}

const MessageIDLoginFailed = "login.failed"
const MessageIDOAuthLoginFailed = "oauth_login.failed"
const MessageIDDeviceNotFound = "user_devices.not_found"

func (s *authService) Login(c *gin.Context, db *gorm.DB, req LoginReq) (LoginResp, *i18n.Message, error) {
	var resp LoginResp
	var err error
	//check user
	var userTypeArr []string
	userTypeArr, exist := systemForLogin[req.System]
	if !exist {
		return resp, nil, errors.New("invalid system")
	}
	var user xmodel.User
	var builder *gorm.DB
	if req.RefreshToken != "" {
		cacheKey := fmt.Sprintf(cacheRefreshTokenUserInfo, req.RefreshToken)
		var cache RefreshTokenUserInfoCache
		exist, err = xredis.GetStruct(c, cacheKey, &cache)
		if err != nil {
			return resp, nil, err
		}
		if !exist {
			return resp, &canNotFoundRefreshMsg, nil
		}
		builder = db.Where("id = ?", cache.UserId)
	} else {
		pw := xtool.EncodeStringWithSalt(req.Password, xconfig.AppConf.PasswordSalt)
		builder = db.Where("BINARY username = ?", req.Username).
			Where("password = ?", pw)
	}
	if len(userTypeArr) > 0 {
		builder = builder.Where("user_type IN (?)", userTypeArr)
	}
	err = builder.
		Where("status = ?", "ENABLE").
		First(&user).Error
	if xgorm.IsSqlErr(err) {
		return resp, nil, err
	}
	if xgorm.IsNotFoundErr(err) {
		if req.RefreshToken == "" {
			msg := i18n.Message{
				ID:    MessageIDLoginFailed,
				Other: "Incorrect account or password.",
			}
			return resp, &msg, nil
		} else {
			msg := i18n.Message{
				ID:    MessageIDOAuthLoginFailed,
				Other: "Unable to sign in this account.",
			}
			return resp, &msg, nil
		}
	}

	token, claims, _ := xjwt.CreateTokenC(strconv.FormatUint(user.Id, 10), user.UserType, req.Dk, 60*12)
	var success bool
	success, err = xjwt.RefreshDeviceCache(c, c.ClientIP(), c.GetHeader("User-Agent"), claims)
	if err != nil {
		return resp, nil, err
	}
	if !success {
		msg := i18n.Message{
			ID:    MessageIDDeviceNotFound,
			Other: "Device not found.",
		}
		return resp, &msg, nil
	}
	if err = db.Model(&xmodel.UserDevice{}).
		Where("user_id = ?", user.Id).
		Where("device_key = ?", req.Dk).
		Update("last_active_system", req.System).Error; err != nil {
		return resp, nil, err
	}
	resp.TK = token
	resp.UserId = user.Id

	if req.RefreshToken != "" {
		// 成功登入後清空 refresh token 的 cache
		cacheKey := fmt.Sprintf(cacheRefreshTokenUserInfo, req.RefreshToken)
		_ = xredis.DeleteKey(c, cacheKey)
	}

	return resp, nil, nil
}

// endregion ---------- 登入 ----------

// region ---------- 忘記密碼 ----------
type AuthForgetPasswordReq struct {
	Email string `json:"email" binding:"required,email"` // 電郵
}

type AuthForgetPassword struct {
	Uuid string `json:"uuid"`
}

func (s *authService) ForgetUserPassword(c *gin.Context, req AuthForgetPasswordReq, m xmodel.User) error {
	var err error
	uid := uuid.NewV4().String()
	err = xredis.SetStruct(c, fmt.Sprintf(companyUserForgetPasswordKeyPrefix, uid, companyUserForgetPasswordUuid), AuthForgetPassword{Uuid: uid}, companyUserForgetPasswordCacheTime)
	if err != nil {
		return err
	}

	err = xredis.SetStruct(c, fmt.Sprintf(companyUserForgetPasswordKeyPrefix, uid, companyUserForgetPasswordUser), &m, companyUserForgetPasswordCacheTime)
	if err != nil {
		return err
	}
	err = s.sendForgetPasswordMail(c, uid, req.Email)
	if err != nil {
		return err
	}

	return nil
}

// endregion ---------- 忘記密碼 ----------

// region ---------- 發送忘記密碼電郵 ----------
func (s *authService) sendForgetPasswordMail(c *gin.Context, uuid string, email string) error {
	var err error
	var url string
	db := xgorm.DB.WithContext(c)
	url, err = CommonSettingService.GetSettingValueByCode(db, model.CommonSettingCodeForgetPasswordUrl)
	if err != nil {
		return err
	}
	if url == "" {
		return errors.New("forget password url not found")
	}
	// TODO：需要更換Email內容
	link := fmt.Sprintf("%s?uuid=%s", url, uuid)
	emailGreeting := i18n.Message{
		ID:    "forget.password.email.greeting",
		Other: "Hi",
	}
	emailSignature := i18n.Message{
		ID:    "forget.password.email.signature",
		Other: "Thank you very much!",
	}
	emailIntros := i18n.Message{
		ID:    "forget.password.email.intros",
		Other: "We Receive your forgot your password message.",
	}
	emailInstructions := i18n.Message{
		ID:    "forget.password.email.instructions",
		Other: "Please click the button below to reset your password.",
	}
	emailButtonText := i18n.Message{
		ID:    "forget.password.email.button.text",
		Other: "RESET ",
	}
	emailOutros := i18n.Message{
		ID:    "forget.password.email.outros",
		Other: "Please reset your password within 30 minutes, re-apply is needed after expiration.",
	}
	emailSubject := i18n.Message{
		ID:    "forget.password.email.subject",
		Other: "Reset password",
	}
	emailTroubleText := i18n.Message{
		ID:    "forget.password.email.trouble.text",
		Other: "If you have any problem to open the button '{ACTION}', please copy and paste the link below to your web browser.",
	}
	body := hermes.Body{
		Greeting:  xi18n.Localize(c.Request, &emailGreeting),
		Signature: xi18n.Localize(c.Request, &emailSignature),
		Name:      "",
		Intros: []string{
			xi18n.Localize(c.Request, &emailIntros),
		},
		Actions: []hermes.Action{
			{
				Instructions: xi18n.Localize(c.Request, &emailInstructions),
				Button: hermes.Button{
					Color: "#1FAF96",
					Text:  xi18n.Localize(c.Request, &emailButtonText),
					Link:  link,
				},
			},
		},
		Outros: []string{
			xi18n.Localize(c.Request, &emailOutros),
		},
	}
	content, err := xhermes.GenerateHTML(ForgetPasswordProduct(medicCrewLogoUrl, xi18n.Localize(c.Request, &emailTroubleText)), body)
	if err != nil {
		return err
	}

	err = xmail.SendMail(email, fmt.Sprintf("[%s] %s", xconfig.MailConf.Name, xi18n.Localize(c.Request, &emailSubject)), "text/html", content)
	if err != nil {
		return err
	}

	return nil
}

// endregion ---------- 發送忘記密碼電郵 ----------

// region ---------- 重置密碼 ----------
type AuthResetPasswordReq struct {
	Uuid     string `json:"uuid" binding:"required"`     // 重置密碼uuid
	Password string `json:"password" binding:"required"` // 新密碼
}

func (s *authService) ResetPassword(c *gin.Context, req AuthResetPasswordReq) (bool, i18n.Message, error) {
	var m xmodel.User
	var exist bool
	var err error
	db := xgorm.DB.WithContext(c)
	msg := i18n.Message{
		ID:    "get.user.login.forget.password.fail",
		Other: "The reset password link you opened is invalid. Please re-enter your email address below and send it.",
	}
	exist, err = xredis.GetStruct(c, fmt.Sprintf(companyUserForgetPasswordKeyPrefix, req.Uuid, companyUserForgetPasswordUser), &m)
	if !exist {
		return false, msg, nil
	}
	if err != nil {
		return false, msg, err
	}
	if err := db.Model(xmodel.User{}).Where("id = ?", m.Id).Updates(map[string]interface{}{
		"password": xtool.EncodeStringWithSalt(req.Password, xconfig.AppConf.PasswordSalt),
	}).Error; err != nil {
		return false, msg, err
	}

	keys, err := xredis.Keys(c, fmt.Sprintf(companyUserForgetPasswordKeyPrefix, req.Uuid, "*"))
	if err != nil {
		return false, msg, err
	}
	for _, k := range keys {
		if err := xredis.DeleteKey(c, k); err != nil {
			return false, msg, err
		}
	}
	return true, msg, nil
}

// endregion ---------- 重置密碼 ----------

// region ---------- 檢查重置密碼uuid是否有效 ----------
type AuthCheckResetPasswordUuidReq struct {
	Uuid string `json:"uuid" binding:"required"`
}

func (s *authService) CheckResetPasswordKeys(c *gin.Context, uuid string) ([]string, error) {
	keys, err := xredis.Keys(c, fmt.Sprintf(companyUserForgetPasswordKeyPrefix, uuid, "*"))
	if err != nil {
		return nil, err
	}
	return keys, nil
}

// endregion ---------- 檢查重置密碼uuid是否有效 ----------

type GoogleOAuthReq struct {
	Scene          string `json:"scene" binding:"required,oneof=LOGIN REGISTER"`                                  // 場景 LOGIN=登錄，REGISTER=註冊
	Purpose        string `json:"purpose" binding:"required_if=Scene REGISTER,omitempty,oneof=FIND_JOB POST_JOB"` // 目的 FIND_JOB=找工作，POST_JOB=發佈工作
	RecaptchaToken string `json:"recaptchaToken" binding:"required"`                                              // Recaptcha Token
}

type GoogleOAuthResp struct {
	URL string `json:"url"` // Google OAuth URL
}

func (s *authService) GenGoogleOAuthURL(nc xapp.NGinCtx, req GoogleOAuthReq) (GoogleOAuthResp, error) {
	url, err := GoogleOAuthService.GenerateAuthURL(nc, req.Scene, req.Purpose)
	if err != nil {
		return GoogleOAuthResp{}, err
	}
	return GoogleOAuthResp{
		URL: url,
	}, nil
}

type GoogleCallbackReq struct {
	State string `json:"state" binding:"required"`
	Code  string `json:"code" binding:"required"`
}

type GoogleCallbackResp struct {
	UserName     string `json:"userName"`
	RefreshToken string `json:"refreshToken"`
}

type RefreshTokenUserInfoCache struct {
	Username string `json:"username"`
	UserId   uint64 `json:"userId"`
}

func (s *authService) GoogleCallback(nc xapp.NGinCtx, db *gorm.DB, req GoogleCallbackReq) (GoogleCallbackResp, *i18n.Message, error) {
	canNotFoundMsg := i18n.Message{
		ID:    "alerter.google_oauth.can_not_found_state",
		Other: "Authorization timed out, please reauthorize.",
	}
	cache, googleInfo, err := GoogleOAuthService.GetUserProfile(nc, CallbackReq{
		State: req.State,
		Code:  req.Code,
	})
	if err != nil {
		log.WithContext(nc.C).WithField("state", req.State).WithField("code", req.Code).Errorf("Google OAuth callback error: %v", err)
		return GoogleCallbackResp{}, &canNotFoundMsg, nil
	}
	// 查詢 googleInfo 的 email 在系統是否已經存在
	var user xmodel.User
	if err = db.Where("BINARY username = ?", googleInfo.Email).First(&user).Error; xgorm.IsSqlErr(err) {
		return GoogleCallbackResp{}, nil, err
	}
	if user.Id > 0 {
		if user.Status == xmodel.UserStatusDisable {
			msg := i18n.Message{
				ID:    "user.status.disable",
				Other: "This account is currently unable to sign in.",
			}
			return GoogleCallbackResp{}, &msg, nil
		}
		if user.UserType != model.UserUserTypeFacilityUser && user.UserType != model.UserUserTypeProfessional {
			msg := i18n.Message{
				ID:    "user.type.not.support_oauth",
				Other: "This account is not supported to sign in with OAuth.",
			}
			return GoogleCallbackResp{}, &msg, nil
		}
	}

	if cache.Scene == "REGISTER" {
		if user.Id == 0 {
			// 註冊機構
			if cache.Purpose == "POST_JOB" {
				err = RegisterService.RegisterVerifyFacilityEmail(db, RegisterFacilityInfo{
					RegisterVerifyInfo: RegisterVerifyInfo{
						Email: googleInfo.Email,
					},
				})
				if err != nil {
					return GoogleCallbackResp{}, nil, err
				}
			} else if cache.Purpose == "FIND_JOB" {
				err = RegisterService.RegisterVerifyProfessionalEmail(db, RegisterProfessionalInfo{
					RegisterVerifyInfo: RegisterVerifyInfo{
						Email: googleInfo.Email,
					},
				})
				if err != nil {
					return GoogleCallbackResp{}, nil, err
				}
			} else {
				return GoogleCallbackResp{}, nil, errors.New("invalid purpose")
			}
		}
	}
	// 儲存 OAuthId,用於前端申請 device 或 登入
	refreshToken := strings.ToLower(uuid.NewV4().String() + "-" + uuid.NewV4().String())
	err = xredis.SetStruct(nc.C, fmt.Sprintf(cacheRefreshTokenUserInfo, refreshToken), RefreshTokenUserInfoCache{
		Username: user.Username,
		UserId:   user.Id,
	}, 30*time.Minute) // 30分鐘有效
	if err != nil {
		return GoogleCallbackResp{}, nil, err
	}
	return GoogleCallbackResp{
		UserName:     user.Username,
		RefreshToken: refreshToken,
	}, nil, nil
}
