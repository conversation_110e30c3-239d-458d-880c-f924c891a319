package routers

import (
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
	"github.com/shopspring/decimal"
)

func TestJobScheduleCreate(t *testing.T) {
	// 構建測試用例
	facilityId := uint64(3)
	test := xtest.Test{
		Url:              programPath + "/v1/facility/job-schedules/actions/create",
		UserId:           11,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "創建工作排程",
		Cases: []xtest.TestCase{
			{
				SubName:           "每日重複",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleCreateReq{
					FacilityId:         facilityId,
					Name:               "每日醫療護理",
					RepeatType:         model.JobScheduleRepeatDaily,
					BeginDate:          "2023-10-01",
					EndDate:            "2023-12-31",
					AdvanceDays:        7,
					DailyInterval:      1,
					WeeklyInterval:     1,
					WeekDays:           "",
					MonthlyInterval:    1,
					MonthlyType:        "DAY",
					MonthlyDayOfMonth:  1,
					MonthlyWeekIndex:   1,
					MonthlyWeekDay:     model.JobScheduleWeekdayMonday,
					Draft:              "N",
					PositionProfession: model.JobPositionProfessionEnrolledNurse,
					NumberOfPeople:     2,
					ServiceLocationId:  1,
					MinExperienceLevel: "RN_GRADE_1",
					Specialisation:     "PSN_ACUTE_CARE",
					SupervisionLevel:   "FULLY_SUPERVISED",
					BreakTimePayable:   "Y",
					ShiftAllocation:    model.JobShiftAllocationAutomatic,
					JobShiftItems: []services.JobScheduleShiftItem{
						{
							BeginTime:     "09:00",
							EndTime:       "17:00",
							Duration:      decimal.NewFromInt(8),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(8),
							ShiftPeriod:   "DAY",
							HourlyRate:    decimal.NewFromInt(25),
						},
					},
				},
			},
			{
				SubName:           "每週重複",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleCreateReq{
					FacilityId:         facilityId,
					Name:               "每週醫生值班",
					RepeatType:         model.JobScheduleRepeatWeekly,
					BeginDate:          "2023-10-01",
					EndDate:            "2023-12-31",
					AdvanceDays:        7,
					DailyInterval:      1,
					WeeklyInterval:     1,
					WeekDays:           "MONDAY,WEDNESDAY,FRIDAY",
					MonthlyInterval:    1,
					MonthlyType:        "DAY",
					MonthlyDayOfMonth:  1,
					MonthlyWeekIndex:   1,
					MonthlyWeekDay:     model.JobScheduleWeekdayMonday,
					Draft:              "N",
					PositionProfession: model.JobPositionProfessionMedicalPractitioner,
					NumberOfPeople:     1,
					ServiceLocationId:  1,
					MinExperienceLevel: "PGY1",
					PreferredGrade:     "INTERN",
					Specialisation:     "PSMP_MEDICINE",
					Language:           "CHINESE",
					SupervisionLevel:   "PARTIALLY_SUPERVISED",
					BreakTimePayable:   "N",
					Benefits:           "PARKING,MEALS",
					ShiftAllocation:    model.JobShiftAllocationManual,
					Remark:             "需要特殊技能",
					JobShiftItems: []services.JobScheduleShiftItem{
						{
							BeginTime:     "09:00",
							EndTime:       "17:00",
							Duration:      decimal.NewFromInt(8),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(8),
							ShiftPeriod:   "DAY",
							HourlyRate:    decimal.NewFromInt(25),
						},
					},
				},
			},
			{
				SubName:           "每月按日期重複",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleCreateReq{
					FacilityId:         facilityId,
					Name:               "每月中期護理",
					RepeatType:         model.JobScheduleRepeatMonthly,
					BeginDate:          "2023-10-01",
					EndDate:            "2024-09-30",
					AdvanceDays:        14,
					DailyInterval:      1,
					WeeklyInterval:     1,
					WeekDays:           "",
					MonthlyInterval:    1,
					MonthlyType:        model.JobScheduleMonthlyTypeDay,
					MonthlyDayOfMonth:  15,
					MonthlyWeekIndex:   1,
					MonthlyWeekDay:     model.JobScheduleWeekdayMonday,
					Draft:              "N",
					PositionProfession: model.JobPositionProfessionEnrolledNurse,
					NumberOfPeople:     3,
					ServiceLocationId:  2,
					MinExperienceLevel: "RN_GRADE_2",
					Specialisation:     "PSN_AGED_CARE",
					SupervisionLevel:   "NO_SUPERVISION",
					BreakTimePayable:   "Y",
					Benefits:           "TRANSPORT",
					ShiftAllocation:    model.JobShiftAllocationAutomatic,
					JobShiftItems: []services.JobScheduleShiftItem{
						{
							BeginTime:     "14:00",
							EndTime:       "22:00",
							Duration:      decimal.NewFromInt(8),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(8),
							ShiftPeriod:   "EVENING",
							HourlyRate:    decimal.NewFromInt(30),
						},
					},
				},
			},
			{
				SubName:           "每月按星期幾重複",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleCreateReq{
					FacilityId:         facilityId,
					Name:               "每月週二護理",
					RepeatType:         model.JobScheduleRepeatMonthly,
					BeginDate:          "2023-10-01",
					EndDate:            "2024-09-30",
					AdvanceDays:        10,
					DailyInterval:      1,
					WeeklyInterval:     1,
					WeekDays:           "1,3,5", // 週一、週三、週五
					MonthlyInterval:    2,
					MonthlyType:        model.JobScheduleMonthlyTypeWeekday,
					MonthlyDayOfMonth:  1,
					MonthlyWeekIndex:   2,
					MonthlyWeekDay:     model.JobScheduleWeekdayTuesday,
					Draft:              "N",
					PositionProfession: model.JobPositionProfessionPersonalCareWorker,
					NumberOfPeople:     2,
					ServiceLocationId:  3,
					MinExperienceLevel: "",
					Specialisation:     "PSPCW_AGED_CARE",
					SupervisionLevel:   "PARTIALLY_SUPERVISED",
					BreakTimePayable:   "Y",
					ShiftAllocation:    model.JobShiftAllocationManual,
					JobShiftItems: []services.JobScheduleShiftItem{
						{
							BeginTime:     "08:00",
							EndTime:       "12:00",
							Duration:      decimal.NewFromInt(4),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(4),
							ShiftPeriod:   "AM",
							HourlyRate:    decimal.NewFromInt(25),
						},
						{
							BeginTime:     "13:00",
							EndTime:       "17:00",
							Duration:      decimal.NewFromInt(4),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(4),
							ShiftPeriod:   "PM",
							HourlyRate:    decimal.NewFromInt(25),
						},
					},
				},
			},
			{
				SubName:           "暫存草稿",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleCreateReq{
					FacilityId:         facilityId,
					Name:               "護理排程草稿",
					RepeatType:         model.JobScheduleRepeatDaily,
					BeginDate:          "2023-10-01",
					EndDate:            "2023-12-31",
					AdvanceDays:        7,
					DailyInterval:      1,
					WeeklyInterval:     1,
					WeekDays:           "1",
					MonthlyInterval:    1,
					MonthlyType:        "DAY",
					MonthlyDayOfMonth:  1,
					MonthlyWeekIndex:   1,
					MonthlyWeekDay:     model.JobScheduleWeekdayMonday,
					Draft:              "Y",
					PositionProfession: model.JobPositionProfessionEnrolledNurse,
					NumberOfPeople:     2,
					ServiceLocationId:  1,
					MinExperienceLevel: "",
					Specialisation:     "PSN_ORTHOPEDIC",
					SupervisionLevel:   "FULLY_SUPERVISED",
					BreakTimePayable:   "Y",
					ShiftAllocation:    model.JobShiftAllocationAutomatic,
					JobShiftItems: []services.JobScheduleShiftItem{
						{
							BeginTime:     "09:00",
							EndTime:       "17:00",
							Duration:      decimal.NewFromInt(8),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(8),
							ShiftPeriod:   "DAY",
							HourlyRate:    decimal.NewFromInt(25),
						},
					},
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestJobScheduleEdit(t *testing.T) {
	// 構建測試用例
	facilityId := uint64(3)
	jobScheduleId := uint64(20) // 假設已存在的排程ID

	test := xtest.Test{
		Url:              programPath + "/v1/facility/job-schedules/actions/edit",
		UserId:           11,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "編輯工作排程",
		Cases: []xtest.TestCase{
			{
				SubName:           "修改每日重複排程",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleEditReq{
					JobScheduleId:      jobScheduleId,
					FacilityId:         facilityId,
					Name:               "更新後的每日護理",
					RepeatType:         model.JobScheduleRepeatDaily,
					BeginDate:          "2023-11-01",
					EndDate:            "2024-02-28",
					AdvanceDays:        10,
					DailyInterval:      2,
					WeeklyInterval:     1,
					WeekDays:           "1",
					MonthlyInterval:    1,
					MonthlyType:        model.JobScheduleMonthlyTypeDay,
					MonthlyDayOfMonth:  1,
					MonthlyWeekIndex:   1,
					MonthlyWeekDay:     model.JobScheduleWeekdayMonday,
					Draft:              "N",
					PositionProfession: model.JobPositionProfessionEnrolledNurse,
					NumberOfPeople:     3,
					ServiceLocationId:  2,
					MinExperienceLevel: "RN_GRADE_1",
					Specialisation:     "PSN_ACUTE_CARE",
					SupervisionLevel:   "PARTIALLY_SUPERVISED",
					BreakTimePayable:   "Y",
					Benefits:           "PARKING,TRANSPORT",
					ShiftAllocation:    model.JobShiftAllocationAutomatic,
					JobShiftItems: []services.JobScheduleShiftItem{
						{
							BeginTime:     "10:00",
							EndTime:       "18:00",
							Duration:      decimal.NewFromInt(8),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(8),
							ShiftPeriod:   "DAY",
							HourlyRate:    decimal.NewFromInt(28),
						},
					},
				},
			},
			{
				SubName:           "修改為每週重複排程",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleEditReq{
					JobScheduleId:      jobScheduleId,
					FacilityId:         facilityId,
					Name:               "更新為每週護理",
					RepeatType:         model.JobScheduleRepeatWeekly,
					BeginDate:          "2023-12-01",
					EndDate:            "2024-06-30",
					AdvanceDays:        14,
					DailyInterval:      1,
					WeeklyInterval:     2,
					WeekDays:           "TUESDAY,THURSDAY",
					MonthlyInterval:    1,
					MonthlyType:        model.JobScheduleMonthlyTypeDay,
					MonthlyDayOfMonth:  1,
					MonthlyWeekIndex:   1,
					MonthlyWeekDay:     model.JobScheduleWeekdayMonday,
					Draft:              "N",
					PositionProfession: model.JobPositionProfessionMedicalPractitioner,
					NumberOfPeople:     2,
					ServiceLocationId:  1,
					MinExperienceLevel: "PGY2",
					PreferredGrade:     "RESIDENT",
					Specialisation:     "PSMP_SURGERY",
					Language:           "ENGLISH,MANDARIN",
					SupervisionLevel:   "NO_SUPERVISION",
					BreakTimePayable:   "N",
					Benefits:           "ACCOMMODATION,PARKING",
					ShiftAllocation:    model.JobShiftAllocationManual,
					Remark:             "需要外科手術經驗",
					JobShiftItems: []services.JobScheduleShiftItem{
						{
							BeginTime:     "08:00",
							EndTime:       "16:00",
							Duration:      decimal.NewFromInt(8),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(8),
							ShiftPeriod:   "AM",
							HourlyRate:    decimal.NewFromInt(45),
						},
						{
							BeginTime:     "16:00",
							EndTime:       "24:00",
							Duration:      decimal.NewFromInt(8),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(8),
							ShiftPeriod:   "EVENING",
							HourlyRate:    decimal.NewFromInt(55),
						},
					},
				},
			},
			{
				SubName:           "修改為每月按日期重複",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleEditReq{
					JobScheduleId:      jobScheduleId,
					FacilityId:         facilityId,
					Name:               "更新為每月護理",
					RepeatType:         model.JobScheduleRepeatMonthly,
					BeginDate:          "2024-01-01",
					EndDate:            "2024-12-31",
					AdvanceDays:        21,
					DailyInterval:      1,
					WeeklyInterval:     1,
					WeekDays:           "1",
					MonthlyInterval:    1,
					MonthlyType:        model.JobScheduleMonthlyTypeDay,
					MonthlyDayOfMonth:  20,
					MonthlyWeekIndex:   1,
					MonthlyWeekDay:     model.JobScheduleWeekdayMonday,
					Draft:              "N",
					PositionProfession: model.JobPositionProfessionRegisteredNurse,
					NumberOfPeople:     4,
					ServiceLocationId:  3,
					MinExperienceLevel: "RN_GRADE_3",
					PreferredGrade:     "RN_GRADE_4",
					Specialisation:     "PSN_MENTAL_HEALTH",
					SupervisionLevel:   "PARTIALLY_SUPERVISED",
					BreakTimePayable:   "Y",
					Benefits:           "MEALS,TRANSPORT",
					ShiftAllocation:    model.JobShiftAllocationAutomatic,
					JobShiftItems: []services.JobScheduleShiftItem{
						{
							BeginTime:   "07:00",
							EndTime:     "15:00",
							Duration:    decimal.NewFromInt(8),
							PayHours:    decimal.NewFromInt(8),
							ShiftPeriod: "DAY",
							HourlyRate:  decimal.NewFromInt(35),
						},
					},
				},
			},
			{
				SubName:           "修改為每月按星期幾重複",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleEditReq{
					JobScheduleId:      jobScheduleId,
					FacilityId:         facilityId,
					Name:               "更新為每月週三護理",
					RepeatType:         model.JobScheduleRepeatMonthly,
					BeginDate:          "2024-01-01",
					EndDate:            "2025-01-01",
					AdvanceDays:        15,
					DailyInterval:      1,
					WeeklyInterval:     1,
					WeekDays:           "",
					MonthlyInterval:    3,
					MonthlyType:        model.JobScheduleMonthlyTypeWeekday,
					MonthlyDayOfMonth:  1,
					MonthlyWeekIndex:   3,
					MonthlyWeekDay:     model.JobScheduleWeekdayWednesday,
					Draft:              "N",
					PositionProfession: model.JobPositionProfessionPersonalCareWorker,
					NumberOfPeople:     5,
					ServiceLocationId:  4,
					MinExperienceLevel: "PCW_GRADE_1",
					Specialisation:     "PSPCW_DISABILITY",
					SupervisionLevel:   "FULLY_SUPERVISED",
					BreakTimePayable:   "Y",
					Benefits:           "TRAINING",
					ShiftAllocation:    model.JobShiftAllocationManual,
					JobShiftItems: []services.JobScheduleShiftItem{
						{
							BeginTime:     "06:00",
							EndTime:       "14:00",
							Duration:      decimal.NewFromInt(8),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(8),
							ShiftPeriod:   "AM",
							HourlyRate:    decimal.NewFromInt(24),
						},
						{
							BeginTime:     "14:00",
							EndTime:       "22:00",
							Duration:      decimal.NewFromInt(8),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(8),
							ShiftPeriod:   "AFTERNOON",
							HourlyRate:    decimal.NewFromInt(26),
						},
					},
				},
			},
			{
				SubName:           "修改為暫存草稿",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleEditReq{
					JobScheduleId:      jobScheduleId,
					FacilityId:         facilityId,
					Name:               "暫存的護理排程",
					RepeatType:         model.JobScheduleRepeatDaily,
					BeginDate:          "2023-12-01",
					EndDate:            "2024-03-31",
					DailyInterval:      1,
					WeeklyInterval:     1,
					WeekDays:           "1",
					MonthlyInterval:    1,
					MonthlyType:        model.JobScheduleMonthlyTypeDay,
					MonthlyDayOfMonth:  1,
					MonthlyWeekIndex:   1,
					MonthlyWeekDay:     model.JobScheduleWeekdayMonday,
					Draft:              "Y",
					PositionProfession: model.JobPositionProfessionEnrolledNurse,
					NumberOfPeople:     1,
					ServiceLocationId:  5,
					MinExperienceLevel: "EN_GRADE_1",
					Specialisation:     "PSN_PALLIATIVE",
					SupervisionLevel:   "NO_SUPERVISION",
					BreakTimePayable:   "Y",
					ShiftAllocation:    model.JobShiftAllocationAutomatic,
					Remark:             "此排程暫時不需",
					JobShiftItems: []services.JobScheduleShiftItem{
						{
							BeginTime:     "09:00",
							EndTime:       "17:00",
							Duration:      decimal.NewFromInt(8),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(8),
							ShiftPeriod:   "DAY",
							HourlyRate:    decimal.NewFromInt(30),
						},
					},
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestJobScheduleList(t *testing.T) {
	// 構建測試用例
	user := getTestUser(16)

	test := xtest.Test{
		Url:              programPath + "/v1/facility/job-schedules",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢工作排程列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "查詢所有排程",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleListReq{
					FacilityId: user.FacilityId,
					Draft:      "N",
				},
				SortingSet: xresp.SortingSet{
					SortingKey:  "createTime",
					SortingType: "a",
				},
			},
			{
				SubName:           "按職位查詢-醫療從業員",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleListReq{
					FacilityId:         user.FacilityId,
					Draft:              "N",
					PositionProfession: model.JobPositionProfessionMedicalPractitioner,
				},
			},
			{
				SubName:           "按職位查詢-登記護士",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleListReq{
					FacilityId:         user.FacilityId,
					Draft:              "N",
					PositionProfession: model.JobPositionProfessionEnrolledNurse,
				},
			},
			{
				SubName:           "按職位查詢-註冊護士",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleListReq{
					FacilityId:         user.FacilityId,
					Draft:              "N",
					PositionProfession: model.JobPositionProfessionRegisteredNurse,
				},
			},
			{
				SubName:           "按職位查詢-個人護理員",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleListReq{
					FacilityId:         user.FacilityId,
					Draft:              "N",
					PositionProfession: model.JobPositionProfessionPersonalCareWorker,
				},
			},
			{
				SubName:           "按狀態查詢-還未開始",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleListReq{
					FacilityId: user.FacilityId,
					Draft:      "N",
					Status:     services.JobScheduleStatusNotStarted,
				},
			},
			{
				SubName:           "按狀態查詢-已終止",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleListReq{
					FacilityId: user.FacilityId,
					Draft:      "N",
					Status:     services.JobScheduleStatusTerminated,
				},
			},
			{
				SubName:           "按狀態查詢-進行中",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleListReq{
					FacilityId: user.FacilityId,
					Draft:      "N",
					Status:     services.JobScheduleStatusInProgress,
				},
			},
			{
				SubName:           "按狀態查詢-已完成",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleListReq{
					FacilityId: user.FacilityId,
					Draft:      "N",
					Status:     services.JobScheduleStatusCompleted,
				},
			},
			{
				SubName:           "按職位和狀態組合查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleListReq{
					FacilityId:         user.FacilityId,
					Draft:              "N",
					PositionProfession: model.JobPositionProfessionEnrolledNurse,
					Status:             services.JobScheduleStatusInProgress,
				},
			},
			{
				SubName:           "查詢草稿排程",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleListReq{
					FacilityId: user.FacilityId,
					Draft:      "Y",
				},
			},
			{
				SubName:           "按職位查詢草稿排程",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleListReq{
					FacilityId:         user.FacilityId,
					Draft:              "Y",
					PositionProfession: model.JobPositionProfessionEnrolledNurse,
				},
			},
			{
				SubName:           "無效的機構ID",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobScheduleListReq{
					FacilityId: 0,
					Draft:      "N",
				},
			},
			{
				SubName:           "無效的狀態值",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobScheduleListReq{
					FacilityId: user.FacilityId,
					Draft:      "N",
					Status:     "INVALID_STATUS",
				},
			},
			{
				SubName:           "無效的草稿參數",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobScheduleListReq{
					FacilityId: user.FacilityId,
					Draft:      "X", // 無效值，應該只能是 Y 或 N
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestJobScheduleInquire(t *testing.T) {
	// 構建測試用例
	jobScheduleId := uint64(17) // 假設已存在的排程ID
	facilityId := uint64(3)     // 假設已存在的排程ID

	test := xtest.Test{
		Url:              programPath + "/v1/facility/job-schedules/actions/inquire",
		UserId:           11,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢工作排程詳情",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleInquireReq{
					FacilityId:    facilityId,
					JobScheduleId: jobScheduleId,
				},
			},
			{
				SubName:           "無效的排程ID",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobScheduleInquireReq{
					FacilityId:    facilityId,
					JobScheduleId: 0,
				},
			},
			{
				SubName:           "不存在的排程ID",
				ExpectErrHttpCode: xresp.StatusBadRequest,
				Params: services.JobScheduleInquireReq{
					FacilityId:    facilityId,
					JobScheduleId: 999999, // 假設不存在的ID
				},
			},
			{
				SubName:           "無權限查詢的排程",
				ExpectErrHttpCode: xresp.StatusForbidden,
				Params: services.JobScheduleInquireReq{
					FacilityId:    4, // 假設用戶無權限查詢的機構ID
					JobScheduleId: jobScheduleId,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestJobScheduleDelete(t *testing.T) {
	// 構建測試用例
	facilityId := uint64(3)
	jobScheduleId := uint64(2) // 假設已存在的排程ID

	test := xtest.Test{
		Url:              programPath + "/v1/facility/job-schedules/actions/delete",
		UserId:           11,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "刪除工作排程",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常刪除",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleDeleteReq{
					FacilityId:    facilityId,
					JobScheduleId: jobScheduleId,
				},
			},
			{
				SubName:           "無效的排程ID",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobScheduleDeleteReq{
					FacilityId:    facilityId,
					JobScheduleId: 0,
				},
			},
			{
				SubName:           "不存在的排程ID",
				ExpectErrHttpCode: xresp.StatusBadRequest,
				Params: services.JobScheduleDeleteReq{
					FacilityId:    facilityId,
					JobScheduleId: 999999, // 假設不存在的ID
				},
			},
			{
				SubName:           "無權限刪除的排程",
				ExpectErrHttpCode: xresp.StatusForbidden,
				Params: services.JobScheduleDeleteReq{
					FacilityId:    4, // 假設用戶無權限操作的機構ID
					JobScheduleId: jobScheduleId,
				},
			},
			{
				SubName:           "已發佈工作的排程",
				ExpectErrHttpCode: xresp.StatusBadRequest,
				Params: services.JobScheduleDeleteReq{
					FacilityId:    facilityId,
					JobScheduleId: 3, // 假設已有發佈工作的排程ID
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestJobScheduleUpdateStatus(t *testing.T) {
	// 構建測試用例
	jobScheduleId := uint64(23) // 假設已存在的排程ID

	test := xtest.Test{
		Url:              programPath + "/v1/facility/job-schedules/actions/update-status",
		UserId:           16,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "更新工作排程狀態",
		Cases: []xtest.TestCase{
			//{
			//	SubName:           "啟用排程",
			//	ExpectErrRespCode: xresp.StatusOK,
			//	Params: services.JobScheduleUpdateStatusReq{
			//		JobScheduleId: jobScheduleId,
			//		Status:        model.JobScheduleStatusEnable,
			//	},
			//},
			{
				SubName:           "停用排程",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobScheduleUpdateStatusReq{
					FacilityId:    7,
					JobScheduleId: jobScheduleId,
					Status:        model.JobScheduleStatusDisable,
				},
			},
			{
				SubName:           "無效的排程ID",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobScheduleUpdateStatusReq{
					JobScheduleId: 0,
					Status:        model.JobScheduleStatusEnable,
				},
			},
			{
				SubName:           "不存在的排程ID",
				ExpectErrHttpCode: xresp.StatusBadRequest,
				Params: services.JobScheduleUpdateStatusReq{
					JobScheduleId: 999999, // 假設不存在的ID
					Status:        model.JobScheduleStatusEnable,
				},
			},
			{
				SubName:           "無效的狀態值",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobScheduleUpdateStatusReq{
					JobScheduleId: jobScheduleId,
					Status:        "INVALID_STATUS", // 無效的狀態值
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestJobScheduleCalcPotentialJobCount(t *testing.T) {
	// 構建測試用例
	facilityId := uint64(7)
	jobScheduleId := uint64(50)        // 假設已存在的排程ID
	serviceLocationIdleId := uint64(3) // 假設已存在的排程ID

	test := xtest.Test{
		Url:              programPath + "/v1/facility/job-schedules/actions/calc-potential-count",
		UserId:           16,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "計算潛在工作數量",
		Cases: []xtest.TestCase{
			{
				SubName:           "每日排程潛在工作數量計算",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobSchedulePotentialCountReq{
					FacilityId:        facilityId,
					JobScheduleId:     0, // 新建模式，ID為0
					ServiceLocationId: serviceLocationIdleId,
					AdvanceDays:       1,
					RepeatType:        model.JobScheduleRepeatDaily,
					BeginDate:         "2025-07-30",
					EndDate:           "2025-08-30",
					DailyInterval:     1,
					JobShiftItems: []services.JobScheduleShiftItem{
						{
							BeginTime:     "08:00",
							EndTime:       "12:00",
							Duration:      decimal.NewFromInt(4),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(4),
							ShiftPeriod:   "AM",
							HourlyRate:    decimal.NewFromInt(1),
						},
						{
							BeginTime:     "12:00",
							EndTime:       "20:00",
							Duration:      decimal.NewFromInt(8),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(8),
							ShiftPeriod:   "PM",
							HourlyRate:    decimal.NewFromFloat(1.5),
						},
					},
				},
			},
			{
				SubName:           "編輯模式下的排程潛在工作數量計算",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobSchedulePotentialCountReq{
					FacilityId:        facilityId,
					JobScheduleId:     jobScheduleId, // 編輯模式，使用已存在的ID
					ServiceLocationId: serviceLocationIdleId,
					RepeatType:        model.JobScheduleRepeatMonthly,
					AdvanceDays:       1,
					BeginDate:         "2025-07-30",
					EndDate:           "2025-08-30",
					MonthlyInterval:   2,
					MonthlyType:       model.JobScheduleMonthlyTypeWeekday,
					MonthlyDayOfMonth: 1,
					MonthlyWeekIndex:  1,
					MonthlyWeekDay:    1,
					JobShiftItems: []services.JobScheduleShiftItem{
						{
							BeginTime:     "09:00",
							EndTime:       "17:00",
							Duration:      decimal.NewFromInt(8),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(8),
							ShiftPeriod:   "DAY",
							HourlyRate:    decimal.NewFromInt(1),
						},
					},
				},
			},
			{
				SubName:           "無效日期範圍",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobSchedulePotentialCountReq{
					FacilityId:        facilityId,
					JobScheduleId:     0,
					ServiceLocationId: serviceLocationIdleId,
					RepeatType:        model.JobScheduleRepeatDaily,
					AdvanceDays:       1,
					BeginDate:         "2025-05-23", // 開始日期晚於結束日期
					EndDate:           "2025-05-17",
					DailyInterval:     1,
					JobShiftItems: []services.JobScheduleShiftItem{
						{
							BeginTime:     "08:00",
							EndTime:       "12:00",
							Duration:      decimal.NewFromInt(4),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(4),
							ShiftPeriod:   "AM",
							HourlyRate:    decimal.NewFromInt(1),
						},
					},
				},
			},
			{
				SubName:           "週重複排程",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobSchedulePotentialCountReq{
					FacilityId:        facilityId,
					JobScheduleId:     0,
					ServiceLocationId: serviceLocationIdleId,
					RepeatType:        model.JobScheduleRepeatWeekly,
					AdvanceDays:       10,
					BeginDate:         "2025-05-17",
					EndDate:           "2025-06-17",
					WeeklyInterval:    1,
					WeekDays:          "1,3,5", // 週一、週三、週五
					JobShiftItems: []services.JobScheduleShiftItem{
						{
							BeginTime:     "09:00",
							EndTime:       "17:00",
							Duration:      decimal.NewFromInt(8),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(8),
							ShiftPeriod:   "DAY",
							HourlyRate:    decimal.NewFromInt(1),
						},
					},
				},
			},
			{
				SubName:           "月重複排程-按日期",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobSchedulePotentialCountReq{
					FacilityId:        facilityId,
					JobScheduleId:     0,
					ServiceLocationId: serviceLocationIdleId,
					RepeatType:        model.JobScheduleRepeatMonthly,
					AdvanceDays:       60,
					BeginDate:         "2025-05-17",
					EndDate:           "2025-12-17",
					MonthlyInterval:   1,
					MonthlyType:       model.JobScheduleMonthlyTypeDay,
					MonthlyDayOfMonth: 15, // 每月15號
					JobShiftItems: []services.JobScheduleShiftItem{
						{
							BeginTime:     "09:00",
							EndTime:       "17:00",
							Duration:      decimal.NewFromInt(8),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(8),
							ShiftPeriod:   "DAY",
							HourlyRate:    decimal.NewFromInt(1),
						},
					},
				},
			},
			{
				SubName:           "月重複排程-按星期",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobSchedulePotentialCountReq{
					FacilityId:        facilityId,
					JobScheduleId:     0,
					ServiceLocationId: serviceLocationIdleId,
					RepeatType:        model.JobScheduleRepeatMonthly,
					AdvanceDays:       60,
					BeginDate:         "2025-05-17",
					EndDate:           "2025-12-17",
					MonthlyInterval:   1,
					MonthlyType:       model.JobScheduleMonthlyTypeWeekday,
					MonthlyWeekIndex:  2, // 第二週
					MonthlyWeekDay:    3, // 星期三
					JobShiftItems: []services.JobScheduleShiftItem{
						{
							BeginTime:     "09:00",
							EndTime:       "17:00",
							Duration:      decimal.NewFromInt(8),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(8),
							ShiftPeriod:   "DAY",
							HourlyRate:    decimal.NewFromInt(1),
						},
					},
				},
			},
			{
				SubName:           "參數不完整-缺少必要的日重複設定",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobSchedulePotentialCountReq{
					FacilityId:        facilityId,
					JobScheduleId:     0,
					RepeatType:        model.JobScheduleRepeatDaily,
					BeginDate:         "2025-05-17",
					EndDate:           "2025-05-23",
					ServiceLocationId: serviceLocationIdleId,
					// 缺少 DailyInterval
					JobShiftItems: []services.JobScheduleShiftItem{
						{
							BeginTime:     "08:00",
							EndTime:       "12:00",
							Duration:      decimal.NewFromInt(4),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(4),
							ShiftPeriod:   "AM",
							HourlyRate:    decimal.NewFromInt(1),
						},
					},
				},
			},
			{
				SubName:           "參數不完整-缺少班次時間",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobSchedulePotentialCountReq{
					FacilityId:        facilityId,
					JobScheduleId:     0,
					ServiceLocationId: serviceLocationIdleId,
					RepeatType:        model.JobScheduleRepeatDaily,
					BeginDate:         "2025-05-17",
					EndDate:           "2025-05-23",
					DailyInterval:     1,
					JobShiftItems:     []services.JobScheduleShiftItem{}, // 空班次時間
				},
			},
			{
				SubName:           "無效的排程ID",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobSchedulePotentialCountReq{
					FacilityId:        facilityId,
					JobScheduleId:     999999, // 不存在的ID
					ServiceLocationId: serviceLocationIdleId,
					RepeatType:        model.JobScheduleRepeatDaily,
					BeginDate:         "2025-05-17",
					EndDate:           "2025-05-23",
					DailyInterval:     1,
					JobShiftItems: []services.JobScheduleShiftItem{
						{
							BeginTime:     "08:00",
							EndTime:       "12:00",
							Duration:      decimal.NewFromInt(4),
							BreakDuration: decimal.NewFromInt(0),
							PayHours:      decimal.NewFromInt(4),
							ShiftPeriod:   "AM",
							HourlyRate:    decimal.NewFromInt(1),
						},
					},
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
