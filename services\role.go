package services

import (
	"context"
	"errors"
	"fmt"

	"github.com/Norray/xrocket/xredis"
	"github.com/Norray/xrocket/xtool"
	"github.com/jinzhu/copier"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xmodel"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gorm.io/gorm"
)

var RoleService = new(roleService)

type roleService struct{}

// region ---------------------------------------------------- Checker ----------------------------------------------------

// 檢查角色是否存在
func (s *roleService) CheckIdExist(db *gorm.DB, role *xmodel.Role, roleId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.role.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var err error
	if err = db.Where("id = ?", roleId).First(role).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, i18n.Message{}, nil
	}
}

// 檢查角色是否存在
func (s *roleService) CheckRoleExists(db *gorm.DB, userType string, roleId uint64, facilityId ...uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.role.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var count int64
	builder := db.Table("role AS r").Where("r.id = ?", roleId).Where("r.user_type = ?", userType)
	if len(facilityId) > 0 {
		builder = builder.Joins("JOIN facility_role AS fr ON fr.role_id = r.id").
			Where("fr.facility_id = ?", facilityId[0])
	}
	if err := builder.Count(&count).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if count == 0 {
		return false, msg, nil
	}
	return true, i18n.Message{}, nil
}

func (s *roleService) CheckRoleCanUpdateByFacility(db *gorm.DB, roleId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.role.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var role xmodel.Role
	if err := db.Where("id = ?", roleId).First(&role).Error; err != nil {
		return false, msg, err
	}
	if role.Sub != "" {
		return false, msg, nil
	} else {
		return true, i18n.Message{}, nil
	}
}

func (s *roleService) CheckRoleCanUpdateByUserType(userType string, role xmodel.Role) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.role.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	if userType == model.UserUserTypeSuperAdmin {
		if role.UserType == model.UserUserTypeSystemAdmin || role.Sub != "" {
			return true, msg, nil
		}
	} else if userType == model.UserUserTypeSystemAdmin {
		if role.UserType == model.UserUserTypeSystemAdmin && role.Sub == "" {
			// 系統管理員只可以改系統管理員的非默認角色
			return true, msg, nil
		}
	}
	return false, msg, nil
}

// 檢查角色名稱是否重複
func (s *roleService) CheckRoleNameAlreadyExists(db *gorm.DB, userType string, name string, facilityId uint64, roleId ...uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.role.name.already_exists",
		Other: "Some thing went wrong, please try again later.",
	}
	var count int64
	builder := db.Table("role AS r").Where("r.name = ?", name).Where("r.user_type = ?", userType)
	if facilityId != 0 {
		builder = builder.Joins("JOIN facility_role AS fr ON fr.role_id = r.id").
			Where("fr.facility_id = ?", facilityId)
	}
	if len(roleId) > 0 {
		builder = builder.Where("r.id != ?", roleId[0])
	}
	if err := builder.Count(&count).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if count == 0 {
		return true, i18n.Message{}, nil
	}
	return false, msg, nil
}

// 檢查角色是否可以刪除
func (s *roleService) CheckRoleCanDelete(db *gorm.DB, roleId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.role.id.cannot_delete",
		Other: "This role cannot be deleted.",
	}
	var err error

	var role xmodel.Role
	if err = db.Where("id = ?", roleId).First(&role).Error; err != nil {
		return false, msg, err
	}
	if role.Sub != "" {
		return false, msg, nil
	}

	var userRole xmodel.UserRole
	if err = db.Where("role_id = ?", roleId).First(&userRole).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return true, i18n.Message{}, nil
	} else {
		return false, msg, nil
	}
}

// endregion ---------------------------------------------------- Checker ----------------------------------------------------

// region ---------------------------------------------------- 角色列表 ----------------------------------------------------

type RoleListReq struct {
	UserType   string `form:"-"`                        // 本次請求的用戶的類型 SUPER_ADMIN=超級管理員, SYSTEM_ADMIN=系統管理員, FACILITY_USER=機構用戶
	FacilityId uint64 `form:"-"`                        // (內部) 機構ID (當 Types 為 FACILITY_USER 時需要填寫)
	Name       string `form:"name" binding:"omitempty"` // 角色名稱
}

type RoleListResp struct {
	RoleId   uint64 `json:"roleId"`
	UserType string `json:"userType"` // 角色類型
	Name     string `json:"name"`
	Sub      string `json:"sub"`
}

func (s *roleService) List(db *gorm.DB, req RoleListReq, pageSet *xresp.PageSet) ([]RoleListResp, error) {
	var resp []RoleListResp

	builder := db.Table("role AS r").
		Select([]string{
			"r.id as role_id",
			"r.user_type",
			"r.name",
			"r.sub",
		})

	if req.UserType == model.UserUserTypeFacilityUser || req.FacilityId != 0 {
		builder = builder.Joins("JOIN facility_role AS fr ON fr.role_id = r.id").
			Where("fr.facility_id = ?", req.FacilityId).Where("r.user_type = ?", model.UserUserTypeFacilityUser)
	}
	if req.UserType == model.UserUserTypeSystemAdmin {
		builder = builder.Where("r.user_type = ?", model.UserUserTypeSystemAdmin)
	}
	if req.UserType == model.UserUserTypeSuperAdmin {
		builder = builder.Where("r.user_type = ? OR r.sub <> ''", model.UserUserTypeSystemAdmin)
	}

	if req.Name != "" {
		builder = builder.Where("r.name like ?", "%"+req.Name+"%")
	}

	if err := builder.
		Order("r.name ASC").
		Scopes(xresp.Paginate(pageSet)).
		Find(&resp).Error; err != nil {
		return resp, err
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 角色列表 ----------------------------------------------------

// region ---------------------------------------------------- 角色搜索 ----------------------------------------------------

type RoleSearchReq struct {
	UserType   string `form:"-"`                        // 本次請求的用戶的類型 SUPER_ADMIN=超級管理員, SYSTEM_ADMIN=系統管理員, FACILITY_USER=機構用戶
	FacilityId uint64 `form:"-"`                        // (內部) 機構ID (當 Types 為 FACILITY_USER 時需要填寫)
	Name       string `form:"name" binding:"omitempty"` // 用戶名稱
	SelectedId uint64 `form:"selectedId"`               // 選中ID
	Limit      int    `form:"limit"`                    // 每頁條目數
}

type RoleSearchResp struct {
	RoleId   uint64 `json:"roleId"`
	UserType string `json:"userType"`
	Name     string `json:"name"`
	Sub      string `json:"sub"`
}

func (s *roleService) Search(db *gorm.DB, req RoleSearchReq) ([]RoleSearchResp, error) {
	var resp []RoleSearchResp

	builder := db.Table("role AS r").
		Select([]string{
			"r.id as role_id",
			"r.user_type",
			"r.name",
			"r.sub",
		})

	if req.UserType == model.UserUserTypeFacilityUser || req.FacilityId != 0 {
		builder = builder.Joins("JOIN facility_role AS fr ON fr.role_id = r.id").
			Where("fr.facility_id = ?", req.FacilityId).Where("r.user_type = ?", model.UserUserTypeFacilityUser)
	}
	if req.UserType == model.UserUserTypeSystemAdmin {
		builder = builder.Where("r.user_type = ?", model.UserUserTypeSystemAdmin)
	}
	if req.UserType == model.UserUserTypeSuperAdmin {
		builder = builder.Where("r.user_type = ? OR r.sub <> ''", model.UserUserTypeSystemAdmin)
	}

	if req.Name != "" {
		builder = builder.Where("r.name like ?", "%"+req.Name+"%")
	}

	if req.SelectedId != 0 {
		builder = builder.Order(fmt.Sprintf("IF(r.id = %d,0,1)", req.SelectedId))
	}

	if req.Limit > 0 {
		builder = builder.Limit(req.Limit)
	}

	if err := builder.
		Order("r.name ASC").
		Find(&resp).Error; err != nil {
		return resp, err
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 用戶列表 ----------------------------------------------------

// region ---------------------------------------------------- 用戶查詢 ----------------------------------------------------

type RoleInquireReq struct {
	RoleId     uint64 `form:"roleId" binding:"required"`
	UserType   string `form:"-"` // 本次請求的用戶的類型 SUPER_ADMIN=超級管理員, SYSTEM_ADMIN=系統管理員, FACILITY_USER=機構用戶
	FacilityId uint64 `form:"-"` // (內部) 機構ID (當 Types 為 FACILITY_USER 時需要填寫)
}

type RoleInquireResp struct {
	RoleId   uint64 `json:"roleId"` // 角色Id
	UserType string `json:"userType"`
	Name     string `json:"name"` // 角色名稱
	Sub      string `json:"sub"`
}

func (s *roleService) Inquire(db *gorm.DB, req RoleInquireReq) (RoleInquireResp, error) {
	var err error
	var resp RoleInquireResp
	var m xmodel.Role

	builder := db.Where("id = ?", req.RoleId)
	switch req.UserType {
	case model.UserUserTypeSystemAdmin:
		builder = builder.Where("user_type = ?", model.UserUserTypeSystemAdmin)
	case model.UserUserTypeSuperAdmin:
		builder = builder.Where("user_type = ? OR sub <> ''", model.UserUserTypeSystemAdmin)
	case model.UserUserTypeFacilityUser:
		builder = builder.Where("user_type = ?", model.UserUserTypeFacilityUser)
	default:
		return RoleInquireResp{}, errors.New("invalid user type")
	}

	if err = builder.
		First(&m).Error; err != nil {
		return resp, err
	}
	if req.FacilityId > 0 {
		var relation model.FacilityRole
		if err = db.Where("role_id = ?", m.Id).Where("facility_id = ?", req.FacilityId).First(&relation).Error; err != nil {
			return resp, err
		}
	}
	_ = copier.Copy(&resp, m)
	resp.RoleId = m.Id
	return resp, nil
}

// endregion ---------------------------------------------------- 用戶查詢 ----------------------------------------------------

// region ---------------------------------------------------- 角色創建 ----------------------------------------------------

type RoleCreateReq struct {
	UserType   string `json:"-"`                       // 本次請求的用戶的類型 SUPER_ADMIN=超級管理員, SYSTEM_ADMIN=系統管理員, FACILITY_USER=機構用戶
	FacilityId uint64 `json:"-"`                       // (內部) 機構ID (當 Types 為 FACILITY_USER 時需要填寫)
	Name       string `json:"name" binding:"required"` // 名稱
}

type RoleCreateResp struct {
	RoleId uint64 `json:"roleId"`
}

func (s *roleService) Create(db *gorm.DB, req RoleCreateReq) (RoleCreateResp, error) {
	var err error
	var roleCreateResp RoleCreateResp
	var role xmodel.Role
	role.Name = req.Name
	role.RoleCode = xtool.GenerateRandomString(6, "U", false)

	switch req.UserType {
	case model.UserUserTypeFacilityUser:
		role.RoleCode = fmt.Sprintf("%d_%s", req.FacilityId, role.RoleCode)
		role.UserType = model.UserUserTypeFacilityUser
	case model.UserUserTypeSystemAdmin, model.UserUserTypeSuperAdmin:
		role.UserType = model.UserUserTypeSystemAdmin
		role.RoleCode = fmt.Sprintf("SYSTEM_%s", role.RoleCode)
	default:
		return roleCreateResp, errors.New("invalid user type")
	}

	if err = db.Create(&role).Error; err != nil {
		return roleCreateResp, err
	}

	// 機構需保存關係表
	if req.UserType == model.UserUserTypeFacilityUser {
		facilityRole := model.FacilityRole{
			FacilityId: req.FacilityId,
			RoleId:     role.Id,
		}
		if err = db.Create(&facilityRole).Error; err != nil {
			return roleCreateResp, err
		}
	}
	roleCreateResp.RoleId = role.Id
	return roleCreateResp, nil
}

// endregion ---------------------------------------------------- 用戶創建 ----------------------------------------------------

// region ---------------------------------------------------- 角色修改 ----------------------------------------------------

type RoleEditReq struct {
	UserType   string `form:"-"`                         // 本次請求的用戶的類型 SUPER_ADMIN=超級管理員, SYSTEM_ADMIN=系統管理員, FACILITY_USER=機構用戶
	FacilityId uint64 `form:"-"`                         // (內部) 機構ID (當 Types 為 FACILITY_USER 時需要填寫)
	RoleId     uint64 `json:"roleId" binding:"required"` // 角色ID
	Name       string `json:"name" binding:"required"`   // 名稱
}

func (s *roleService) Edit(db *gorm.DB, req RoleEditReq) error {
	var err error

	builder := db.Model(&xmodel.Role{}).Where("id = ?", req.RoleId)
	switch req.UserType {
	case model.UserUserTypeSystemAdmin:
		builder = builder.Where("user_type = ?", model.UserUserTypeSystemAdmin)
	case model.UserUserTypeSuperAdmin:
		builder = builder.Where("user_type = ? OR sub <> ''", model.UserUserTypeSystemAdmin)
	case model.UserUserTypeFacilityUser:
		builder = builder.Where("user_type = ?", model.UserUserTypeFacilityUser)
	default:
		return errors.New("invalid user type")
	}

	if err = builder.Updates(map[string]interface{}{
		"name": req.Name,
	}).Error; err != nil {
		return err
	}
	return nil
}

// endregion ---------------------------------------------------- 角色修改 ----------------------------------------------------

// region ---------------------------------------------------- 角色刪除 ----------------------------------------------------

type RoleDeleteReq struct {
	UserType   string `form:"-"`                         // 本次請求的用戶的類型 SUPER_ADMIN=超級管理員, SYSTEM_ADMIN=系統管理員, FACILITY_USER=機構用戶
	FacilityId uint64 `form:"-"`                         // (內部) 機構ID (當 Types 為 FACILITY_USER 時需要填寫)
	RoleId     uint64 `json:"roleId" binding:"required"` // 角色ID
}

func (s *roleService) Delete(db *gorm.DB, req RoleDeleteReq) error {
	var err error

	builder := db.Model(&xmodel.Role{}).Where("id = ?", req.RoleId)
	switch req.UserType {
	case model.UserUserTypeSystemAdmin:
		builder = builder.Where("user_type = ?", model.UserUserTypeSystemAdmin)
	case model.UserUserTypeSuperAdmin:
		builder = builder.Where("user_type = ? OR sub <> ''", model.UserUserTypeSystemAdmin)
	case model.UserUserTypeFacilityUser:
		builder = builder.Where("user_type = ?", model.UserUserTypeFacilityUser)
	default:
		return errors.New("invalid user type")
	}
	// 刪除角色
	if err = builder.
		Delete(&xmodel.Role{}).Error; err != nil {
		return err
	}

	// 刪除角色權限
	if err = db.Where("role_id = ?", req.RoleId).Delete(&xmodel.RoleAction{}).Error; err != nil {
		return err
	}

	// 刪除機構角色
	if req.UserType == model.UserUserTypeFacilityUser {
		if err = db.
			Where("role_id = ?", req.RoleId).
			Where("facility_id = ?", req.FacilityId).
			Delete(&model.FacilityRole{}).Error; err != nil {
			return err
		}
	}

	return nil
}

// endregion ---------------------------------------------------- 角色刪除 ----------------------------------------------------

// region ---------------------------------------------------- 角色緩存 ----------------------------------------------------

func (s *roleService) ClearUserRoleCache(ctx context.Context, roleId uint64) error {
	var userIds []uint64
	db := xgorm.DB.WithContext(ctx)
	if err := db.Model(&xmodel.UserRole{}).Where("role_id = ?", roleId).Pluck("user_id", &userIds).Error; err != nil {
		return err
	}
	for _, userId := range userIds {
		key := fmt.Sprintf("cache:user_role:%d", userId)
		_ = xredis.DeleteKey(ctx, key)
	}
	return nil
}
