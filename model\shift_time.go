package model

import (
	"github.com/Norray/xrocket/xmodel"
	"github.com/shopspring/decimal"
)

// 機構的班次時間管理
type ShiftTime struct {
	Id         uint64          `json:"id" gorm:"primary_key"`
	FacilityId uint64          `json:"facilityId" gorm:"index:facility_idx;not null"` // 機構ID
	Name       string          `json:"name" gorm:"type:varchar(255);not null"`        // 班次名稱
	BeginTime  string          `json:"beginTime" gorm:"type:varchar(5);not null"`     // 開始時間 mm:ss
	NextDay1   string          `json:"nextDay1" gorm:"type:varchar(1);not null"`      // 結束時間1是否是次日
	EndTime1   string          `json:"endTime1" gorm:"type:varchar(5);not null"`      // 結束時間1 mm:ss
	NextDay2   string          `json:"nextDay2" gorm:"type:varchar(1);not null"`      // 結束時間2是否是次日
	EndTime2   string          `json:"endTime2" gorm:"type:varchar(5);not null"`      // 結束時間2 mm:ss
	HourlyRate decimal.Decimal `json:"hourlyRate" gorm:"type:decimal(10,2);not null"` // 時薪
	xmodel.Model
}

func (ShiftTime) TableName() string {
	return "shift_time"
}

func (ShiftTime) SwaggerDescription() string {
	return "機構的班次時間管理"
}
