package services

import (
	"time"

	"github.com/Norray/xrocket/xtool"

	"github.com/Norray/medic-crew/model"
	"github.com/nicksnyder/go-i18n/v2/i18n"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type jobShiftTimeService struct{}

var JobShiftTimeService = new(jobShiftTimeService)

type JobSearchForProfessionalShiftTimeResp struct {
	JobId           uint64            `json:"-"`
	JobShiftId      uint64            `json:"-"`
	BeginTime       time.Time         `json:"beginTime"`           // 開始時間
	EndTime         time.Time         `json:"endTime"`             // 結束時間
	Duration        decimal.Decimal   `json:"duration"`            // 時長（小時）
	BreakDuration   decimal.Decimal   `json:"breakDuration"`       // 休息時長（小時）
	PayHours        decimal.Decimal   `json:"payHours"`            // 支付時長（小時）
	ShiftPeriod     string            `json:"shiftPeriod"`         // 班次時間段
	HourlyRate      decimal.Decimal   `json:"hourlyRate"`          // 時薪
	AllowanceAmount decimal.Decimal   `json:"allowanceAmount"`     // 津貼總金額
	Allowances      []JobAllowanceReq `json:"allowances" gorm:"-"` // 津貼設定
}

func (s *jobShiftTimeService) SetJobShiftTimeList(db *gorm.DB, jobIds []uint64) (map[uint64][]JobSearchForProfessionalShiftTimeResp, error) {
	shiftTimesMap := make(map[uint64][]JobSearchForProfessionalShiftTimeResp)
	if len(jobIds) == 0 {
		return shiftTimesMap, nil
	}

	var err error
	var shiftTimes []JobSearchForProfessionalShiftTimeResp
	if err = db.Table("job_shift").
		Select([]string{
			"job_id",
			"id as job_shift_id",
			"begin_time",
			"end_time",
			"duration",
			"break_duration",
			"pay_hours",
			"shift_period",
			"hourly_rate",
			"allowance_amount",
		}).
		Where("job_id IN (?)", jobIds).Find(&shiftTimes).Error; err != nil {
		return shiftTimesMap, err
	}

	// 加載津貼設定
	shiftAllowanceMap, err := JobService.GetJobShiftAllowances(db, jobIds)
	if err != nil {
		return shiftTimesMap, err
	}

	for _, shiftTime := range shiftTimes {
		if allowance, ok := shiftAllowanceMap[shiftTime.JobShiftId]; ok {
			shiftTime.Allowances = allowance
		} else {
			shiftTime.Allowances = make([]JobAllowanceReq, 0)
		}
		shiftTimesMap[shiftTime.JobId] = append(shiftTimesMap[shiftTime.JobId], shiftTime)
	}

	return shiftTimesMap, nil
}

func (s *jobShiftTimeService) CheckJobShiftTimeCanSelect(db *gorm.DB, userId uint64, jobId uint64, jobShiftId []uint64, exceptDocumentId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.job_shift_time.id.can_not_select",
		Other: "Some thing went wrong, please try again later.",
	}
	var err error

	subBuilder := db.Table("document_item di").
		Select("di.job_shift_id, COUNT(d.id) AS count").
		Joins("JOIN document d ON di.document_id = d.id AND d.progress <> ? AND d.category = ? AND d.user_id = ?", model.DocumentProgressCancel, model.DocumentCategoryConfirmation, userId).
		Group("di.job_shift_id")

	if exceptDocumentId > 0 {
		subBuilder = subBuilder.Where("d.id <> ?", exceptDocumentId)
	}

	var jobShifts []model.JobShift
	builder := db.Model(&model.JobShift{}).
		Table("job_shift as js").
		Joins("JOIN job_application AS ja ON js.job_id = ja.job_id AND ja.user_id = ? AND ja.accept = ? AND ja.status = ?", userId, model.JobApplicationAcceptY, model.JobApplicationStatusAccept).
		Joins("JOIN job_shift_cancellation AS jsc ON jsc.job_shift_id = ja.id AND jsc.job_application_id = ja.id").
		Joins("LEFT JOIN (?) AS shift_count ON js.id = shift_count.job_shift_id", subBuilder).
		Select([]string{
			"js.*",
		}).
		Where("js.job_id = ?", jobId).
		Where("js.id IN (?)", jobShiftId).
		Where("jsc.id IS NULL").
		Where("IFNULL(shift_count.count, 0) = 0")

	if err = builder.Group("js.id").Find(&jobShifts).Error; err != nil {
		return false, msg, err
	}

	if len(jobShifts) != len(jobShiftId) {
		return false, msg, nil
	}

	return true, msg, nil
}

func (s *jobShiftTimeService) GetNextValidJobShiftsByJobId(db *gorm.DB, jobId uint64, targetTime time.Time) ([]model.JobShift, error) {
	targetTimeStr := targetTime.Format(xtool.DateTimeSecA1)
	var nextJobShifts []model.JobShift
	builder := db.
		Table("job_shift js").
		Joins("LEFT JOIN job_shift_cancellation jsc ON jsc.job_shift_id = js.id").
		Where("js.job_id = ?", jobId).
		Where("jsc.id IS NULL").
		Where("js.end_time >=", targetTimeStr)

	if err := builder.Group("js.end_time ASC").Find(&nextJobShifts).Error; err != nil {
		return nextJobShifts, err
	}

	return nextJobShifts, nil
}
