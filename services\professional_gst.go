package services

import (
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xmodel/xtype"
	"gorm.io/gorm"
)

var ProfessionalGstService = new(professionalGstService)

type professionalGstService struct{}

// 查詢專業人士GST記錄請求
type ProfessionalGstQueryReq struct {
	ProfessionalId uint64     `json:"professionalId" binding:"required"` // 專業人士ID
	QueryDate      xtype.Date `json:"queryDate" binding:"required"`      // 查詢日期
}

// 查詢專業人士GST記錄響應
type ProfessionalGstQueryResp struct {
	// Id                uint64         `json:"id"`                // GST記錄ID
	// UserId            uint64         `json:"userId"`            // 用戶ID
	ProfessionalId    uint64         `json:"professionalId"`    // 專業人士ID
	EffectiveFrom     xtype.Date     `json:"effectiveFrom"`     // 生效時間
	EffectiveTo       xtype.NullDate `json:"effectiveTo"`       // 失效時間
	RecordUpdatedDate xtype.Date     `json:"recordUpdatedDate"` // ABN更新日期
	UpdateTime        time.Time      `json:"updateTime"`        // 更新時間(UTC)
}

// 查詢專業人士GST記錄
func (s *professionalGstService) GetProfessionalGst(db *gorm.DB, req ProfessionalGstQueryReq) (*ProfessionalGstQueryResp, error) {
	var gstRecords []model.ProfessionalGST

	// 查詢該專業人士的所有GST記錄，按ID排序（保持插入順序，即從新到舊）
	if err := db.Where("professional_id = ?", req.ProfessionalId).
		Order("id ASC").
		Find(&gstRecords).Error; err != nil {
		return nil, err
	}

	// 如果沒有記錄，返回空
	if len(gstRecords) == 0 {
		return nil, nil
	}

	// 1. 先判斷之前的記錄有沒有完整時間段(EffectiveTo有日期)
	if record := s.findCompletedRecord(gstRecords, req.QueryDate); record != nil {
		return s.convertToResp(*record), nil
	}

	// 2. 判斷EffectiveFrom ~ EffectiveTo（未到期）有沒有包含此日期
	if record := s.findActiveRecord(gstRecords, req.QueryDate); record != nil {
		// 判斷UpdateTime是不是今天
		if s.isToday(record.UpdateTime) {
			// 是今天，直接返回這條記錄
			return s.convertToResp(*record), nil
		} else {
			// 不是今天，需要更新ABN數據
			return s.updateAndReturnLatest(db, *record, req)
		}
	}

	// 3. 都沒有返回空
	return nil, nil
}

// 查找完整時間段的記錄
func (s *professionalGstService) findCompletedRecord(gstRecords []model.ProfessionalGST, queryDate xtype.Date) *model.ProfessionalGST {
	for _, record := range gstRecords {
		if record.EffectiveTo.Valid {
			// 檢查查詢日期是否在此記錄的時間範圍內
			if s.isDateInRange(queryDate, record.EffectiveFrom, record.EffectiveTo) {
				return &record
			}
		}
	}
	return nil
}

// 查找未到期的記錄
func (s *professionalGstService) findActiveRecord(gstRecords []model.ProfessionalGST, queryDate xtype.Date) *model.ProfessionalGST {
	for _, record := range gstRecords {
		if !record.EffectiveTo.Valid {
			queryDateTime, err := queryDate.Time()
			if err != nil {
				return nil
			}
			effectiveFromTime, err := record.EffectiveFrom.Time()
			if err != nil {
				return nil
			}

			// 這是未到期的記錄，檢查查詢日期是否在生效日期之後
			if !queryDateTime.Before(effectiveFromTime) {
				return &record
			}
		}
	}
	return nil
}

// 檢查日期是否在指定範圍內
func (s *professionalGstService) isDateInRange(queryDate xtype.Date, effectiveFrom xtype.Date, effectiveTo xtype.NullDate) bool {
	queryTime, _ := queryDate.Time()
	effectiveFromTime, _ := effectiveFrom.Time()
	// 檢查是否在生效日期之後
	if queryTime.Before(effectiveFromTime) {
		return false
	}

	// 檢查是否在失效日期之前
	if effectiveTo.Valid {
		effectiveToTime, _ := effectiveTo.Date.Time()
		if !queryTime.Before(effectiveToTime) {
			return false
		}
	}

	return true
}

// 檢查時間是否是今天
func (s *professionalGstService) isToday(t time.Time) bool {
	now := time.Now().UTC()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)
	tomorrow := today.Add(24 * time.Hour)

	return t.After(today) && t.Before(tomorrow)
}

// 轉換模型為響應結構
func (s *professionalGstService) convertToResp(record model.ProfessionalGST) *ProfessionalGstQueryResp {
	return &ProfessionalGstQueryResp{
		//Id:                record.Id,
		//UserId:            record.UserId,
		ProfessionalId:    record.ProfessionalId,
		EffectiveFrom:     record.EffectiveFrom,
		EffectiveTo:       record.EffectiveTo,
		RecordUpdatedDate: record.RecordUpdatedDate,
		UpdateTime:        record.UpdateTime,
	}
}

// 更新ABN數據並返回最新記錄
func (s *professionalGstService) updateAndReturnLatest(db *gorm.DB, oldRecord model.ProfessionalGST, req ProfessionalGstQueryReq) (*ProfessionalGstQueryResp, error) {
	// 需要調用abnService.QueryAndUpdateProfessionalAbnData更新ABN數據
	// 但是我們需要ABN號碼和認證GUID，這些信息需要從Professional表中獲取

	var professional model.Professional
	if err := db.Where("id = ?", req.ProfessionalId).First(&professional).Error; err != nil {
		return nil, err
	}

	// 構建更新請求
	updateReq := QueryAndUpdateProfessionalDataAbnReq{
		UserId:            oldRecord.UserId,
		ProfessionalId:    req.ProfessionalId,
		AbnNumber:         professional.AbnNumber,
		IncludeHistorical: true,
	}

	// 調用ABN服務更新數據
	_, err := AbnService.QueryAndUpdateProfessionalAbnData(db, updateReq)
	if err != nil {
		return nil, err
	}

	// 重新查詢該專業人士的GST記錄，避免循環調用
	var gstRecords []model.ProfessionalGST
	if err := db.Where("professional_id = ?", req.ProfessionalId).
		Order("id ASC").
		Find(&gstRecords).Error; err != nil {
		return nil, err
	}

	// 如果沒有記錄，返回空
	if len(gstRecords) == 0 {
		return nil, nil
	}

	// 1. 先查找完整時間段的記錄
	if record := s.findCompletedRecord(gstRecords, req.QueryDate); record != nil {
		return s.convertToResp(*record), nil
	}

	// 2. 查找未到期的記錄（更新後直接返回，不再檢查UpdateTime）
	if record := s.findActiveRecord(gstRecords, req.QueryDate); record != nil {
		return s.convertToResp(*record), nil
	}

	// 3. 都沒有返回空
	return nil, nil
}
