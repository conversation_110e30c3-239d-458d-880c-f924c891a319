package routers

import (
	"time"

	"github.com/Norray/medic-crew/middleware"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/xrocket/xapp/xvalidator"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xmiddleware"
	"github.com/Norray/xrocket/xsentry"
	sentrygin "github.com/getsentry/sentry-go/gin"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

const programPath = "/medic-crew/api"

func InitRouter() *gin.Engine {
	r := gin.New()

	//增加自定義驗證器
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		_ = v.RegisterValidation(xvalidator.TagSplitIn, xvalidator.SplitIn)
		_ = v.RegisterValidation(xvalidator.TagStringGte, xvalidator.StringGte)
		_ = v.RegisterValidation(xvalidator.TagStringGteField, xvalidator.StringGteField)
		_ = v.RegisterValidation(xvalidator.TagIdsWithComma, xvalidator.IdsWithComma)
		_ = v.RegisterValidation(xvalidator.TagTimezone, xvalidator.TimezoneValidator)
		_ = v.RegisterValidation(xvalidator.TagRequiredIfAnd, xvalidator.RequiredIfAnd)
	}

	config := cors.DefaultConfig()
	// need to set origins on production
	if len(xconfig.ServerConf.AllowOrigins) > 0 {
		config.AllowOrigins = xconfig.ServerConf.AllowOrigins
	} else {
		config.AllowAllOrigins = true
	}
	r.Use(cors.New(config), xmiddleware.Trace(), xmiddleware.Logger())
	//swagger
	if xconfig.ServerConf.RunMode == "debug" {
		r.GET(programPath+"/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	}
	if xsentry.SentryInitialized() {
		r.Use(sentrygin.New(sentrygin.Options{
			Repanic:         true, // 繼續把 panic 往外拋，方便日誌或其他中間件處理
			WaitForDelivery: true, // 等待事件送出
			Timeout:         time.Second * 2,
		}))
	}

	g := r.Group(programPath)
	userDevicePublicRouter(g)         // 用戶設備申請
	authPublicRouter(g)               // 用戶認證
	registerRouter(g)                 // 註冊
	selectionPublicRouter(g)          // 選項 - 公開
	uploadFileByQrcodePublicRouter(g) // 掃碼上傳文件

	verify := r.Group(programPath, xmiddleware.JWT(resource.JwtExpiredMsg, resource.JwtFailedMsg, map[string]bool{}), xmiddleware.Casbin(resource.ForbiddenMsg, middleware.GetUserRoleCache))

	wsVerify := r.Group(programPath, xmiddleware.SecWebSocketProtocolFillInToXToken(resource.JwtFailedMsg), xmiddleware.JWT(resource.JwtExpiredMsg, resource.JwtFailedMsg, map[string]bool{}))
	casbinRouter(verify)                // Casbin
	userDeviceRouter(verify)            // 用戶設備
	myRouter(verify)                    // 用戶
	selectionAppRouter(verify)          // 選項 - App
	uploadFileByQrcodeAppRouter(verify) // 掃碼上傳文件
	locationAppRouter(verify)           // 郵遞區號
	serviceFacilityAppRouter(verify)    // 服務機構
	documentRouter(verify)              // 文件 PDF
	platformRouter(verify)              // 平台

	// 專業人士
	professionalProfileRouter(verify)          // 專業人士
	professionalFileRouter(verify)             // 專業人士文件
	professionalJobAvailabilityRouter(verify)  // 專業人士工作可用性
	selectionProfessionalRouter(verify)        // 選項 - 專業人士
	faqProfessionalRouter(verify)              // 常見問題 - 專業人士
	jobProfessionalRouter(verify)              // 工作職位搜索 - 專業人士
	jobApplicationProfessionalRouter(verify)   // 我的工作 - 專業人士
	facilityProfessionalRouter(verify)         // 機構 - 專業人士
	professionalDocumentFileRouter(verify)     // 賬單文件 - 專業人士
	professionalConfirmationNoteRouter(verify) // 賬單 - 專業人士
	wsMessageProfessionalRouter(wsVerify)      // WebSocket 消息 - 專業人士
	professionalInvoiceRouter(verify)          // 發票 - 專業人士

	// 機構用戶
	userFacilityRouter(verify)                   // 用戶 - 機構
	roleFacilityRouter(verify)                   // 角色 - 機構
	facilityProfileFacilityRouter(verify)        // 機構資料 - 機構
	facilityFileFacilityRouter(verify)           // 機構文件 - 機構
	facilitySpecialisationFacilityRouter(verify) // 機構專業細分 - 機構
	selectionFacilityRouter(verify)              // 選項 - 機構
	facilityFacilityAgreementRouter(verify)      // 協議 - 機構
	faqFacilityRouter(verify)                    // 常見問題 - 機構
	serviceLocationFacilityRouter(verify)        // 服務地點管理 - 機構
	benefitFacilityRouter(verify)                // 福利管理 - 機構
	allowanceFacilityRouter(verify)              // 津貼管理 - 機構
	shiftTimeFacilityRouter(verify)              // 班次時間 - 機構
	hourlyRateFacilityRouter(verify)             // 時薪管理 - 機構
	jobFacilityRouter(verify)                    // 工作職位管理 - 機構
	jobScheduleFacilityRouter(verify)            // 工作排程管理 - 機構
	jobApplicationFacilityRouter(verify)         // 工作職位申請 - 機構
	professionalFileFacilityRouter(verify)       // 專業人士文件 - 機構
	facilityBlacklistRouter(verify)              // 黑名單 - 機構
	facilityRouter(verify)                       // 機構
	googleFacilityRouter(verify)                 // Google - 機構
	wsMessageFacilityRouter(wsVerify)            // WebSocket 消息 - 機構
	facilityConfirmationNoteRouter(verify)       // 賬單 - 機構
	facilityDocumentFileRouter(verify)           // 賬單文件 - 機構
	facilityInvoiceRouter(verify)                // 發票 - 機構

	// 管理端
	userSystemRouter(verify)                // 用戶 - 管理端
	roleSystemRouter(verify)                // 角色 - 管理端
	selectionSystemRouter(verify)           // 選項 - 管理端
	facilitySystemRouter(verify)            // 機構 - 管理端
	facilityProfileSystemRouter(verify)     // 機構資料 - 管理端
	facilityFileSystemRouter(verify)        // 機構文件 - 管理端
	professionalProfileSystemRouter(verify) // 專業人士資料 - 管理端
	professionalFileSystemRouter(verify)    // 專業人士文件 - 管理端
	agreementSystemRouter(verify)           // 協議 - 管理端
	faqSystemRouter(verify)                 // 常見問題 - 管理端
	faqFileSystemRouter(verify)             // 常見問題文件 - 管理端
	userDeviceSystemRouter(verify)          // 用戶設備 - 管理端
	userPermissionSystemRouter(verify)      // 用戶權限 - 管理端
	locationSystemRouter(verify)            // 地區 - 管理端
	commissionSystemRouter(verify)          // 佣金 - 管理端
	jobSystemRouter(verify)                 // 工作職位 - 管理端
	jobApplicationSystemRouter(verify)      // 工作職位申請 - 管理端
	systemConfirmationNoteRouter(verify)    // 確認通知單 - 管理端
	systemDocumentFileRouter(verify)        // 單據文件 - 管理端
	systemInvoiceRouter(verify)             // 發票 - 管理端

	// 開發者功能
	menuActionSystemRouter(verify)        // 菜單權限管理
	systemFacilityAgreementRouter(verify) // 機構協議

	return r
}
