package services

import (
	"fmt"
	"regexp"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gorm.io/gorm"
)

const (
	FaqAnswerImageRegex = `(?m)\[image!([0-9a-zA-Z-]{36})\]` // 圖片Uuid匹配正則，使用子匹配提取UUID
)

var FaqService = new(faqService)

type faqService struct{}

func (s *faqService) CheckIdExist(db *gorm.DB, m *model.Faq, id uint64, category ...string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.faq.id.does_not_exist",
		Other: "No such record, please try after reloading.",
	}
	var err error
	builder := db.Model(&model.Faq{}).Where("id = ?", id)
	if len(category) > 0 {
		builder = builder.Where("category = ?", category[0])
	}
	err = builder.First(&m).Error
	if xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	}
	return true, msg, nil
}

// 檢查答案的圖片是否存在
func (s *faqService) CheckAnswerFileExist(db *gorm.DB, answer string) (bool, i18n.Message, error) {
	imgUuids := s.GetAnswerFileUuids(answer)
	if len(imgUuids) == 0 {
		return true, i18n.Message{}, nil
	}
	return FaqFileService.CheckUuidsExist(db, imgUuids)
}

// 提取答案中的圖片UUID
func (s *faqService) GetAnswerFileUuids(answer string) []string {
	re := regexp.MustCompile(FaqAnswerImageRegex)
	imgUuids := make([]string, 0)
	matches := re.FindAllStringSubmatch(answer, -1)
	for _, match := range matches {
		if len(match) > 1 {
			imgUuids = append(imgUuids, match[1])
		}
	}
	imgUuids = StringArrayDistinct(imgUuids)
	return imgUuids
}

type FaqCreateReq struct {
	Category   string `json:"category" binding:"required,oneof=PROFESSIONAL FACILITY"` // 分類 專業人士=PROFESSIONAL 機構=FACILITY
	Question   string `json:"question" binding:"required,max=1024"`                    // 問題
	Answer     string `json:"answer" binding:"required"`                               // 答案
	AnswerText string `json:"answerText" binding:"required,max=255"`                   // 答案純文本
	Status     string `json:"status" binding:"required,oneof=ENABLE DISABLE"`          // 狀態 啟用=ENABLE 禁用=DISABLE
}

type FaqCreateResp struct {
	FaqId uint64 `json:"faqId"` // 常見問題ID
}

func (s *faqService) Create(db *gorm.DB, req FaqCreateReq) (FaqCreateResp, error) {
	var resp FaqCreateResp
	var err error
	var maxSeq int32
	if err = db.Model(&model.Faq{}).
		Where("category = ?", req.Category).
		Select("COALESCE(MAX(seq), 0)").Scan(&maxSeq).Error; err != nil {
		return resp, err
	}
	var m model.Faq
	_ = copier.Copy(&m, req)
	m.Seq = maxSeq + 1
	if err = db.Create(&m).Error; err != nil {
		return resp, err
	}
	resp.FaqId = m.Id

	fileUuids := s.GetAnswerFileUuids(m.Answer)
	if len(fileUuids) > 0 {
		if err = FaqFileService.BindFaq(db, m.Id, fileUuids); err != nil {
			return resp, err
		}
	}

	return resp, nil
}

type FaqListReq struct {
	SearchContent string `form:"searchContent" binding:"omitempty"`                        // 搜索關鍵字
	Category      string `form:"category" binding:"omitempty,oneof=PROFESSIONAL FACILITY"` // 分類 專業人士=PROFESSIONAL 機構=FACILITY
	Status        string `form:"status" binding:"omitempty,oneof=ENABLE DISABLE"`          // 狀態 啟用=ENABLE 禁用=DISABLE
}

type FaqListResp struct {
	FaqId    uint64 `json:"faqId"`
	Category string `json:"category"` // 分類 專業人士=PROFESSIONAL 機構=FACILITY
	Question string `json:"question"` // 問題
	Answer   string `json:"answer"`   // 答案
	Seq      int32  `json:"seq"`      // 排序
	Status   string `json:"status"`   // 狀態
}

func (s *faqService) List(db *gorm.DB, req FaqListReq, pageSet *xresp.PageSet) ([]FaqListResp, error) {
	var err error
	var resp []FaqListResp
	builder := db.Table("faq AS f").Select([]string{
		"f.id AS faq_id",
		"f.category",
		"f.question",
		"f.answer_text AS answer",
		"f.seq",
		"f.status",
	})
	if req.Category != "" {
		builder = builder.Where("f.category = ?", req.Category)
	}
	if req.SearchContent != "" {
		builder = builder.Where("f.question LIKE ? OR f.answer LIKE ?", "%"+req.SearchContent+"%", "%"+req.SearchContent+"%")
	}
	if req.Status != "" {
		builder = builder.Where("f.status = ?", req.Status)
	}
	if err = builder.
		Order("f.seq").
		Order("f.id").
		Scopes(xresp.Paginate(pageSet)).
		Find(&resp).Error; err != nil {
		return resp, err
	}
	for i := range resp {
		resp[i].Answer = s.FormatAnswer(db, resp[i].Answer, false)
	}
	return resp, nil
}

type FaqSearchReq struct {
	Category      string `form:"category" binding:"omitempty,oneof=PROFESSIONAL FACILITY"` // 分類 專業人士=PROFESSIONAL 機構=FACILITY
	SearchContent string `form:"searchContent" binding:"omitempty"`                        // 搜索關鍵字
	Status        string `form:"status" binding:"omitempty,oneof=ENABLE DISABLE"`          // 狀態 啟用=ENABLE 禁用=DISABLE
	SelectedId    uint64 `form:"selectedId"`                                               // 選中ID
	Limit         int    `form:"limit"`                                                    // 每頁條目數
}

type FaqSearchReqByUser struct {
	Category      string `form:"-"`                                               // 分類 專業人士=PROFESSIONAL 機構=FACILITY
	SearchContent string `form:"searchContent" binding:"omitempty"`               // 搜索關鍵字
	Status        string `form:"status" binding:"omitempty,oneof=ENABLE DISABLE"` // 狀態 啟用=ENABLE 禁用=DISABLE
	SelectedId    uint64 `form:"selectedId"`                                      // 選中ID
	Limit         int    `form:"limit"`                                           // 每頁條目數
}

type FaqSearchResp struct {
	FaqId    uint64 `json:"faqId"`    // 常見問題ID
	Category string `json:"category"` // 分類 專業人士=PROFESSIONAL 機構=FACILITY
	Question string `json:"question"` // 問題
	Answer   string `json:"answer"`   // 答案純文本
	Seq      int32  `json:"seq"`      // 排序
	Status   string `json:"status"`   // 狀態 啟用=ENABLE 禁用=DISABLE
}

type FaqSearchRespByUser struct {
	FaqId    uint64 `json:"faqId"`    // 常見問題ID
	Question string `json:"question"` // 問題
	Answer   string `json:"answer"`   // 答案
	Seq      int32  `json:"seq"`      // 排序
}

func (s *faqService) Search(db *gorm.DB, req FaqSearchReq, userType string) (interface{}, error) {
	var err error
	var resp []FaqSearchResp
	builder := db.Table("faq AS f").Select([]string{
		"f.id AS faq_id",
		"f.category",
		"f.question",
		"f.answer_text AS answer",
		"f.seq",
		"f.status",
	})
	if req.Category != "" {
		builder = builder.Where("f.category = ?", req.Category)
	}
	if req.SearchContent != "" {
		builder = builder.Where("f.question LIKE ? OR f.answer LIKE ?", "%"+req.SearchContent+"%", "%"+req.SearchContent+"%")
	}
	if req.Status != "" {
		builder = builder.Where("f.status = ?", req.Status)
	}
	if req.SelectedId != 0 {
		builder = builder.Order(fmt.Sprintf("IF(f.id = %d,0,1)", req.SelectedId))
	}
	if req.Limit > 0 {
		builder = builder.Limit(req.Limit)
	}
	if err = builder.Order("f.id").Find(&resp).Error; err != nil {
		return resp, err
	}
	for i := range resp {
		resp[i].Answer = s.FormatAnswer(db, resp[i].Answer, false)
	}
	switch userType {
	case model.UserUserTypeProfessional, model.UserUserTypeFacilityUser:
		var respByProfessional []FaqSearchRespByUser
		_ = copier.Copy(&respByProfessional, resp)
		return respByProfessional, nil
	default:
		return resp, nil
	}
}

type FaqEditReq struct {
	FaqId      uint64 `json:"faqId"  binding:"required"`                                // 常見問題ID
	Category   string `json:"category" binding:"omitempty,oneof=PROFESSIONAL FACILITY"` // 分類 專業人士=PROFESSIONAL 機構=FACILITY
	Question   string `json:"question" binding:"required"`                              // 問題
	Answer     string `json:"answer" binding:"required"`                                // 答案
	AnswerText string `json:"answerText" binding:"required,max=255"`                    // 答案純文本
	Status     string `json:"status" binding:"omitempty,oneof=ENABLE DISABLE"`          // 狀態 啟用=ENABLE 禁用=DISABLE
}

func (s *faqService) Edit(db *gorm.DB, req FaqEditReq) error {
	var err error
	var m model.Faq
	if err = db.First(&m, req.FaqId).Error; err != nil {
		return err
	}
	_ = copier.Copy(&m, req)
	if err = db.Save(&m).Error; err != nil {
		return err
	}

	fileUuids := s.GetAnswerFileUuids(m.Answer)
	if len(fileUuids) > 0 {
		if err = FaqFileService.BindFaq(db, m.Id, fileUuids); err != nil {
			return err
		}
	}
	return nil
}

type FaqInquireReq struct {
	Category string `form:"-"` // 分類 專業人士=PROFESSIONAL 機構=FACILITY
	FaqId    uint64 `form:"faqId" binding:"required"`
}

type FaqInquireResp struct {
	FaqId      uint64 `json:"faqId"`      // 常見問題ID
	Category   string `json:"category"`   // 分類 專業人士=PROFESSIONAL 機構=FACILITY
	Question   string `json:"question"`   // 問題
	Answer     string `json:"answer"`     // 答案
	AnswerText string `json:"answerText"` // 答案純文本
	Seq        int32  `json:"seq"`        // 排序
	Status     string `json:"status"`     // 狀態 啟用=ENABLE 禁用=DISABLE
}

type FaqInquireRespByUser struct {
	FaqId    uint64 `json:"faqId"`    // 常見問題ID
	Question string `json:"question"` // 問題
	Answer   string `json:"answer"`   // 答案
	Seq      int32  `json:"seq"`      // 排序
}

func (s *faqService) Inquire(db *gorm.DB, req FaqInquireReq) (interface{}, error) {
	var err error
	var resp FaqInquireResp
	var m model.Faq
	if err = db.First(&m, req.FaqId).Error; err != nil {
		return resp, err
	}
	_ = copier.Copy(&resp, m)
	resp.FaqId = m.Id
	switch req.Category {
	case model.FaqCategoryProfessional, model.FaqCategoryFacility:
		var respByUser FaqInquireRespByUser
		_ = copier.Copy(&respByUser, resp)
		respByUser.Answer = s.FormatAnswer(db, respByUser.Answer, true)
		return respByUser, nil
	default:
		return resp, nil
	}
}

type FaqDeleteReq struct {
	FaqId uint64 `json:"faqId"  binding:"required"` // 常見問題ID
}

func (s *faqService) Delete(db *gorm.DB, req FaqDeleteReq) error {
	var err error
	if err = db.Delete(&model.Faq{}, req.FaqId).Error; err != nil {
		return err
	}
	return nil
}

func (s *faqService) FormatAnswer(db *gorm.DB, answer string, showImage bool) string {
	// 使用正則表達式匹配所有圖片 ID 佔位符
	re := regexp.MustCompile(FaqAnswerImageRegex)
	// 遍歷所有匹配的圖片 ID
	answer = re.ReplaceAllStringFunc(answer, func(match string) string {
		// 使用正則表達式的子匹配提取圖片 ID
		submatches := re.FindStringSubmatch(match)
		if len(submatches) > 1 {
			imageId := submatches[1]
			if showImage {
				url, err := FaqFileService.GenPreviewUrlByUuid(db, imageId)
				if err != nil {
					return ""
				}
				// 顯示圖片時，替換為圖片標籤
				return fmt.Sprintf("<img src='%s'>", url)
			} else {
				return ""
			}
		}
		return match
	})
	return answer
}
