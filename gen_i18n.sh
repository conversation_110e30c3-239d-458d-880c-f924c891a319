# 复制此文件到根目录
goi18n extract -outdir=resource/i18n
cd ./resource/i18n || exit
goi18n merge active.*.toml
goi18n merge active.*.toml translate.*.toml

# 檢查是否生成了新的翻譯文件
if [ -f "translate.zh-CN.toml" ] || [ -f "translate.zh-HK.toml" ]; then
    echo "檢測到新的翻譯內容，請更新翻譯後再提交。"
    
    # 清理生成的文件
    if [ -f "translate.zh-CN.toml" ]; then
        rm translate.zh-CN.toml
    fi

    if [ -f "translate.zh-HK.toml" ]; then
        rm translate.zh-HK.toml
    fi

    exit 1
fi