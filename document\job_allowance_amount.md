# 津貼金額計算

注意，每次計算應該將金額保留2位小數，避免計算精度問題

## 關鍵值
- 津貼列表 JobAllowance
- 津貼類型 JobAllowance.AllowanceType HOURLY, SHIFT, JOB
- 工作時長 JobShift.PayHours
- 津貼金額 JobAllowance.Amount 根據津貼類型不同分配到每個JobShift的金額不同
- 是否包含公務員津貼 JobAllowance.AttractsSuperannuation 如果等於Y，需要計算後的津貼金額加上12%的superannuation

## 按每小時津貼 （AllowanceType = HOURLY）
每個JobShift的津貼金額 = 每小時津貼 * 工作時長
jobShift.AllowanceAmount = jobAllowance.Amount * jobShift.PayHours
如果包含公務員津貼，需要加上12%的superannuation
jobShift.AllowanceAmount = jobAllowance.Amount * jobShift.PayHours * 1.12

## 按每班次津貼 （AllowanceType = SHIFT）
每個JobShift的津貼金額 = 每班次津貼 * 工作時長
jobShift.AllowanceAmount = jobAllowance.Amount
如果包含公務員津貼，需要加上12%的superannuation
jobShift.AllowanceAmount = jobAllowance.Amount * 1.12

## 按每職位津貼 （AllowanceType = JOB）
每個JobShift的津貼金額 = 每職位津貼
jobShift.AllowanceAmount = jobAllowance.Amount / len(jobShifts), 如因計算精度問題導致誤差，將誤差計入最後一個JobShift
如果包含公務員津貼，需要加上12%的superannuation
jobShift.AllowanceAmount =  jobAllowance.Amount / len(jobShifts) * 1.12
