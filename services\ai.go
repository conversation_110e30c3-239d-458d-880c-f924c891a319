package services

import (
	"errors"

	"github.com/Norray/xrocket/xgemini"
	"github.com/Norray/xrocket/xmodel"
	"gorm.io/gorm"
)

var AiService = new(aiService)

type aiService struct{}

type AiResp struct {
	Model       string `json:"model"`
	Output      string `json:"output"`
	InputToken  int32  `json:"inputToken"`
	OutputToken int32  `json:"outputToken"`
}

func (s *aiService) AiExpireDate(db *gorm.DB, filename string, data []byte) (AiResp, error) {
	//prompt, err := s.GetExpireDatePrompt(db)
	prompt := `Analyze the provided image and perform the following tasks:

1. Document Identification:
   - Identify all documents or certificates present in the image
   - Locate expiration information (either Expire Date or Expire Month)

2. Date Processing:
   - If an exact Expire Date is found (including day, month, and year):
     * Convert it to yyyy-mm-dd format
     * Store in JSON field "expireDate"
   - If only an Expire Month is found (month and year without specific day):
     * Convert it to yyyy-mm format
     * Store in JSON field "expireMonth"
   - If no expiration information is found:
     * Set both fields to empty strings

3. Output Requirements:
   - Use exactly these field names:
     * "expireDate" (for full dates)
     * "expireMonth" (for month-year only)
   - Format dates precisely:
     * yyyy-mm-dd for full dates (e.g., "2025-12-31")
     * yyyy-mm for months (e.g., "2025-12")
   - For missing data, use empty strings ("")

Example Output:
{"expireDate": "2024-06-15","expireMonth": ""}

or if only month is found:
{"expireDate": "","expireMonth": "2024-06"}

or if nothing found:
{"expireDate": "","expireMonth": ""}

Important Notes:
- Prioritize finding exact dates over month-only information
- If both exist in the document, use the exact date for "expireDate"
- Validate all dates to ensure they're logical (e.g., months 01-12)
- Handle various date formats (MM/DD/YYYY, DD-MM-YYYY, Month YYYY, etc.)
- Explicitly confirm when no expiration information is detectable
- Return only raw JSON. Do not include any Markdown formatting like ` + "```json or ``` blocks."
	//if err != nil {
	//	return AiResp{}, err
	//}
	if prompt == "" {
		return AiResp{}, errors.New("prompt is not set")
	}
	modelName := "gemini-2.0-flash-001"
	result, err := xgemini.GeminiChatCompletions(&xgemini.GeminiChatCompletionsReq{
		Prompt:          prompt,
		ModelName:       modelName,
		Temperature:     0.7,
		TopK:            1,
		TopP:            0,
		MaxOutputTokens: xgemini.GeminiDefaultMaxOutputTokens,
		File: &xgemini.GeminiFile{
			FileName: filename,
			MimeType: xgemini.GetFileMimeType(filename),
			Content:  data,
		},
	})
	if err != nil {
		return AiResp{}, err
	}
	return AiResp{
		Model:       modelName,
		Output:      xgemini.RemoveJSONMarkdown(result.Output),
		InputToken:  result.InputToken,
		OutputToken: result.OutputToken,
	}, nil
}

func (s *aiService) GetExpireDatePrompt(db *gorm.DB) (string, error) {
	var setting xmodel.CommonSetting
	if err := db.Where("code = ?", "EXPIRE_DATE_PROMPT").First(&setting).Error; err != nil {
		return "", err
	}
	return setting.SettingValue, nil
}
